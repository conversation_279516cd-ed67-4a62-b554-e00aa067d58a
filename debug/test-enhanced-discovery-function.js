/**
 * Test the enhanced getLiveDiscoveryTotal function with real data
 */

require('dotenv').config();

async function testEnhancedDiscoveryFunction() {
    console.log('🔧 Testing enhanced getLiveDiscoveryTotal function...');
    
    try {
        // Get sample data first
        const { optimizedFind } = require('../utils/database-optimizer.js');
        
        const sampleRecords = await optimizedFind("item_records", 
            { parameter: '_item_discovery' }, 
            { limit: 3, sort: { recordedAt: -1 } }
        );
        
        if (sampleRecords.length === 0) {
            console.log('❌ No discovery records found for testing');
            return false;
        }
        
        console.log('✅ Found sample records for testing:');
        sampleRecords.forEach((record, i) => {
            console.log(`  ${i + 1}. ${record.itemName} (${record.itemType}) - ${record.scope} - ${record.guildId || 'global'}`);
        });
        
        // Test the function by recreating its logic with enhanced logging
        console.log('\n=== Testing Function Logic ===');
        
        for (const record of sampleRecords) {
            console.log(`\n🔍 Testing with: ${record.itemName} (${record.itemType})`);
            
            const itemName = record.itemName;
            const itemType = record.itemType;
            const guildId = record.scope === 'guild' ? record.guildId : null;
            const fallbackTotal = 1;
            
            console.log(`Parameters: itemName="${itemName}", itemType="${itemType}", guildId="${guildId}"`);
            
            // Recreate the function logic
            const startTime = Date.now();
            
            try {
                // Input validation
                if (!itemName || !itemType) {
                    console.log('❌ Input validation failed');
                    continue;
                }
                
                // Cache key generation
                const cacheKey = `discovery_${itemName}_${itemType}_${guildId || 'global'}`;
                console.log(`Cache key: ${cacheKey}`);
                
                // Query construction
                const scope = guildId ? 'guild' : 'global';
                const query = {
                    scope: scope,
                    itemName: itemName,
                    itemType: itemType,
                    parameter: '_item_discovery'
                };
                
                if (guildId) {
                    query.guildId = guildId;
                }
                
                console.log('Query:', query);
                
                // Execute query
                const { optimizedCountDocuments } = require('../utils/database-optimizer.js');
                const count = await optimizedCountDocuments("item_records", query);
                
                const duration = Date.now() - startTime;
                console.log(`✅ Query result: ${count} records (${duration}ms)`);
                
                if (count === 0) {
                    console.log('❌ Query returned 0 - this would cause the "1st/0 server" issue');
                    console.log('   Fallback would return:', fallbackTotal);
                } else {
                    console.log('✅ Query returned valid count');
                }
                
            } catch (error) {
                const duration = Date.now() - startTime;
                console.log(`❌ Error occurred (${duration}ms):`, error.message);
                console.log('   Fallback would return:', fallbackTotal);
            }
        }
        
        console.log('\n=== Testing Cache Functionality ===');
        
        // Test the LRU cache
        const { CacheFactory } = require('../utils/LRUCache.js');
        const testCache = CacheFactory.createHighFrequencyCache();
        
        // Test cache operations
        const testKey = 'discovery_test_item_OTHER_global';
        const testValue = { count: 5, timestamp: Date.now() };
        
        testCache.set(testKey, testValue);
        const cached = testCache.get(testKey);
        
        if (cached && cached.count === 5) {
            console.log('✅ LRU cache working correctly');
        } else {
            console.log('❌ LRU cache issue detected');
        }
        
        console.log('\n=== Testing Real Function Call ===');
        
        // Since getLiveDiscoveryTotal is not exported, let's test by calling the items module
        // and checking if it loads without errors
        try {
            const itemsModule = require('../commands/utility/items.js');
            console.log('✅ Items module with enhanced function loaded successfully');
            
            // The enhanced logging should show up in console when the function is actually called
            console.log('ℹ️  Enhanced logging is now active - check console output when items are found');
            
        } catch (error) {
            console.log('❌ Items module loading failed:', error.message);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error during enhanced function test:', error);
        return false;
    }
}

// Run the test
if (require.main === module) {
    testEnhancedDiscoveryFunction().then(success => {
        if (success) {
            console.log('\n🏁 Enhanced function test completed');
            console.log('💡 The enhanced logging will show detailed information when items are actually found');
            process.exit(0);
        } else {
            console.log('\n💥 Test failed');
            process.exit(1);
        }
    }).catch(error => {
        console.error('💥 Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { testEnhancedDiscoveryFunction };
