# Fresh Session Context - January 5, 2025

## Project Overview
Discord bot built with Discord.js v14 using Components v2 architecture. Features custom item system, leveling, starfall rewards, and comprehensive notification infrastructure.

## Recent Major Fixes & Implementations

### ✅ Image Upload System (`utils/imageUploader.js`)
**FIXED: Image selection bug showing only old images from 2022**
- **24-hour filter**: Only shows images from last 24 hours (no more "old ass images")
- **Optimized search**: Limited to 10 messages for item creation scenarios
- **Cache management**: Automatic refresh after uploads and emote selection
- **Working perfectly**: Tested in "things to look at" channel, finds recent images correctly
- **Performance**: Single API call, instant results

### ✅ Starfall System (`utils/starfall.js`)
**COMPLETE: Full infrastructure integration**
- **DM notifications**: Uses same infrastructure as regular item drops
- **Notification queue**: Proper integration with `/you → notifications`
- **Drop location**: Tracked as "starfall" in all systems
- **Owner testing**: `ownerTestingMode = false` (currently disabled)
- **Cleaned up**: Removed duplicate testing code from owner.js

### ✅ Level-Up Integration
**FIXED: Level-up items now use full infrastructure**
- **DM notifications**: Automatic notifications for level-up item rewards
- **Notification queue**: Same experience as regular drops
- **Cache integration**: Proper refresh and display
- **Guild level display**: Fixed "1/5 instead of 0/5" bug in `/you` command

## Key Technical Context

### Architecture
- **Components v2**: All interfaces use ContainerBuilder, TextDisplayBuilder, etc.
- **No 'content' field**: When using MessageFlags.IsComponentsV2, only use components
- **MongoDB**: Database with collections for items, users, guilds, notifications
- **Modular design**: Shared utilities in `/utils/` directory

### User Preferences
- **Single-page interfaces**: Cascading select menus instead of multiple pages
- **Status messages**: Use `**status:** [message]` in containers, not ephemeral replies
- **Hide unavailable options**: Don't show disabled options (except admin toggles)
- **Package managers**: Always use npm/yarn instead of manual package.json edits
- **Comprehensive testing**: Real Discord interactions with bot login and database

### Testing Environment
- **Test guild**: GUILDIDTWO = "417175807795134475"
- **Test channel**: "things to look at" channel for image uploads
- **Owner testing**: Use OWNER env variable for testing owner-only features

## Current System Status

### Working Systems ✅
- **Image upload visibility**: 24-hour filter working perfectly in real channels
- **Starfall integration**: Complete infrastructure integration with DM notifications
- **Level-up items**: Full integration with notification and inventory systems
- **Guild level display**: Fixed off-by-one error in `/you` command
- **Cache management**: Proper invalidation and refresh after uploads

### Key Files & Functions
- `utils/imageUploader.js`: Shared image upload utility (24-hour filter, 10 message limit)
- `utils/starfall.js`: Complete starfall system with infrastructure integration
- `utils/globalLevels.js`: Global leveling with prestige system
- `commands/utility/items.js`: Item creation/editing interface
- `commands/utility/you.js`: User profile and inventory display

### Development Patterns

#### Error Handling
```javascript
// Use status messages in containers, not ephemeral replies
const statusDisplay = new TextDisplayBuilder().setContent('**status:** error message');
container.addTextDisplayComponents(statusDisplay);
```

#### Image Upload Integration
```javascript
// Always refresh cache after uploads
const { getRecentImagesFromChannel } = require('../../utils/imageUploader.js');
await getRecentImagesFromChannel(interaction, { limit: 8, cacheMinutes: 2 });
```

## Important Notes
- **Database required**: Bot always requires MongoDB connection, no fallback logic
- **Owner permissions**: Server owners have full access regardless of specific permissions
- **Image search**: Limited to 10 messages for performance during item creation
- **Time filtering**: All image searches use 24-hour filter to avoid old irrelevant images
- **Modular architecture**: Shared utilities eliminate code duplication

## Recent Test Results
All major systems tested and working:
- Image upload visibility: 100% success rate
- Starfall integration: Complete infrastructure integration
- Level-up items: Full notification and inventory integration
- Guild level display: Fixed off-by-one error

## Next Steps Guidance
When working on this project:
1. Use existing shared utilities instead of duplicating code
2. Follow Components v2 patterns consistently
3. Test thoroughly with real Discord interactions
4. Maintain the 24-hour image filter and performance optimizations
5. Use status messages in containers for better UX consistency

All major systems are integrated and working correctly. The bot has comprehensive infrastructure for item drops, notifications, and user interactions.
