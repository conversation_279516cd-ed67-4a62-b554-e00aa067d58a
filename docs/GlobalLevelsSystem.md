# Global Levels System Documentation

## Overview

The Global Levels System is a server-agnostic progression system that allows users to gain XP and level up across all servers where the bot is present. Unlike guild-specific levels, global levels persist across all servers and provide permanent rewards including XP boosters, drop chance boosters, and items.

## Key Features

- **Cross-Server Progression**: XP gained in any server contributes to global level progression
- **Prestige System**: Reset progression with increased XP requirements for ultimate rewards
- **Permanent Boosters**: XP and drop chance multipliers that persist through prestige
- **Global Inventory**: Items earned from global levels are accessible across all servers
- **Owner-Configurable**: Bot owners can create custom global levels with rewards

## Architecture

### Database Collections

#### `global_user_data`
Stores individual user's global progression data:
```javascript
{
  userId: "string",
  globalExp: NumberLong(0),
  globalLevel: Number(0),
  prestigeLevel: Number(0),
  prestigeMultiplier: Number(1.0),
  boosters: {
    expMultiplier: Decimal128("1.0"),
    dropChanceMultiplier: Decimal128("1.0")
  },
  lastActivity: Date,
  createdAt: Date,
  updatedAt: Date
}
```

#### `global_levels`
Stores owner-configured global level definitions:
```javascript
{
  level: Number,
  name: "string",
  expRequired: NumberLong,
  levelIcon: "string",
  prestigeIcon: "string",
  rewards: {
    items: ["string"],          // Array of item IDs from LEVEL_UP drop location
    xpBooster: Number,          // Optional XP multiplier (e.g., 1.1, 1.5) - permanent
    dropBooster: Number         // Optional drop chance multiplier (e.g., 1.2, 2.0) - permanent
  },
  createdAt: Date,
  isActive: Boolean(true)
}
```

#### `global_level_notifications`
Stores global level-up notifications for the notification center:
```javascript
{
  userId: "string",
  levelUpData: Object,
  createdAt: Date,
  viewed: Boolean(false)
}
```

### Performance Optimizations

#### Caching Strategy
- **Global Levels Cache**: 10-minute TTL for level configurations
- **Global User Cache**: 2-minute TTL for user data
- **Global Rankings Cache**: 1-minute TTL for leaderboards

#### Database Indexes
```javascript
// Global user data indexes
{ userId: 1 }                           // Unique user lookup
{ globalExp: -1 }                       // Rankings
{ globalLevel: -1, globalExp: -1 }      // Level-based queries
{ prestigeLevel: -1, globalExp: -1 }    // Prestige queries

// Global levels indexes
{ level: 1 }                            // Unique level lookup
{ expRequired: 1 }                      // XP requirement queries
{ isActive: 1, level: 1 }               // Active levels
```

## Core Systems

### XP Awarding System

Global XP is awarded alongside guild XP for all activities:

```javascript
// Text XP (messageCreate.js)
const globalResult = await awardGlobalExp(userId, expAmount, 'TEXT');

// Voice XP (voiceStateUpdate.js)
const globalResult = await awardGlobalExp(userId, expAmount, 'VOICE');
```

#### Booster Application
XP boosters are applied automatically when awarding XP:
```javascript
const expMultiplier = parseFloat(userData.boosters.expMultiplier.toString());
const boostedExp = Math.floor(expAmount * expMultiplier);
```

### Level Progression

Level calculation considers prestige multipliers:
```javascript
function calculateGlobalLevel(globalExp, prestigeLevel, levels) {
  const prestigeMultiplier = prestigeLevel > 0 ? (prestigeLevel + 1) : 1;
  
  // Find current level based on adjusted XP requirements
  for (let i = levels.length - 1; i >= 0; i--) {
    const adjustedExpRequired = levels[i].expRequired * prestigeMultiplier;
    if (globalExp >= adjustedExpRequired) {
      return { currentLevel: i + 1, canPrestige: i === levels.length - 1 };
    }
  }
}
```

### Prestige System

#### Prestige Mechanics
- Available when user reaches maximum global level
- Resets global XP and level to 0
- Increases XP requirements by 2x, 3x, 4x, 5x, 6x for each prestige
- Preserves all items and boosters

#### Prestige Confirmation
3-click confirmation system prevents accidental prestige:
```javascript
// Click tracking with 30-second timeout
const prestigeConfirmationClicks = new Map();
const CONFIRMATION_TIMEOUT = 30 * 1000;
```

### Booster System

#### XP Boosters
- Permanent multipliers applied to all XP gains
- Decimal precision using MongoDB Decimal128
- Additive stacking (1.0 + 0.1 + 0.2 = 1.3x)

#### Drop Chance Boosters
- Reduces "nothing" weight in drop calculations
- Applied in `performMasterRoll()` function:
```javascript
const adjustedNothingWeight = Math.max(1, Math.floor(NOTHING_WEIGHT / dropChanceMultiplier));
```

## User Interface

### /you Command Restructure

#### Global View (Default)
- Shows global XP, level, and prestige status
- Displays global inventory preview
- Global stats and booster information

#### Server View (Submenu)
- Accessible via "You in <ServerName>" option
- Shows server-specific XP, level, and role
- Server-specific inventory

### Notification System

#### Notification Center
- Global level-up notifications appear first (higher priority)
- Shows XP source, rewards earned, and prestige availability
- Dismissible with button interaction

#### DM Notifications
- Separate toggle from server notifications
- Configurable in /you settings
- Includes all level-up details and rewards

### Settings Integration

New global level DM notification toggle in /you settings:
```javascript
{
  label: 'enable/disable global level dm notifications',
  value: 'toggle-global-level-dm',
  emoji: '🌟'
}
```

## Owner Management

### /globallevels Command

Owner-only command for managing global levels:

#### Features
- Create new global levels with cascading select menus
- Set level names, XP requirements, icons, and rewards
- View all existing levels
- Database index management

#### Level Creation Flow
1. **Set Name**: Custom level name via modal input
2. **Set XP**: Select from generated exponential options
3. **Set Icon**: Custom emoji/icon for level display
4. **Set Prestige Icon**: Icon shown for prestiged users
5. **Configure Rewards**: Items, XP boosters, drop boosters
6. **Confirm**: Review and create level

#### UI Design Standards
- **Color System**: Uses centralized colors from `utils/colors.js`
  - `OPERATION_COLORS.ADD` for creation operations
  - `OPERATION_COLORS.EDIT` for editing operations
  - `LOG_COLORS.SUCCESS` for successful operations
- **Container Design**: Modern Components v2 with consistent theming
- **Error Handling**: Status messages in containers, not ephemeral replies

## Integration Points

### Item Drop System
- Global drop chance boosters applied in `performMasterRoll()`
- Global level rewards added to global inventory
- Items marked with `guildId: null` for global items

### Inventory System
- Global inventory accessible across all servers
- Separate from guild-specific inventories
- Displayed in global /you view

### Notification System
- Global level notifications integrated into existing queue
- Higher priority than item notifications
- Separate DM preferences

## Testing

Comprehensive test suite in `tests/globalLevelsTest.js`:

### Test Coverage
- Global XP progression and level-ups
- Booster application and stacking
- Prestige system mechanics
- Drop chance booster effectiveness
- Database operations and caching

### Running Tests
```bash
node runGlobalLevelsTests.js
```

## Performance Considerations

### Optimizations Implemented
- Aggressive caching with appropriate TTLs
- Efficient database indexes for common queries
- Parallel processing of rewards and notifications
- Lazy loading of server-specific data

### Monitoring
- Cache hit/miss statistics available
- Performance logging for slow operations
- Error handling with fallback mechanisms

## Future Enhancements

### Potential Features
- Multiple prestige tiers beyond 6x
- Seasonal global events and bonuses
- Global leaderboards with rewards
- Achievement system integration
- Custom prestige icons per tier

### Scalability Considerations
- Sharding strategies for large user bases
- Background processing for heavy operations
- Rate limiting for XP awarding
- Archive old notification data

## Troubleshooting

### Common Issues
1. **Cache Inconsistency**: Use `invalidateAllCaches()` function
2. **Missing Indexes**: Run `/globallevels` → "create indexes"
3. **Booster Not Applied**: Check Decimal128 conversion
4. **Prestige Stuck**: Clear confirmation clicks manually

### Debug Commands
```javascript
// Check cache status
const { getCacheStats } = require('./utils/globalLevels.js');
console.log(getCacheStats());

// Force cache refresh
const { invalidateAllCaches } = require('./utils/globalLevels.js');
invalidateAllCaches();
```

## Migration Notes

Since the bot isn't released yet, no migration is needed. For future updates:
- Always backup database before schema changes
- Test migrations on staging environment
- Consider gradual rollout for major changes
- Maintain backward compatibility where possible
