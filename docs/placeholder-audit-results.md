# Placeholder vs Select Menu Text Audit Results

## Overview
This audit reviews inconsistencies between select menu option labels and their corresponding placeholder text across all bot features.

## Audit Findings

### ✅ CONSISTENT - No Issues Found

#### featuresMenu.js
- **Placeholder**: `'features'`
- **Options**: `'owner'`, `'opener'`, `'logs'`, `'dehoist'`, `'exp'`, `'items'`, `'sticky'`, `'17'`
- **Status**: ✅ Generic placeholder appropriately covers all feature options

#### changelog.js
- **Placeholder**: `version ${selected.version}` (dynamic)
- **Options**: `version ${c.version}` (matches format)
- **Status**: ✅ Placeholder matches option label format exactly

#### status.js
- **Placeholder**: `'status messages'`
- **Options**: Status message content with emojis
- **Status**: ✅ Generic placeholder appropriately describes content

#### logs.js
- **Placeholder**: `'select specialty events to log'`
- **Options**: Event names (e.g., 'expLevelUp', 'itemCreated')
- **Status**: ✅ Descriptive placeholder explains purpose

#### opener.js
- **Placeholder**: `'channel(s)'`
- **Options**: Channel selection (ChannelSelectMenuBuilder)
- **Status**: ✅ Placeholder matches expected input type

### ⚠️ MINOR INCONSISTENCIES - Acceptable

#### you.js
- **Placeholder**: `currentFeature ? currentFeature.replace('-', ' ') : 'exp'`
- **Options**: `'voice exp stats'`, `'text exp stats'`, `'inventory'`, `'main'`
- **Analysis**: Dynamic placeholder shows current feature, which is logical
- **Status**: ⚠️ Acceptable - shows current state rather than available options

#### exp.js - Subcomponent Menu
- **Placeholder**: `subcomponent` (current selection)
- **Options**: `'levels'`, `'text'`, `'voice'`
- **Analysis**: Shows current subcomponent, not available options
- **Status**: ⚠️ Acceptable - follows pattern of showing current state

### 🔧 INCONSISTENCIES REQUIRING FIXES

#### items.js - Type Selection
- **Placeholder**: `'select item type'`
- **Options**: Type names like `'Weapon'`, `'Armor'`, `'Consumable'`, etc.
- **Issue**: Placeholder is instructional, options are nouns
- **Recommendation**: Change to `'item type'` for consistency

#### items.js - Parameter Selection
- **Placeholder**: `select ${paramName}` (e.g., `'select weight'`)
- **Options**: Parameter values
- **Issue**: Inconsistent with other parameter placeholders
- **Recommendation**: Change to just `paramName` (e.g., `'weight'`)

#### items.js - Rarity Selection
- **Placeholder**: `'select rarity'`
- **Options**: `'Common'`, `'Rare'`, `'Mythical'`, etc.
- **Issue**: Placeholder is instructional, options are nouns
- **Recommendation**: Change to `'rarity'` for consistency

#### items.js - Drop Locations
- **Placeholder**: `'select drop locations'`
- **Options**: `'Text EXP'`, `'Voice EXP'`, etc.
- **Issue**: Placeholder is instructional, options are nouns
- **Recommendation**: Change to `'drop locations'` for consistency

#### items.js - Image Selection
- **Placeholder**: `'select image for emote'`
- **Options**: Image filenames
- **Issue**: Overly descriptive placeholder
- **Recommendation**: Change to `'recent images'` for consistency

#### exp.js - Role Selection
- **Placeholder**: `'select role'`
- **Options**: Role names
- **Issue**: Placeholder is instructional, should be noun
- **Recommendation**: Change to `'role'` for consistency

#### exp.js - EXP Value Selection
- **Placeholder**: `'select exp value'`
- **Options**: EXP amounts
- **Issue**: Placeholder is instructional, should be noun
- **Recommendation**: Change to `'exp value'` for consistency

#### exp.js - Channel Selection
- **Placeholder**: `'channel'`
- **Options**: Channel selection
- **Status**: ✅ Already consistent

#### exp.js - Config Menus
- **Placeholder**: `'config'`
- **Options**: Configuration option names
- **Status**: ✅ Already consistent

## Recommended Pattern

### Consistent Placeholder Style
- **Use nouns, not instructions**: `'role'` instead of `'select role'`
- **Be concise**: `'item type'` instead of `'select item type'`
- **Match option category**: If options are role names, placeholder should be `'role'`

### Examples of Good Patterns
```javascript
// ✅ Good - noun placeholder
.setPlaceholder('features')
.addOptions([
  { label: 'owner', value: '1' },
  { label: 'logs', value: 'logs' }
])

// ✅ Good - category placeholder
.setPlaceholder('rarity')
.addOptions([
  { label: 'Common', value: 'COMMON' },
  { label: 'Rare', value: 'RARE' }
])
```

### Examples of Patterns to Avoid
```javascript
// ❌ Avoid - instructional placeholder
.setPlaceholder('select item type')
.addOptions([
  { label: 'Weapon', value: 'WEAPON' },
  { label: 'Armor', value: 'ARMOR' }
])
```

## Implementation Priority

### High Priority (User-Facing)
1. items.js - All select menus (most visible to users)
2. exp.js - Role and EXP value selection (frequently used)

### Medium Priority
3. Parameter selection menus (less frequently used)
4. Image selection menus (advanced feature)

### Low Priority
5. Dynamic placeholders that show current state (acceptable as-is)

## Files Requiring Updates
1. `commands/utility/items.js` - Multiple select menus
2. `commands/utility/exp.js` - Role and EXP selection menus

Total select menus requiring updates: ~8-10 instances
