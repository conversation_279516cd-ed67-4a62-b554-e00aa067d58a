# 🤝 Contributing to Discord Bot Project

Welcome to the Discord bot project! This guide will help you understand our development standards, workflows, and best practices.

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Development Standards](#development-standards)
3. [Color System Guidelines](#color-system-guidelines)
4. [Testing Requirements](#testing-requirements)
5. [Code Review Process](#code-review-process)
6. [Project Architecture](#project-architecture)

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ 
- MongoDB database
- Discord bot token
- Basic understanding of Discord.js v14

### Environment Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Create `.env` file with required variables:
   ```env
   TOKEN=your_discord_bot_token
   MONGO=your_mongodb_connection_string
   OWNER=your_discord_user_id
   GUILDIDTWO=417175807795134475
   ```
4. Run the bot: `node index.js`

### Development Environment
- **Test Guild**: Use `GUILDIDTWO` (417175807795134475) for testing
- **Owner Testing**: Add your Discord ID to `OWNER` environment variable
- **Database**: Use development/testing database, never production

## 📏 Development Standards

### Code Style
- **ES6+ Syntax**: Use modern JavaScript features
- **Consistent Naming**: Use camelCase for variables, PascalCase for classes
- **File Organization**: Group related functionality in feature-specific files
- **Comments**: Use JSDoc for function documentation

### Import Patterns
```javascript
// Discord.js imports first
const { ContainerBuilder, SectionBuilder, TextDisplayBuilder } = require('discord.js');

// Local utilities second
const { mongoClient } = require('../../mongo/client.js');
const { OPERATION_COLORS, LOG_COLORS } = require('../../utils/colors.js');

// Feature-specific imports last
const { buildSelectMenu } = require('./featuresMenu.js');
```

### Error Handling
- **Container Status Messages**: Use status messages in containers instead of ephemeral replies
- **Graceful Degradation**: Provide fallbacks for failed operations
- **Logging**: Use appropriate log levels and structured logging

### Package Management
- **Always use package managers**: `npm install`, `npm uninstall`
- **Never edit package files manually**: Let package managers handle dependencies
- **Update lock files**: Commit `package-lock.json` changes

## 🎨 Color System Guidelines

### Mandatory Color System Usage
All UI elements **must** use the centralized color system from `utils/colors.js`:

```javascript
const { OPERATION_COLORS, LOG_COLORS } = require('../../utils/colors.js');

// ✅ Correct usage
container.setAccentColor(OPERATION_COLORS.ADD);

// ❌ Never do this
container.setAccentColor(0x69FF69);
```

### Color Categories
- **OPERATION_COLORS**: UI operations (ADD, EDIT, DELETE, NEUTRAL, ENTITY)
- **LOG_COLORS**: Status and feedback (SUCCESS, ERROR, WARNING, INFO, DISABLED, PREMIUM)
- **RARITY_COLORS**: Item system rarities only
- **LEGACY_COLORS**: Backwards compatibility

### Color Selection Guidelines
1. **Semantic Selection**: Choose colors based on context and meaning
2. **Consistency**: Use the same colors for similar operations across features
3. **Fallbacks**: Always provide fallback colors for dynamic content
4. **Testing**: Verify color consistency across all UI states

For complete color system documentation, see [Color System Guide](COLOR_SYSTEM_GUIDE.md).

## 🧪 Testing Requirements

### Comprehensive Testing Standard
All features **must** include comprehensive tests that:

- **Real Discord Integration**: Test with actual Discord client connections
- **Database Operations**: Test with real database connections and data
- **Complete Workflows**: Test entire user journeys, not just individual functions
- **Edge Cases**: Include error conditions, empty states, and boundary conditions
- **Components v2 Compliance**: Validate Discord API constraints

### Test File Structure
```javascript
/**
 * COMPREHENSIVE TEST FOR [FEATURE NAME]
 * Tests all interactions, edge cases, and integration points
 */

const { Client, GatewayIntentBits } = require('discord.js');
const { mongoClient } = require('../mongo/client.js');

class FeatureComprehensiveTest {
    constructor() {
        this.client = new Client({
            intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages]
        });
        this.results = {};
    }

    async runAllTests() {
        // Test implementation
    }
}
```

### Running Tests
```bash
# Run all comprehensive tests
npm test

# Run specific feature tests
node tests/test_[feature]_system_comprehensive.js

# Performance testing
node tests/test_performance_optimizations.js
```

### Test Documentation
- Keep test files as reference for future development
- Document test scenarios and expected outcomes
- Include performance benchmarks where applicable

## 🔍 Code Review Process

### Before Submitting
- [ ] All tests pass with 100% success rate
- [ ] Color system compliance verified (no hardcoded hex values)
- [ ] Performance impact assessed
- [ ] Documentation updated if needed
- [ ] Error handling implemented
- [ ] Edge cases considered

### Review Checklist
- [ ] **Color System**: No hardcoded colors, appropriate color categories used
- [ ] **Testing**: Comprehensive tests included and passing
- [ ] **Performance**: No obvious performance regressions
- [ ] **Architecture**: Follows established patterns and conventions
- [ ] **Error Handling**: Graceful error handling with user-friendly messages
- [ ] **Documentation**: Code is well-documented and self-explanatory

### Code Quality Standards
- **Modularity**: Break large files into focused, reusable components
- **DRY Principle**: Eliminate duplicate code through shared utilities
- **Single Responsibility**: Each function/class should have one clear purpose
- **Performance**: Consider memory usage and database query optimization

## 🏗️ Project Architecture

### File Organization
```
commands/utility/           # Main command implementations
├── [feature].js           # Primary feature file
├── [feature]-handlers.js  # Interaction handlers (if needed)
└── featuresMenu.js        # Shared UI components

utils/                     # Shared utilities
├── colors.js             # Color system (mandatory usage)
├── LRUCache.js           # Performance caching
├── imageUploader.js      # Shared image utilities
└── [feature]Cache.js     # Feature-specific caching

tests/                     # Comprehensive testing
├── test_[feature]_system_comprehensive.js
└── test_performance_optimizations.js

docs/                      # Documentation
├── COLOR_SYSTEM_GUIDE.md  # Color system documentation
├── CONTRIBUTING.md        # This file
└── comprehensive-testing-guide.md
```

### Design Patterns
- **Components v2**: Use modern Discord UI components
- **Container-Based UI**: Build interfaces with ContainerBuilder
- **Shared Utilities**: Create reusable functions for common operations
- **Cache Factory Pattern**: Use LRU caches for performance optimization
- **Status Message Pattern**: Use container status messages instead of ephemeral replies

### Database Patterns
- **Atomic Operations**: Use `findOneAndUpdate` with upsert for single-operation modifications
- **Aggregation Pipelines**: Use `$facet` for multiple statistics in single query
- **Indexing**: Create appropriate indexes for query performance
- **Connection Pooling**: Leverage existing MongoDB connection pooling

## 🚀 Performance Guidelines

### Database Optimization
- **Atomic Operations**: Replace sequential read-modify-write patterns
- **Aggregation Pipelines**: Use single queries instead of multiple operations
- **Proper Indexing**: Ensure queries have appropriate indexes
- **Connection Reuse**: Use existing connection pooling

### Memory Management
- **LRU Caching**: Use `utils/LRUCache.js` for memory-bounded caching
- **Cache Cleanup**: Implement automatic cache eviction and cleanup
- **Memory Monitoring**: Track memory usage in performance-critical operations

### Performance Testing
- Include performance benchmarks in comprehensive tests
- Monitor operation timing and memory usage
- Document performance improvements and regressions

## 📚 Additional Resources

- [Color System Guide](COLOR_SYSTEM_GUIDE.md) - Complete color system documentation
- [Testing Guide](comprehensive-testing-guide.md) - Detailed testing methodologies
- [Fresh Session Instructions](fresh-conversation-instructions.md) - Getting started guide
- [Performance Analysis](PERFORMANCE_OPTIMIZATION_RESULTS_2025-07-06.md) - Performance optimization results

## 🤝 Getting Help

- **Documentation**: Check existing documentation first
- **Test Examples**: Look at existing comprehensive test files for patterns
- **Code Examples**: Review similar features for implementation patterns
- **Architecture Questions**: Refer to established patterns in the codebase

## 📝 Commit Guidelines

### Commit Message Format
```
type(scope): description

- feat: new feature
- fix: bug fix
- docs: documentation changes
- style: formatting, missing semicolons, etc.
- refactor: code restructuring
- test: adding or updating tests
- perf: performance improvements
```

### Examples
```
feat(items): add rarity-based color system integration
fix(exp): resolve level creation atomic operation issue
docs(colors): add comprehensive color system guide
test(starfall): add comprehensive test coverage
perf(cache): implement LRU cache system for memory optimization
```

---

**Last Updated**: 2025-07-06  
**Version**: 1.0.0  
**Status**: Production Ready ✅

Thank you for contributing to this project! 🎉
