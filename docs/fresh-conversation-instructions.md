# Fresh Conversation Instructions

## Project Overview
This is a Discord bot project with comprehensive features including:
- Experience/leveling system (guild and global levels)
- Item management and inventory system
- Starfall rewards system
- Owner-only administrative commands
- Modular command structure

## Key Technical Details

### Bot Structure
- **Main Commands**: `/17` (main bot interface), `/you` (user profile), `/owner` (admin panel)
- **Database**: MongoDB with collections for users, guilds, items, levels, etc.
- **Architecture**: Modular design with separate files for different features

### Important File Locations
- **Main Commands**: `commands/utility/` directory
- **Utilities**: `utils/` directory for shared functions
  - `utils/colors.js` - Centralized color system (mandatory usage)
  - `utils/LRUCache.js` - Performance caching system
  - `utils/imageUploader.js` - Shared image upload utilities
- **Owner Features**: `commands/utility/owner-*.js` files
- **Documentation**: `docs/` directory with comprehensive guides
- **Environment**: Uses `.env` file for configuration

### Recent Major Work Completed
1. **Color System Standardization**: Achieved 100% compliance with centralized color system across entire codebase
2. **Performance Optimization**: 87% improvement in database operations through LRU caching and atomic operations
3. **Comprehensive Testing**: Real Discord API testing with 95%+ success rates across all major features
4. **Global Levels Editing System**: Fixed select menu default values, name editing, update buttons, and stars reward saving
5. **UI Modernization**: Updated `/you` command to use modern quote-based display format instead of progress bars
6. **Modular Refactoring**: Broke down large files into focused, reusable components

### Key Preferences & Standards
- **UI Style**: Single-page interfaces with cascading select menus, modern quote format for displays
- **Color System**: Mandatory use of centralized color system from `utils/colors.js` - no hardcoded hex values
- **Error Handling**: Use status messages in containers instead of ephemeral replies
- **Testing**: Comprehensive Discord-connected tests with real bot login and database connections
- **Package Management**: Always use package managers (npm, etc.) instead of manual file editing
- **Code Quality**: Modular design, shared utilities, consistent formatting
- **Performance**: LRU caching system and database optimization patterns

### Environment Variables
- `OWNER`: Bot owner Discord ID for admin access
- `GUILDIDTWO`: Test guild ID (417175807795134475)
- `TOKEN`: Discord bot token
- MongoDB connection details

### Testing Approach
- Use existing comprehensive test files that connect to Discord and database
- Test guild ID: 417175807795134475
- Owner-only testing variables preferred over global testing flags
- Keep test files as reference for future use

## Getting Started in New Conversation
When starting fresh, mention:
1. What specific feature or issue you want to work on
2. Whether you need an overview of any particular system
3. If you're continuing work on something specific or starting something new

The codebase is well-documented and modular, so the AI can quickly understand any area you want to focus on.
