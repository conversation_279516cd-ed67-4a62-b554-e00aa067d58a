const { MongoClient } = require('mongodb');

// Import LRU cache utilities for performance correlation
let getGlobalCacheStats;
try {
    ({ getGlobalCacheStats } = require('../utils/LRUCache.js'));
} catch (error) {
    // Fallback if LRU cache not available during initialization
    getGlobalCacheStats = () => ({ totalHits: 0, totalMisses: 1, cacheCount: 0 });
}

// Advanced MongoDB connection with optimized pooling and performance monitoring
const mongoClient = new MongoClient(process.env.MONGO, {
    // Enhanced connection pool settings for high-performance applications
    maxPoolSize: 100,       // Increased maximum connections for high concurrency
    minPoolSize: 10,        // Higher minimum to reduce connection creation overhead
    maxIdleTimeMS: 60000,   // Longer idle time for better connection reuse
    maxConnecting: 5,       // Limit concurrent connection attempts
    serverSelectionTimeoutMS: 15000, // Increased for replica set stability
    socketTimeoutMS: 30000, // Balanced for responsiveness vs reliability
    connectTimeoutMS: 15000, // Increased for initial connection stability
    heartbeatFrequencyMS: 30000, // Reduced frequency to match health checks
    waitQueueTimeoutMS: 10000, // Prevent indefinite waiting for connections

    // Performance optimizations
    retryWrites: true,      // Automatically retry write operations
    retryReads: true,       // Automatically retry read operations
    compressors: ['zlib'],  // Enable compression to reduce network traffic
    zlibCompressionLevel: 6, // Balanced compression level

    // Advanced settings
    readPreference: 'primaryPreferred', // Prefer primary but allow secondary reads
    readConcern: { level: 'local' },    // Local read concern for better performance
    writeConcern: { w: 1, j: false }, // Optimized for Discord bot performance (non-critical writes)

    // Monitoring and logging (disabled for production performance)
    monitorCommands: false,
});

mongoClient.clientConnected = false;

// Connection monitoring and health tracking
const connectionMetrics = {
    totalConnections: 0,
    activeConnections: 0,
    connectionErrors: 0,
    lastHealthCheck: null,
    poolStats: {
        created: 0,
        closed: 0,
        inUse: 0,
        available: 0
    }
};

const dbName = "test";

async function listDatabases(client) {
    try {
        const databasesList = await client.db().admin().listDatabases();
        const collections = await client.db(dbName).command({
            listCollections: 1,
        });

        console.log(`[mongo] Connected to database with ${databasesList.databases.length} databases`);
        return { databases: databasesList.databases, collections };
    } catch (error) {
        console.error('[mongo] Error listing databases:', error);
        throw error;
    }
}

/**
 * Helper function to create index with error handling
 */
async function createIndexSafely(collection, indexSpec, options, description) {
    try {
        await collection.createIndex(indexSpec, options);
        console.log(`[mongo] ✅ Created ${description} index`);
    } catch (error) {
        if (error.code === 85) {
            console.log(`[mongo] ℹ️  ${description} index already exists`);
        } else {
            console.error(`[mongo] ❌ Error creating ${description} index:`, error.message);
            throw error;
        }
    }
}

/**
 * Initialize database indexes for optimal performance
 * This should be called once during bot startup
 */
async function initializeIndexes() {
    try {
        const db = mongoClient.db(dbName);

        // Member collection indexes (most frequently queried)
        const memberCol = db.collection('member');
        await Promise.all([
            memberCol.createIndex({ guildId: 1, userId: 1 }, { unique: true, background: true }),
            memberCol.createIndex({ guildId: 1, "exp.total": -1 }, { background: true }),
            memberCol.createIndex({ guildId: 1, "exp.text.total": -1 }, { background: true }),
            memberCol.createIndex({ guildId: 1, "exp.voice.total": -1 }, { background: true }),
            memberCol.createIndex({ "exp.total": -1 }, { background: true }), // Global rankings
            memberCol.createIndex({ guildId: 1, "exp.lastText": 1 }, { background: true }),
            memberCol.createIndex({ guildId: 1, "exp.lastVoice": 1 }, { background: true }),
        ]);

        // Guild collection indexes
        const guildCol = db.collection('guilds');
        await Promise.all([
            guildCol.createIndex({ id: 1 }, { unique: true, background: true }),
            guildCol.createIndex({ "logs.enabled": 1 }, { background: true }),
            guildCol.createIndex({ "exp.enabled": 1 }, { background: true }),
        ]);

        // Custom items collection indexes
        const itemsCol = db.collection('custom_items');
        await Promise.all([
            itemsCol.createIndex({ id: 1 }, { unique: true, background: true }),
            itemsCol.createIndex({ guildId: 1, enabled: 1 }, { background: true }),
            itemsCol.createIndex({ guildId: 1, type: 1, rarity: 1 }, { background: true }),
            itemsCol.createIndex({ createdBy: 1 }, { background: true }),
            itemsCol.createIndex({ globalOrder: 1 }, { background: true }),
            itemsCol.createIndex({ guildOrder: 1, guildId: 1 }, { background: true }),
        ]);

        // User inventory collection indexes
        const inventoryCol = db.collection('user_inventory');
        await Promise.all([
            inventoryCol.createIndex({ userId: 1, guildId: 1 }, { background: true }),
            inventoryCol.createIndex({ guildId: 1, itemId: 1 }, { background: true }),
            inventoryCol.createIndex({ itemId: 1, foundAt: -1 }, { background: true }),
            inventoryCol.createIndex({ userId: 1, itemId: 1, guildId: 1 }, { background: true }),
        ]);

        // CRITICAL: Index for global inventory queries (used by /you command)
        // Use safe creation to handle existing index from createIndexes.js
        await createIndexSafely(
            inventoryCol,
            { userId: 1, droppedAt: -1 },
            { name: 'userId_droppedAt_desc', background: true },
            'userId + droppedAt (critical for /you performance)'
        );

        // Voice sessions collection indexes
        const voiceCol = db.collection('voice_sessions');
        await Promise.all([
            voiceCol.createIndex({ userId: 1, guildId: 1 }, { background: true }),
            voiceCol.createIndex({ guildId: 1, channelId: 1 }, { background: true }),
            voiceCol.createIndex({ startTime: 1 }, { background: true }),
        ]);

        // Stats collection indexes
        const statsCol = db.collection('stats');
        await Promise.all([
            statsCol.createIndex({ type: 1 }, { background: true }),
            statsCol.createIndex({ type: 1, command: 1 }, { background: true }),
        ]);

        // Item notifications queue collection indexes
        const notificationsCol = db.collection('item_notifications_queue');
        await Promise.all([
            createIndexSafely(
                notificationsCol,
                { userId: 1, guildId: 1, viewed: 1, createdAt: 1 },
                { background: true },
                'notification queue compound'
            ),
            createIndexSafely(
                notificationsCol,
                { userId: 1, viewed: 1 },
                { background: true },
                'notification queue user'
            ),
            createIndexSafely(
                notificationsCol,
                { createdAt: 1 },
                { background: true, expireAfterSeconds: 7 * 24 * 60 * 60 },
                'notification queue TTL (7 days)'
            ),
        ]);

        console.log('[mongo] Database indexes initialized successfully');
    } catch (error) {
        console.error('[mongo] Error initializing indexes:', error);
        throw error;
    }
}

/**
 * Set up connection pool monitoring
 */
function setupConnectionMonitoring() {
    // Monitor connection pool events
    mongoClient.on('connectionPoolCreated', () => {
        connectionMetrics.poolStats.created++;
        console.log('[mongo] Connection pool created');
    });

    mongoClient.on('connectionPoolClosed', () => {
        connectionMetrics.poolStats.closed++;
        console.log('[mongo] Connection pool closed');
    });

    mongoClient.on('connectionCreated', () => {
        connectionMetrics.totalConnections++;
        connectionMetrics.activeConnections++;
    });

    mongoClient.on('connectionClosed', () => {
        connectionMetrics.activeConnections--;
    });

    mongoClient.on('connectionCheckOutStarted', () => {
        connectionMetrics.poolStats.inUse++;
    });

    mongoClient.on('connectionCheckedIn', () => {
        connectionMetrics.poolStats.inUse--;
        connectionMetrics.poolStats.available++;
    });

    mongoClient.on('connectionCheckOutFailed', (event) => {
        connectionMetrics.connectionErrors++;
        console.warn('[mongo] Connection checkout failed:', event.reason);
    });

    // Monitor server events
    mongoClient.on('serverHeartbeatFailed', (event) => {
        console.warn('[mongo] Server heartbeat failed:', event.failure);
    });

    mongoClient.on('topologyDescriptionChanged', (event) => {
        console.log('[mongo] Topology changed:', {
            previousType: event.previousDescription.type,
            newType: event.newDescription.type
        });
    });
}

async function connect() {
    try {
        // Set up monitoring before connecting
        setupConnectionMonitoring();

        // Use connect method to connect to the server
        await mongoClient.connect();
        mongoClient.clientConnected = true;

        await listDatabases(mongoClient);

        // Initialize database indexes for optimal performance
        await initializeIndexes();

        // Start health check monitoring
        startHealthCheckMonitoring();

        console.log('[mongo] Connection established with advanced monitoring and optimized settings');
        return '[mongo] Connection established with advanced monitoring and optimized settings';
    } catch (error) {
        console.error('[mongo] Connection failed:', error);
        mongoClient.clientConnected = false;
        connectionMetrics.connectionErrors++;
        throw error;
    }
}

/**
 * Get a database collection with error handling
 * @param {string} collectionName - Name of the collection
 * @returns {Object} MongoDB collection
 */
function getCollection(collectionName) {
    if (!mongoClient.clientConnected) {
        throw new Error('MongoDB client is not connected');
    }
    return mongoClient.db(dbName).collection(collectionName);
}

/**
 * Get a database collection with enhanced write concern for critical operations
 * @param {string} collectionName - Name of the collection
 * @param {boolean} criticalOperation - Whether this is a critical operation requiring stronger consistency
 * @returns {Object} MongoDB collection with appropriate write concern
 */
function getCollectionWithWriteConcern(collectionName, criticalOperation = false) {
    if (!mongoClient.clientConnected) {
        throw new Error('MongoDB client is not connected');
    }

    const collection = mongoClient.db(dbName).collection(collectionName);

    if (criticalOperation) {
        // Override with stronger write concern for critical operations
        return collection.withOptions({
            writeConcern: { w: 'majority', j: true }
        });
    }

    return collection;
}

/**
 * Perform database health check
 */
async function performHealthCheck() {
    try {
        if (!mongoClient.clientConnected) {
            return { healthy: false, reason: 'Not connected' };
        }

        const startTime = Date.now();

        // Simple ping to test connectivity
        await mongoClient.db(dbName).admin().ping();

        const responseTime = Date.now() - startTime;
        connectionMetrics.lastHealthCheck = new Date();

        return {
            healthy: true,
            responseTime: responseTime,
            timestamp: connectionMetrics.lastHealthCheck
        };
    } catch (error) {
        console.error('[mongo] Health check failed:', error);
        return {
            healthy: false,
            reason: error.message,
            timestamp: new Date()
        };
    }
}

/**
 * Analyze cache performance and suggest connection pool adjustments
 */
function analyzeCachePerformance() {
    try {
        const cacheStats = getGlobalCacheStats();
        const hitRate = cacheStats.totalHits / (cacheStats.totalHits + cacheStats.totalMisses);

        return {
            hitRate: hitRate,
            totalCaches: cacheStats.cacheCount,
            recommendation: hitRate > 0.9 ? 'reduce_connections' :
                          hitRate < 0.7 ? 'increase_monitoring' : 'optimal',
            performance: hitRate > 0.85 ? 'excellent' :
                        hitRate > 0.7 ? 'good' : 'needs_improvement'
        };
    } catch (error) {
        return { hitRate: 0, totalCaches: 0, recommendation: 'monitor', performance: 'unknown' };
    }
}

/**
 * Start periodic health check monitoring with cache performance correlation
 */
function startHealthCheckMonitoring() {
    // Perform health check every 30 seconds
    setInterval(async () => {
        const health = await performHealthCheck();
        const cachePerf = analyzeCachePerformance();

        if (!health.healthy) {
            console.warn('[mongo] Database health check failed:', health.reason);
        } else if (health.responseTime > 100) {
            console.warn(`[mongo] Slow database response: ${health.responseTime}ms`);
        }

        // Log cache performance correlation every 5 minutes (10 cycles)
        if (Date.now() % 300000 < 30000) { // Approximately every 5 minutes
            console.log(`[mongo] Performance correlation - DB: ${health.responseTime}ms, Cache hit rate: ${(cachePerf.hitRate * 100).toFixed(1)}%, Status: ${cachePerf.performance}`);
        }
    }, 30000);
}

/**
 * Get connection pool statistics with cache performance correlation
 */
function getConnectionStats() {
    const cachePerf = analyzeCachePerformance();

    return {
        connected: mongoClient.clientConnected,
        totalConnections: connectionMetrics.totalConnections,
        activeConnections: connectionMetrics.activeConnections,
        connectionErrors: connectionMetrics.connectionErrors,
        lastHealthCheck: connectionMetrics.lastHealthCheck,
        poolStats: { ...connectionMetrics.poolStats },
        cachePerformance: {
            hitRate: cachePerf.hitRate,
            totalCaches: cachePerf.totalCaches,
            performance: cachePerf.performance,
            recommendation: cachePerf.recommendation
        }
    };
}

/**
 * Get detailed database performance metrics
 */
async function getDetailedStats() {
    try {
        const db = mongoClient.db(dbName);
        const [dbStats, serverStatus] = await Promise.all([
            db.stats(),
            db.admin().serverStatus()
        ]);

        return {
            connection: getConnectionStats(),
            database: {
                collections: dbStats.collections,
                dataSize: Math.round(dbStats.dataSize / 1024 / 1024 * 100) / 100, // MB
                indexSize: Math.round(dbStats.indexSize / 1024 / 1024 * 100) / 100, // MB
                totalSize: Math.round((dbStats.dataSize + dbStats.indexSize) / 1024 / 1024 * 100) / 100, // MB
                objects: dbStats.objects
            },
            server: {
                uptime: serverStatus.uptime,
                connections: serverStatus.connections,
                memory: serverStatus.mem,
                version: serverStatus.version
            }
        };
    } catch (error) {
        console.error('[mongo] Error getting detailed stats:', error);
        return { connection: getConnectionStats() };
    }
}

/**
 * Gracefully close the MongoDB connection
 */
async function disconnect() {
    try {
        if (mongoClient.clientConnected) {
            await mongoClient.close();
            mongoClient.clientConnected = false;
            console.log('[mongo] Connection closed gracefully');
        }
    } catch (error) {
        console.error('[mongo] Error closing connection:', error);
    }
}

// Handle process termination gracefully
process.on('SIGINT', disconnect);
process.on('SIGTERM', disconnect);

module.exports = {
    mongoClient,
    connect,
    disconnect,
    getCollection,
    getCollectionWithWriteConcern,
    initializeIndexes,
    performHealthCheck,
    getConnectionStats,
    getDetailedStats,
    analyzeCachePerformance
};