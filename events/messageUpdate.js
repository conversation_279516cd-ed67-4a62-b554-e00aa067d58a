const { Events, MessageFlags } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne } = require('../utils/database-optimizer.js');
const { defaults } = require("../utils/default_db_structures");
const { createMessageEditContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');
const { getConsolidatedGuildConfig, getCachedMessage, invalidateAllGuildCaches } = require('../utils/messageCache.js');

// Removed duplicate caching logic - now using consolidated messageCache.js functions

module.exports = {
	name: Events.MessageUpdate,
	async execute(client, oldMessage, newMessage) {
		try {
			// Skip if new message has no content, is from a bot
			if (!newMessage.content?.length || newMessage.author.bot) return;

			// PERFORMANCE OPTIMIZATION: Get consolidated guild configuration (single cache lookup)
			const guildConfig = await getConsolidatedGuildConfig(newMessage.guild.id, 'messageUpdate');

			// Early return if logging is disabled or no channels configured
			if (!guildConfig.loggingEnabled || guildConfig.channels.length === 0) {
				return;
			}

			// PERFORMANCE OPTIMIZATION: Get valid channels without additional database queries
			const channels = guildConfig.channels
				.map(ch => newMessage.guild.channels.cache.get(ch.id))
				.filter(ch => ch); // Remove null/undefined channels

			// Early return if no valid channels found
			if (channels.length === 0) {
				return;
			}

			// PERFORMANCE OPTIMIZATION: Handle uncached old messages with LRU caching
			let beforeContent = oldMessage.content;
			let isFromCache = false;

			// Check if old message content is missing (partial/uncached message)
			if (!beforeContent) {
				// Use optimized message cache with LRU caching
				const cachedMessage = await getCachedMessage(newMessage.id);

				if (cachedMessage) {
					beforeContent = cachedMessage.content || '';
					isFromCache = true;
				} else {
					beforeContent = '`content unavailable`';
				}
			}

			// PERFORMANCE OPTIMIZATION: Early return if content didn't actually change
			if (beforeContent === newMessage.content) return;

			// PERFORMANCE OPTIMIZATION: Create and send log container efficiently
			const { container, buttonRow } = createMessageEditContainer({
				author: `<@${newMessage.author.id}> (${newMessage.author.tag})${isFromCache ? ' *(cached)*' : ''}`,
				channel: `<#${newMessage.channelId}>`,
				messageUrl: newMessage.url,
				beforeContent: beforeContent,
				afterContent: newMessage.content
			});

			// PERFORMANCE OPTIMIZATION: Send directly to channels to avoid duplicate database query
			const messageOptions = {
				flags: MessageFlags.IsComponentsV2 | MessageFlags.SuppressNotifications,
				components: [container, buttonRow],
				allowedMentions: { parse: [] } // Prevent pings but show mentions visually
			};

			// Send to all valid channels in parallel for better performance
			const sendPromises = channels.map(async (channel) => {
				try {
					await channel.send(messageOptions);
				} catch (error) {
					console.error(`[messageUpdate] Failed to send to channel ${channel.id}:`, error.message);
				}
			});

			await Promise.allSettled(sendPromises);
	} catch (error) {
			console.error('[messageUpdate] Error processing message update:', error);
			logger.error('messageUpdate', `Error processing message update: ${error.message}`, client);
		}
	},

	/**
	 * PERFORMANCE OPTIMIZATION: Invalidate guild caches when configuration changes
	 * Uses consolidated cache invalidation from messageCache.js
	 */
	invalidateGuildLoggingCache(guildId) {
		invalidateAllGuildCaches(guildId);
	}
};





