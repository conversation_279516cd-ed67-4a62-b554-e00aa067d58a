const { Events, EmbedBuilder } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne } = require("../utils/database-optimizer.js");
const { defaults } = require("../utils/default_db_structures");
const { sendLogContainer } = require('../utils/sendLog.js');
const { createMessageDeleteContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Message Delete Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes message deletion processing with comprehensive caching and performance monitoring
 * OPTIMIZED: Consolidated guild configuration caching, enhanced message cache integration, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const messageDeleteMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    messageDeletesProcessed: 0,
    cachedMessagesUsed: 0,
    loggingChannelsFound: 0,
    containersCreated: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU caches for message delete operations (similar to messageUpdate.js pattern)
const messageDeleteGuildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const messageDeleteChannelCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for channel configurations

// Register caches for global cleanup
registerCache(messageDeleteGuildConfigCache);
registerCache(messageDeleteChannelCache);

/**
 * Get cached guild configuration for message delete (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration (similar to messageUpdate.js)
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Guild configuration data
 */
async function getCachedMessageDeleteGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `message_delete_guild_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = messageDeleteGuildConfigCache.get(cacheKey);
        if (cached) {
            messageDeleteMetrics.cacheHits++;
            if (messageDeleteMetrics.verboseLogging) {
                console.log(`[messageDelete] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        messageDeleteMetrics.cacheMisses++;
        messageDeleteMetrics.databaseQueries++;

        // Get guild data and check if logging is enabled
        var guildData = await optimizedFindOne("guilds", { id: guildId });

        // if no guild data then create one
        if (guildData == null) {
            await optimizedInsertOne("guilds", defaults.guild(guildId));
            guildData = await optimizedFindOne("guilds", { id: guildId });
        }

        // Cache the result
        messageDeleteGuildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        messageDeleteMetrics.averageQueryTime =
            (messageDeleteMetrics.averageQueryTime * (messageDeleteMetrics.databaseQueries - 1) + duration) /
            messageDeleteMetrics.databaseQueries;

        if (messageDeleteMetrics.verboseLogging || duration > 100) {
            console.log(`[messageDelete] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error('[messageDelete] ❌ Error getting guild config:', error);
        return null;
    }
}

/**
 * Get cached logging channels for message delete (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for channel configurations
 * @param {Object} client - Discord client
 * @param {string} guildId - Guild ID
 * @param {Object} guildData - Guild configuration data
 * @returns {Array} Array of logging channels
 */
function getCachedMessageDeleteChannels(client, guildId, guildData) {
    const startTime = Date.now();
    const cacheKey = `message_delete_channels_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = messageDeleteChannelCache.get(cacheKey);
        if (cached) {
            messageDeleteMetrics.cacheHits++;
            if (messageDeleteMetrics.verboseLogging) {
                console.log(`[messageDelete] ⚡ Channel cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        messageDeleteMetrics.cacheMisses++;

        // Check if there are any channels configured for messageDelete event
        const channels = (guildData.logs?.channels?.filter(ch => ch.events.includes("messageDelete")) ?? [])
            .map(l => client.guilds.cache.get(guildId)?.channels.cache.get(l.id))
            .filter(ch => ch);

        // Cache the result
        messageDeleteChannelCache.set(cacheKey, channels);

        const duration = Date.now() - startTime;
        if (messageDeleteMetrics.verboseLogging || duration > 50) {
            console.log(`[messageDelete] ✅ Channels fetched for ${guildId}: ${channels.length} channels in ${duration}ms - cached for future access`);
        }

        messageDeleteMetrics.loggingChannelsFound += channels.length;
        return channels;
    } catch (error) {
        console.error('[messageDelete] ❌ Error getting channels:', error);
        return [];
    }
}

module.exports = {
	name: Events.MessageDelete,
	async execute(client, message) {
		const startTime = Date.now();

		try {
			// Skip if message is from a bot
			if (message?.author?.bot) return;

			messageDeleteMetrics.messageDeletesProcessed++;

			// OPTIMIZED: Get cached guild configuration
			const guildData = await getCachedMessageDeleteGuildConfig(message?.guild?.id);
			if (!guildData) return;

			// OPTIMIZED: Get cached logging channels
			const channels = getCachedMessageDeleteChannels(client, message.guild.id, guildData);

			// Early return if no logging channels configured - don't waste time on cache operations
			if (channels.length === 0) {
				return;
			}

			// OPTIMIZED: Handle uncached messages using enhanced message cache integration
			let messageData = message;
			let isFromCache = false;

			// Check if message or author is undefined (partial/uncached message)
			if (!message || !message.author) {
				console.log('Deleted message or author is undefined, checking cache...');

				// Try to get message from cache
				const { getCachedMessage } = require('../utils/messageCache.js');
				const cachedMessage = await getCachedMessage(message?.id);

				if (cachedMessage) {
					messageDeleteMetrics.cachedMessagesUsed++;

					// Reconstruct message-like object from cache
					messageData = {
						id: cachedMessage.messageId,
						guild: { id: cachedMessage.guildId },
						channel: { id: cachedMessage.channelId },
						channelId: cachedMessage.channelId,
						author: {
							id: cachedMessage.authorId,
							tag: cachedMessage.authorTag,
							bot: false
						},
						content: cachedMessage.content || '',
						attachments: new Map(cachedMessage.attachments.map(att => [att.name, att])),
						createdAt: cachedMessage.createdAt
					};
					isFromCache = true;
					console.log(`[messageDelete] Using cached content for message ${message.id}`);
				} else {
					console.log('No cached content found for deleted message.');
					return;
				}
			}

			// Proceed with logging since we know channels are configured
				// Prepare attachments array
				let attachments = [];
				if (messageData.attachments instanceof Map) {
					attachments = Array.from(messageData.attachments.values()).filter(a => a.url).map(a => a.url);
				} else if (Array.isArray(messageData.attachments)) {
					attachments = messageData.attachments.filter(a => a.url).map(a => a.url);
				}

				// Create Components v2 container for message delete
				const container = createMessageDeleteContainer({
					author: `<@${messageData.author.id}> (${messageData.author.tag})${isFromCache ? ' *(cached)*' : ''}`,
					channel: `<#${messageData.channelId}>`,
					content: messageData.content,
					attachments: attachments
				});

				// Send container to all configured channels
				messageDeleteMetrics.containersCreated++;
				await sendLogContainer(messageData.guild.id, 'messageDelete', container, client);

				// Remove from cache after successful logging
				if (isFromCache) {
					const { removeCachedMessage } = require('../utils/messageCache.js');
					await removeCachedMessage(messageData.id);
				}
		} catch (error) {
			console.error('[messageDelete] Error processing message delete:', error);
			logger.error('messageDelete', `Error processing message delete: ${error.message}`, client);
		} finally {
			// Track performance metrics
			const duration = Date.now() - startTime;
			if (messageDeleteMetrics.verboseLogging || duration > 100) {
				console.log(`[messageDelete] ✅ Message delete processed in ${duration}ms`);
			}
		}
	},

	// Enhanced optimization functions
	getCachedMessageDeleteGuildConfig,
	getCachedMessageDeleteChannels,
	getMessageDeleteStats,
	performanceCleanupAndOptimization,
	clearAllMessageDeleteCaches
};

/**
 * Get comprehensive message delete performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive message delete performance data
 */
function getMessageDeleteStats() {
    const cacheHitRate = messageDeleteMetrics.cacheHits + messageDeleteMetrics.cacheMisses > 0 ?
        (messageDeleteMetrics.cacheHits / (messageDeleteMetrics.cacheHits + messageDeleteMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: messageDeleteMetrics.cacheHits,
            cacheMisses: messageDeleteMetrics.cacheMisses,
            databaseQueries: messageDeleteMetrics.databaseQueries,
            averageQueryTime: `${messageDeleteMetrics.averageQueryTime.toFixed(2)}ms`,
            messageDeletesProcessed: messageDeleteMetrics.messageDeletesProcessed,
            cachedMessagesUsed: messageDeleteMetrics.cachedMessagesUsed,
            loggingChannelsFound: messageDeleteMetrics.loggingChannelsFound,
            containersCreated: messageDeleteMetrics.containersCreated,
            partialFailures: messageDeleteMetrics.partialFailures,
            lastOptimization: new Date(messageDeleteMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: messageDeleteGuildConfigCache.getStats(),
            channels: messageDeleteChannelCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildConfig: messageDeleteGuildConfigCache.getStats().memoryUsage,
            channels: messageDeleteChannelCache.getStats().memoryUsage,
            total: messageDeleteGuildConfigCache.getStats().memoryUsage +
                   messageDeleteChannelCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            cacheUtilization: messageDeleteMetrics.cachedMessagesUsed > 0 ? 'active' : 'minimal'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    messageDeleteMetrics.lastOptimization = Date.now();

    const stats = getMessageDeleteStats();
    if (messageDeleteMetrics.verboseLogging) {
        console.log(`[messageDelete] 📊 Performance Report:`);
        console.log(`[messageDelete]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[messageDelete]   Message Deletes Processed: ${stats.performance.messageDeletesProcessed}`);
        console.log(`[messageDelete]   Cached Messages Used: ${stats.performance.cachedMessagesUsed}`);
        console.log(`[messageDelete]   Logging Channels Found: ${stats.performance.loggingChannelsFound}`);
        console.log(`[messageDelete]   Containers Created: ${stats.performance.containersCreated}`);
        console.log(`[messageDelete]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[messageDelete]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[messageDelete]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[messageDelete]   System Health: ${stats.systemHealth.status}`);
        console.log(`[messageDelete]   Cache Utilization: ${stats.systemHealth.cacheUtilization}`);
    }

    return stats;
}

/**
 * Clear all message delete caches (Enterprise-Grade Cache Management)
 */
function clearAllMessageDeleteCaches() {
    messageDeleteGuildConfigCache.clear();
    messageDeleteChannelCache.clear();

    console.log('[messageDelete] 🗑️ Cleared all message delete caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, messageDeleteMetrics.performanceReportInterval);


