const { Events, MessageFlags } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require('../utils/database-optimizer.js');
const { defaults } = require("../utils/default_db_structures");
const { sendExpLevelUpLog } = require("../utils/sendLog.js");
const { logger } = require('../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Message Create Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes message processing with comprehensive caching and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const messageCreateMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    messagesProcessed: 0,
    expCalculations: 0,
    globalExpOperations: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const memberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member data
const expCalculationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for EXP calculations

// Register caches for global cleanup
registerCache(guildConfigCache);
registerCache(memberDataCache);
registerCache(expCalculationCache);

/**
 * Get cached guild configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Guild configuration data
 */
async function getCachedGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildConfigCache.get(cacheKey);
        if (cached) {
            messageCreateMetrics.cacheHits++;
            if (messageCreateMetrics.verboseLogging) {
                console.log(`[messageCreate] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        messageCreateMetrics.cacheMisses++;
        messageCreateMetrics.databaseQueries++;

        // PERFORMANCE OPTIMIZATION: Use optimized database operations with monitoring and retry logic
        let guildData = await optimizedFindOne('guilds', { id: guildId });
        if (!guildData) {
            await optimizedInsertOne('guilds', defaults.guild(guildId));
            guildData = await optimizedFindOne('guilds', { id: guildId });
        }

        // Cache the result
        guildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        messageCreateMetrics.averageQueryTime =
            (messageCreateMetrics.averageQueryTime * (messageCreateMetrics.databaseQueries - 1) + duration) /
            messageCreateMetrics.databaseQueries;

        if (messageCreateMetrics.verboseLogging || duration > 100) {
            console.log(`[messageCreate] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error('[messageCreate] ❌ Error getting guild config:', error);
        return null;
    }
}

/**
 * Get cached member data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} Member data
 */
async function getCachedMemberData(guildId, userId) {
    const startTime = Date.now();
    const cacheKey = `member_${guildId}_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = memberDataCache.get(cacheKey);
        if (cached) {
            messageCreateMetrics.cacheHits++;
            if (messageCreateMetrics.verboseLogging) {
                console.log(`[messageCreate] ⚡ Member data cache hit for ${userId} in ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        messageCreateMetrics.cacheMisses++;
        messageCreateMetrics.databaseQueries++;

        // PERFORMANCE OPTIMIZATION: Use optimized database operations for member data
        let memberData = await optimizedFindOne('member', { guildId: guildId, userId: userId });
        if (!memberData) {
            await optimizedInsertOne('member', defaults.member({ guildId: guildId, userId: userId }));
            memberData = await optimizedFindOne('member', { guildId: guildId, userId: userId });
        }

        // Ensure exp structure exists with text stats (optimized single operation)
        if (!memberData.exp || !memberData.exp.text) {
            const defaultExpStructure = {
                total: 0,
                lastText: 0,
                lastVoice: 0,
                voice: {
                    total: 0,
                    timeSpent: 0,
                    longestSession: 0,
                    sessionCount: 0,
                    lastActiveDay: null,
                    currentStreak: 0,
                    maxStreak: 0
                },
                text: {
                    total: 0,
                    messageCount: 0,
                    lastActiveDay: null,
                    currentStreak: 0,
                    maxStreak: 0
                }
            };

            await optimizedUpdateOne('member',
                { guildId: guildId, userId: userId },
                { $set: { exp: defaultExpStructure } }
            );
            memberData.exp = defaultExpStructure;
        }

        // Cache the result
        memberDataCache.set(cacheKey, memberData);

        const duration = Date.now() - startTime;
        messageCreateMetrics.averageQueryTime =
            (messageCreateMetrics.averageQueryTime * (messageCreateMetrics.databaseQueries - 1) + duration) /
            messageCreateMetrics.databaseQueries;

        if (messageCreateMetrics.verboseLogging || duration > 100) {
            console.log(`[messageCreate] ✅ Member data fetched for ${userId} in ${guildId}: ${duration}ms - cached for future access`);
        }

        return memberData;
    } catch (error) {
        console.error('[messageCreate] ❌ Error getting member data:', error);
        return null;
    }
}

// Helper function to send messages with proper @silent handling
async function sendMessage(channel, content) {
	const isSilent = content.startsWith('@silent ');
	const messageContent = isSilent ? content.replace('@silent ', '') : content;
	const options = {
		content: messageContent,
		allowedMentions: { parse: [] } // Prevent pings but show mentions visually
	};

	if (isSilent) {
		options.flags = MessageFlags.SuppressNotifications;
	}

	// Use smart routing for forum channels and regular channels
	const { sendToLogChannel } = require('../commands/utility/logs.js');
	try {
		return await sendToLogChannel(channel, options, 'messageCreate');
	} catch (error) {
		console.error(`[sendMessage] Error sending to channel ${channel.name}:`, error);
		throw error;
	}
}

module.exports = {
	name: Events.MessageCreate,
	async execute(client, message) {
		const startTime = Date.now();

		try {
			// Cache message for improved logging (if enabled)
			if (message.guild) {
				const { cacheMessage } = require('../utils/messageCache.js');
				await cacheMessage(message);
			}
			if (message.author.bot) return;
			if (!message.guild) return;

			messageCreateMetrics.messagesProcessed++;

			// Optimized lazy sticky sync - only run when needed
			setImmediate(() => optimizedLazyStickySync(message.member));

			// OPTIMIZED: Get cached guild configuration with performance monitoring
			const guildData = await getCachedGuildConfig(message.guild.id);
			if (!guildData) return;

			// EXP feature must be enabled
			if (!guildData.exp || guildData.exp.enabled === false) return;
			const textConfig = guildData.exp.text || {};
			const textEnabled = textConfig.enabled ?? true; // Default to enabled
			if (!textEnabled) return;

			const expPer = Number(textConfig.expPerMin ?? 1);
			const cooldown = Number(textConfig.cooldown ?? 1); // in minutes
			const minChars = Number(textConfig.minChars ?? 4);
			const levels = guildData.exp.levels || [];
			const levelMsgEnabled = guildData.exp.levelMsgEnabled !== false;
			const levelChannelId = guildData.exp.levelChannel;
			const levelMsgTemplate = guildData.exp.levelMsg || '{mention} leveled up to level {level} and received the {role} role.';

			// OPTIMIZED: Get cached member data with performance monitoring
			const memberData = await getCachedMemberData(message.guild.id, message.author.id);
			if (!memberData) return;

		const now = Date.now();
		const lastText = memberData.exp.lastText || 0;
		const msCooldown = cooldown * 60 * 1000;

		// Always increment messages sent
		const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
		const textStats = memberData.exp.text || {};
		let newStreak = textStats.currentStreak || 0;

		// Calculate streak for messages sent (regardless of cooldown)
		if (textStats.lastActiveDay !== today) {
			const yesterday = new Date();
			yesterday.setDate(yesterday.getDate() - 1);
			const yesterdayStr = yesterday.toISOString().split('T')[0];

			if (textStats.lastActiveDay === yesterdayStr) {
				newStreak += 1;
			} else if (textStats.lastActiveDay === null || textStats.lastActiveDay < yesterdayStr) {
				newStreak = 1;
			}
		}

		// Check minChars requirement for EXP gain early
		const shouldGainExp = message.content.length >= minChars && (now - lastText >= msCooldown);

		// Combine database operations for better performance
		const updateOperations = {
			$inc: { "exp.text.messagesSent": 1 },
			$set: {
				"exp.text.lastActiveDay": today,
				"exp.text.currentStreak": newStreak
			}
		};

		if (shouldGainExp) {
			// Add EXP operations to the same update
			updateOperations.$inc["exp.total"] = expPer;
			updateOperations.$inc["exp.text.total"] = expPer;
			updateOperations.$inc["exp.text.messagesCounted"] = 1;
			updateOperations.$set["exp.lastText"] = now;
		}

		// Single database operation instead of multiple (with error handling)
		try {
			await optimizedUpdateOne('member',
				{ guildId: message.guild.id, userId: message.author.id },
				updateOperations
			);
		} catch (dbError) {
			console.error('[messageCreate] Database update error:', dbError);
			// Don't throw to prevent breaking other message processing
			return;
		}



		// Early return if no EXP was gained
		if (!shouldGainExp) return;

		// Update local memberData for level calculation
		const oldTotal = memberData.exp.total;
		memberData.exp.total += expPer;

		// Log text EXP gain
		console.log(`[textExp] ${message.author.username} (${message.author.id}) gained ${expPer} text EXP in ${message.guild.name} | ${oldTotal} → ${memberData.exp.total} total EXP`);

		// OPTIMIZED: Process global EXP and item drops in parallel with Promise.allSettled
		messageCreateMetrics.parallelOperations++;

		const [globalExpResult, itemDropsResult] = await Promise.allSettled([
			// Global EXP processing
			(async () => {
				const { awardGlobalExp } = require('../utils/globalLevels.js');
				const globalResult = await awardGlobalExp(message.author.id, expPer, 'TEXT');

				if (globalResult.leveledUp) {
					console.log(`[textExp] 🌟 GLOBAL LEVEL UP! ${message.author.username} reached global level ${globalResult.newLevel}!`);

					// Process global level-up notifications
					const { processGlobalLevelUp } = require('../utils/globalLevelNotifications.js');
					await processGlobalLevelUp(message.author.id, globalResult, message.client);
				}

				return globalResult;
			})(),

			// Item drops processing
			(async () => {
				const { processItemDrops, sendItemDropDM, sendGuildChannelNotification } = require('../utils/itemDrops.js');
				const droppedItems = await processItemDrops(
					message.author.id,
					message.guild.id,
					'TEXT',
					expPer
				);

				if (droppedItems.length > 0) {
					console.log(`[textExp] 🎁 ${message.author.username} found ${droppedItems[0].itemName} from text EXP!`);

					// Process notifications in parallel
					const [guildNotifResult, dmResult] = await Promise.allSettled([
						sendGuildChannelNotification(
							message.author.id,
							message.guild.id,
							droppedItems,
							'TEXT',
							message.client
						),
						sendItemDropDM(
							message.author.id,
							message.guild.id,
							droppedItems,
							'TEXT',
							message.client
						)
					]);

					if (guildNotifResult.status === 'rejected') {
						console.error('[textExp] ❌ Error sending guild channel notification:', guildNotifResult.reason);
					}
					if (dmResult.status === 'rejected') {
						console.error('[textExp] ❌ Error sending DM notification:', dmResult.reason);
					}
				}

				return droppedItems;
			})()
		]);

		// Handle partial failures
		if (globalExpResult.status === 'rejected') {
			console.error('[textExp] ❌ Error awarding global XP:', globalExpResult.reason);
			messageCreateMetrics.partialFailures++;
		}
		if (itemDropsResult.status === 'rejected') {
			console.error('[textExp] ❌ Error processing item drops:', itemDropsResult.reason);
			messageCreateMetrics.partialFailures++;
		}

		messageCreateMetrics.globalExpOperations++;
		messageCreateMetrics.expCalculations++;

		// Use cached level calculation for better performance
		const { getCachedLevelCalculation } = require('../utils/expCache.js');
		const levelCalc = getCachedLevelCalculation(memberData.exp.total, levels);
		const newLevelIndex = levelCalc.levelIndex;

		if (newLevelIndex >= 0) {
			const level = levels[newLevelIndex];
			const roleId = level.roleId;
			const member = await message.guild.members.fetch(message.author.id).catch(() => null);
			if (member && !member.roles.cache.has(roleId)) {
				// Check if this is a "catch-up" role assignment (user already had enough EXP)
				const isLevelUp = oldTotal < level.exp;

				// Assign role
				try {
					await member.roles.add(roleId);
					if (isLevelUp) {
						console.log(`[textExp] 🎉 LEVEL UP! ${message.author.username} reached level ${newLevelIndex + 1} (${memberData.exp.total} EXP) in ${message.guild.name} - Role assigned!`);

						// Send level up log
						await sendExpLevelUpLog(
							message.guild.id,
							message.author.id,
							newLevelIndex + 1,
							memberData.exp.total,
							roleId,
							message.client
						);
					} else {
						console.log(`[textExp] 🔄 ROLE CATCH-UP! ${message.author.username} received missing level ${newLevelIndex + 1} role (${memberData.exp.total} EXP) in ${message.guild.name} - Previously failed due to permissions!`);
					}
				} catch (err) {
					console.error('[textExp] Error assigning role:', err);
				}

				// Send level up message if enabled
				console.log(`[textExp] Level message config - Enabled: ${levelMsgEnabled}, Channel: ${levelChannelId}`);

				if (levelMsgEnabled && levelChannelId) {
					const channel = message.guild.channels.cache.get(levelChannelId);
					if (channel && channel.isTextBased()) {
						const msg = levelMsgTemplate
							.replace('{mention}', `<@${message.author.id}>`)
							.replace('{level}', (newLevelIndex + 1).toString())
							.replace('{role}', `<@&${roleId}>`)
							.replace('{exp}', memberData.exp.total.toLocaleString())
							.replace('{server}', message.guild.name);
						await sendMessage(channel, msg).catch(() => {});
						console.log(`[textExp] Level up message sent to #${channel.name}`);
					} else {
						console.log(`[textExp] Level channel not found or not text-based: ${levelChannelId}`);
					}
				} else {
					if (!levelMsgEnabled) {
						console.log(`[textExp] Level messages disabled`);
					} else if (!levelChannelId) {
						console.log(`[textExp] No level channel configured - use /exp to set one`);
					}
				}
			}
		}
	} catch (error) {
			console.error('[messageCreate] Error processing message:', error);
			logger.error('messageCreate', `Error processing message: ${error.message}`, client);
		}
	},
};

// Performance tracking for sticky sync optimization
const stickyPerformanceTracker = new Map();
const STICKY_SYNC_COOLDOWN = 5 * 60 * 1000; // 5 minutes per user per guild
const STICKY_BATCH_SIZE = 10; // Process in batches
const stickyBatchQueue = new Map(); // Guild-based batching

/**
 * Optimized lazy sticky sync - reduces frequency while maintaining accuracy
 * Uses cooldowns, batching, and smart filtering to improve performance
 */
async function optimizedLazyStickySync(member) {
	try {
		const userGuildKey = `${member.id}_${member.guild.id}`;
		const now = Date.now();

		// Check cooldown to prevent excessive calls for the same user
		const lastSync = stickyPerformanceTracker.get(userGuildKey);
		if (lastSync && (now - lastSync) < STICKY_SYNC_COOLDOWN) {
			return; // Skip if recently synced
		}

		// Get guild sticky configuration (cached) - early exit if disabled
		const { getCachedGuildStickyConfig } = require('../utils/stickyCache.js');
		const stickyConfig = await getCachedGuildStickyConfig(member.guild.id);

		if (!stickyConfig.enabled || (stickyConfig.roles.length === 0 && !stickyConfig.nick)) {
			return; // Sticky disabled or no sticky roles/nicknames configured
		}

		// Add to batch queue for this guild
		const guildId = member.guild.id;
		if (!stickyBatchQueue.has(guildId)) {
			stickyBatchQueue.set(guildId, []);
		}

		const batchQueue = stickyBatchQueue.get(guildId);
		batchQueue.push({ member, timestamp: now });

		// Update performance tracker
		stickyPerformanceTracker.set(userGuildKey, now);

		// Process batch if it's full or if this is the first item (start timer)
		if (batchQueue.length === 1) {
			// Start batch processing timer (500ms delay to collect more members)
			setTimeout(() => processStickyBatch(guildId), 500);
		} else if (batchQueue.length >= STICKY_BATCH_SIZE) {
			// Process immediately if batch is full
			processStickyBatch(guildId);
		}

	} catch (error) {
		console.error('[optimizedLazyStickySync] Error during optimized sync:', error);
	}
}

/**
 * Process a batch of sticky sync operations for better database performance
 */
async function processStickyBatch(guildId) {
	try {
		const batchQueue = stickyBatchQueue.get(guildId);
		if (!batchQueue || batchQueue.length === 0) return;

		// Take all queued members and clear the queue
		const membersToProcess = [...batchQueue];
		stickyBatchQueue.set(guildId, []);

		console.log(`[processStickyBatch] Processing ${membersToProcess.length} members for guild ${guildId}`);

		// Get all member data in one database call
		const { getCachedMemberStickyData, batchUpdateMemberStickyData } = require('../utils/stickyCache.js');
		const memberIds = membersToProcess.map(item => item.member.id);

		// Preload member data for the batch
		const { preloadMemberData } = require('../utils/stickyCache.js');
		await preloadMemberData(memberIds.map(userId => ({ userId, guildId })));

		const updates = [];

		for (const { member } of membersToProcess) {
			const result = await processSingleMemberSticky(member);
			if (result) {
				updates.push(result);
			}
		}

		// Batch update all changes
		if (updates.length > 0) {
			await batchUpdateMemberStickyData(updates);
			console.log(`[processStickyBatch] Batch updated ${updates.length} members`);
		}

	} catch (error) {
		console.error('[processStickyBatch] Error processing batch:', error);
	}
}

/**
 * Process sticky data for a single member (used by batch processor)
 */
async function processSingleMemberSticky(member) {
	try {
		const { getCachedGuildStickyConfig, getCachedMemberStickyData } = require('../utils/stickyCache.js');
		const stickyConfig = await getCachedGuildStickyConfig(member.guild.id);

		// Check if user already has sticky data (cached)
		const existingStickyData = await getCachedMemberStickyData(member.id, member.guild.id);

		// Prepare sticky data to save
		const stickyData = {};
		let shouldSave = false;

		// Save current roles if sticky roles are configured
		if (stickyConfig.roles.length > 0) {
			const currentStickyRoles = member.roles.cache
				.filter(role => role.id !== member.guild.id) // Exclude @everyone
				.filter(role => stickyConfig.roles.includes(role.id)) // Only sticky-enabled roles
				.map(role => role.id);

			if (currentStickyRoles.length > 0) {
				stickyData.roles = currentStickyRoles;
				shouldSave = true;
			}
		}

		// Save current nickname if sticky nicknames are enabled
		if (stickyConfig.nick && member.nickname) {
			stickyData.nick = member.nickname;
			shouldSave = true;
		}

		// Check if data has changed
		const hasDataChanged = JSON.stringify(stickyData.roles || []) !== JSON.stringify(existingStickyData.roles || []) ||
							   stickyData.nick !== existingStickyData.nick;

		// Don't overwrite existing data with empty data (user might have just rejoined)
		const wouldOverwriteWithEmpty = (existingStickyData.roles && existingStickyData.roles.length > 0) &&
										(!stickyData.roles || stickyData.roles.length === 0);

		if ((shouldSave || hasDataChanged) && !wouldOverwriteWithEmpty) {
			return {
				userId: member.id,
				guildId: member.guild.id,
				stickyData: {
					...stickyData,
					lastUpdated: new Date()
				}
			};
		}

		return null; // No update needed

	} catch (error) {
		console.error('[processSingleMemberSticky] Error processing member:', error);
		return null;
	}
}

/**
 * Get comprehensive message create performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive message create performance data
 */
function getMessageCreateStats() {
    const cacheHitRate = messageCreateMetrics.cacheHits + messageCreateMetrics.cacheMisses > 0 ?
        (messageCreateMetrics.cacheHits / (messageCreateMetrics.cacheHits + messageCreateMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: messageCreateMetrics.cacheHits,
            cacheMisses: messageCreateMetrics.cacheMisses,
            databaseQueries: messageCreateMetrics.databaseQueries,
            averageQueryTime: `${messageCreateMetrics.averageQueryTime.toFixed(2)}ms`,
            messagesProcessed: messageCreateMetrics.messagesProcessed,
            expCalculations: messageCreateMetrics.expCalculations,
            globalExpOperations: messageCreateMetrics.globalExpOperations,
            parallelOperations: messageCreateMetrics.parallelOperations,
            partialFailures: messageCreateMetrics.partialFailures,
            lastOptimization: new Date(messageCreateMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: guildConfigCache.getStats(),
            memberData: memberDataCache.getStats(),
            expCalculation: expCalculationCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildConfig: guildConfigCache.getStats().memoryUsage,
            memberData: memberDataCache.getStats().memoryUsage,
            expCalculation: expCalculationCache.getStats().memoryUsage,
            total: guildConfigCache.getStats().memoryUsage +
                   memberDataCache.getStats().memoryUsage +
                   expCalculationCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    messageCreateMetrics.lastOptimization = Date.now();

    const stats = getMessageCreateStats();
    if (messageCreateMetrics.verboseLogging) {
        console.log(`[messageCreate] 📊 Performance Report:`);
        console.log(`[messageCreate]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[messageCreate]   Messages Processed: ${stats.performance.messagesProcessed}`);
        console.log(`[messageCreate]   EXP Calculations: ${stats.performance.expCalculations}`);
        console.log(`[messageCreate]   Global EXP Operations: ${stats.performance.globalExpOperations}`);
        console.log(`[messageCreate]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[messageCreate]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[messageCreate]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[messageCreate]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[messageCreate]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Clear all message create caches (Enterprise-Grade Cache Management)
 */
function clearAllMessageCreateCaches() {
    guildConfigCache.clear();
    memberDataCache.clear();
    expCalculationCache.clear();

    console.log('[messageCreate] 🗑️ Cleared all message create caches');
}

// Cleanup performance tracker every hour to prevent memory leaks
setInterval(() => {
	const now = Date.now();
	const oneHourAgo = now - (60 * 60 * 1000);

	for (const [key, timestamp] of stickyPerformanceTracker.entries()) {
		if (timestamp < oneHourAgo) {
			stickyPerformanceTracker.delete(key);
		}
	}

	console.log(`[stickyPerformanceTracker] Cleaned up old entries. Current size: ${stickyPerformanceTracker.size}`);
}, 60 * 60 * 1000); // Run every hour

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, messageCreateMetrics.performanceReportInterval);


