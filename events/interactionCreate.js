const { Events } = require('discord.js');
const { exec } = require('child_process');
const sticky = require('../commands/utility/sticky.js');
const { MessageFlags } = require('discord.js');
const ownerStatus = require('../commands/utility/owner-status.js');
const { logger } = require('../utils/consoleLogger.js');
const exp = require('../commands/utility/exp.js');
const { isInteractionInvalidated } = require('../utils/commandInvalidation.js');
const { optimizedUpdateOne } = require("../utils/database-optimizer.js");
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Interaction Create Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes interaction processing with comprehensive caching and performance monitoring
 * OPTIMIZED: LRU caching for command routing, interaction validation, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const interactionCreateMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    interactionsProcessed: 0,
    commandsExecuted: 0,
    buttonsHandled: 0,
    modalsHandled: 0,
    selectMenusHandled: 0,
    invalidatedInteractions: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU caches for interaction processing
const commandCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for command data
const interactionValidationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for validation results

// Register caches for global cleanup
registerCache(commandCache);
registerCache(interactionValidationCache);

/**
 * Get cached interaction invalidation status (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {Object} interaction - Discord interaction
 * @returns {Promise<boolean>} Whether interaction is invalidated
 */
async function getCachedInteractionValidation(interaction) {
    const startTime = Date.now();
    const cacheKey = `validation_${interaction.id}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = interactionValidationCache.get(cacheKey);
        if (cached !== undefined) {
            interactionCreateMetrics.cacheHits++;
            if (interactionCreateMetrics.verboseLogging) {
                console.log(`[interactionCreate] ⚡ Interaction validation cache hit for ${interaction.id} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        interactionCreateMetrics.cacheMisses++;
        interactionCreateMetrics.databaseQueries++;

        // Check if this interaction is from an invalidated command
        const isInvalidated = await isInteractionInvalidated(interaction);

        // Cache the result (shorter TTL for invalidation checks)
        interactionValidationCache.set(cacheKey, isInvalidated);

        const duration = Date.now() - startTime;
        interactionCreateMetrics.averageQueryTime =
            (interactionCreateMetrics.averageQueryTime * (interactionCreateMetrics.databaseQueries - 1) + duration) /
            interactionCreateMetrics.databaseQueries;

        if (interactionCreateMetrics.verboseLogging || duration > 50) {
            console.log(`[interactionCreate] ✅ Interaction validation checked for ${interaction.id}: ${duration}ms - cached for future access`);
        }

        return isInvalidated;
    } catch (error) {
        console.error('[interactionCreate] ❌ Error checking interaction validation:', error);
        return false; // Default to not invalidated on error
    }
}

/**
 * Get cached command data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for command routing
 * @param {string} commandName - Command name
 * @returns {Object|null} Cached command data
 */
function getCachedCommand(commandName) {
    const startTime = Date.now();
    const cacheKey = `command_${commandName}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = commandCache.get(cacheKey);
        if (cached) {
            interactionCreateMetrics.cacheHits++;
            if (interactionCreateMetrics.verboseLogging) {
                console.log(`[interactionCreate] ⚡ Command cache hit for ${commandName} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        interactionCreateMetrics.cacheMisses++;

        // This would be where we'd fetch command data if needed
        // For now, we'll cache the miss to avoid repeated lookups
        commandCache.set(cacheKey, null);

        const duration = Date.now() - startTime;
        if (interactionCreateMetrics.verboseLogging) {
            console.log(`[interactionCreate] ❌ Command cache miss for ${commandName}: ${duration}ms`);
        }

        return null;
    } catch (error) {
        console.error('[interactionCreate] ❌ Error getting cached command:', error);
        return null;
    }
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(client, interaction) {
        const startTime = Date.now();

        try {
            interactionCreateMetrics.interactionsProcessed++;

            // OPTIMIZED: Check cached interaction invalidation status
            const isInvalidated = await getCachedInteractionValidation(interaction);
            if (isInvalidated) {
                interactionCreateMetrics.invalidatedInteractions++;
                // Silently ignore interactions from invalidated commands
                // This prevents users from interacting with old command responses
                return;
            }

        if (interaction.isButton()) {
            interactionCreateMetrics.buttonsHandled++;
            if (interaction.customId === 'exp-create-level-exp') {
                console.log('[interactionCreate] Handling exp-create-level-exp');
                await exp.buttons(interaction);
                console.log('[interactionCreate] exp-create-level-exp handled, returning');
                return;
            }
            if (interaction.customId === 'owner-reload') {
                // Save the current timestamp to the database (optimized to use shared connection)
                const now = Math.floor(Date.now() / 1000);
                await optimizedUpdateOne("stats",
                    { type: "last_reload" },
                    { $set: { timestamp: now } },
                    { upsert: true }
                );
                // Rebuild the owner container with the new timestamp
                const owner = require('../commands/utility/owner.js');
                const container = await owner.buildOwnerContainer(interaction.client, interaction.user);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                setTimeout(() => process.exit(0), 1000);
                return;
            }
            if (interaction.customId === 'owner-sync') {
                await interaction.deferUpdate();
                // Save syncing state and timestamp to DB (optimized to use shared connection)
                const now = Math.floor(Date.now() / 1000);
                await optimizedUpdateOne("stats",
                    { type: "last_sync" },
                    { $set: { syncing: true, timestamp: now } },
                    { upsert: true }
                );
                // Rebuild the owner container with the new timestamp
                const owner = require('../commands/utility/owner.js');
                const container = await owner.buildOwnerContainer(interaction.client, interaction.user);
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                exec('node deploy-commands.js', { cwd: process.cwd() }, async () => {
                    // Just update the DB with syncing: false and new timestamp
                    const done = Math.floor(Date.now() / 1000);
                    await optimizedUpdateOne("stats",
                        { type: "last_sync" },
                        { $set: { syncing: false, timestamp: done } },
                        { upsert: true }
                    );
                    // No need to close shared connection
                });
                return;
            }
            if (["invite", "info"].includes(interaction.customId)) {
                if (interaction.customId == "invite") {
                    interaction.reply({ content: "this isn't ever going to be used.", ephemeral: true });
                }
                else if (interaction.customId == "info") {
                    interaction.reply({ content: "a bot with specialized features that others might find useful or interesting. to know more about a specific feature, click [here](https://19aliens.com/17).", ephemeral: true });
                }
            }
            if (interaction.customId === 'owner-back') {
                const owner = require('../commands/utility/owner.js');
                await owner.buttons(interaction, []);
                return;
            }
            // Sticky feature buttons
            if (['sticky-disable', 'sticky-enable'].includes(interaction.customId)) {
                await sticky.buttons(interaction);
                return;
            }
            // Logs enable/disable button
            if (interaction.customId === 'logs-enabler') {
                const logs = require('../commands/utility/logs.js');
                await logs.toggleEnabled(interaction);
                return;
            }

            // Opener enable/disable buttons
            if (['opener-disable', 'opener-enable'].includes(interaction.customId)) {
                const opener = require('../commands/utility/opener.js');
                await opener.buttons(interaction);
                return;
            }
            // Items pagination buttons
            if (interaction.customId.startsWith('items-page-')) {
                const items = require('../commands/utility/items.js');
                await items.buttons(interaction, []);
                return;
            }
            // Items creation back button
            if (interaction.customId === 'items-creation-back') {
                const items = require('../commands/utility/items.js');
                await items.buttons(interaction, []);
                return;
            }
            // Items create final button
            if (interaction.customId === 'items-create-final') {
                const items = require('../commands/utility/items.js');
                await items.createFinalItem(interaction);
                return;
            }
            // Items toggle disable button
            if (interaction.customId === 'items-toggle-disable') {
                const items = require('../commands/utility/items.js');
                await items.toggleItemDisable(interaction);
                return;
            }

            // Items delete button
            if (interaction.customId === 'items-delete-item') {
                const items = require('../commands/utility/items.js');
                await items.handleDeleteClick(interaction);
                return;
            }
            // Items global enable/disable buttons
            if (interaction.customId === 'items-global-disable' || interaction.customId === 'items-global-enable') {
                const items = require('../commands/utility/items.js');
                await items.buttons(interaction, []);
                return;
            }
            // You command notification dismiss buttons
            if (interaction.customId.startsWith('you-dismiss-notification-')) {
                const you = require('../commands/utility/you.js');
                await you.buttons(interaction, []);
                return;
            }
            // Starfall claim buttons
            if (interaction.customId.startsWith('starfall-claim-')) {
                const { processStarfallClaim } = require('../utils/starfall.js');
                await processStarfallClaim(interaction);
                return;
            }
            // Prestige system buttons
            if (interaction.customId.startsWith('prestige-confirm-') || interaction.customId.startsWith('prestige-cancel-')) {
                const { handlePrestigeClick, handlePrestigeCancel } = require('../utils/prestigeUI.js');
                if (interaction.customId.startsWith('prestige-confirm-')) {
                    await handlePrestigeClick(interaction);
                } else {
                    await handlePrestigeCancel(interaction);
                }
                return;
            }
            // Global level notification dismiss buttons
            if (interaction.customId.startsWith('dismiss-global-level-')) {
                const notificationId = interaction.customId.replace('dismiss-global-level-', '');
                const { dismissGlobalLevelNotification } = require('../utils/globalLevelNotifications.js');

                await dismissGlobalLevelNotification(notificationId);

                // Update the interaction to show dismissal
                await interaction.update({
                    content: '✅ Global level notification dismissed.',
                    components: [],
                    ephemeral: true
                });
                return;
            }
            // Global levels create/update buttons
            if (interaction.customId === 'global-level-create-final' || interaction.customId === 'global-level-update-final') {
                const owner = require('../commands/utility/owner.js');
                await owner.buttons(interaction, []);
                return;
            }
            // Global levels back button
            if (interaction.customId === 'global-levels-back') {
                const owner = require('../commands/utility/owner.js');
                await owner.buttons(interaction, []);
                return;
            }
            // Owner item notification back button (return to items interface)
            if (interaction.customId === 'owner-itemnotification-back') {
                const items = require('../commands/utility/items.js');
                const container = await items.buildItemsContainer({
                    isOwner: true,
                    guildId: null, // Bot-wide items (not guild-specific)
                    page: 0,
                    client: interaction.client,
                    member: null, // No member context for bot-wide items
                    showBackButton: true // Show back button in owner panel context
                });
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;
            }

            // EXP global enable/disable buttons
            if (interaction.customId === 'exp-global-disable' || interaction.customId === 'exp-global-enable') {
                await exp.buttons(interaction);
                return;
            }
            // EXP voice enable/disable buttons
            if (interaction.customId === 'exp-voice-disable' || interaction.customId === 'exp-voice-enable') {
                await exp.buttons(interaction);
                return;
            }
            // EXP text enable/disable buttons
            if (interaction.customId === 'exp-text-disable' || interaction.customId === 'exp-text-enable') {
                await exp.buttons(interaction);
                return;
            }
            if (interaction.customId === 'exp-create-level-exp') {
                await exp.buttons(interaction);
                return;
            }
            if (interaction.customId === 'exp-create-level-back') {
                await exp.expCreateLevelBack(interaction);
                return;
            }
            if (interaction.customId.startsWith('exp-edit-level-back-')) {
                await exp.expEditLevelBack(interaction);
                return;
            }
            if (interaction.customId === 'exp-levels-channel-back') {
                await exp.expLevelChannelBack(interaction);
                return;
            }
            // Unified level interface handlers
            if (interaction.customId === 'exp-create-level-final' || interaction.customId.startsWith('exp-modify-level-final-')) {
                await exp.buttons(interaction);
                return;
            }
            if (interaction.customId === 'exp-unified-level-back' || interaction.customId.startsWith('exp-unified-edit-back-')) {
                await exp.buttons(interaction);
                return;
            }

            else {
                const [command, ...args] = interaction.customId.split("-");
                console.log('[interactionCreate] Button interaction:', command, args);
                if (command === 'dehoist') {
                    const dehoist = require('../commands/utility/dehoist.js');
                    await dehoist.buttons(interaction, args);
                    return;
                }
                if (client.commands.get(command)?.buttons) client.commands.get(command)?.buttons(interaction, args);
            }
        }

        else if (
            interaction.isStringSelectMenu() ||
            interaction.isRoleSelectMenu() ||
            interaction.isUserSelectMenu() ||
            interaction.isMentionableSelectMenu() ||
            interaction.isChannelSelectMenu()
        ) {
            interactionCreateMetrics.selectMenusHandled++;
            if (interaction.customId === 'exp-create-level-role') {
                console.log('[interactionCreate] Handling exp-create-level-role');
                await exp.buttons(interaction);
                console.log('[interactionCreate] exp-create-level-role handled, returning');
                return;
            }
            if (interaction.customId === 'exp-create-level-exp-select') {
                console.log('[interactionCreate] Handling exp-create-level-exp-select');
                await exp.expCreateLevelExpSelect(interaction);
                return;
            }
            if (interaction.customId === 'exp-create-level-back') {
                console.log('[interactionCreate] Handling exp-create-level-back');
                await exp.expCreateLevelBack(interaction);
                return;
            }
            if (interaction.customId.startsWith('exp-edit-level-exp-select-')) {
                console.log('[interactionCreate] Handling exp-edit-level-exp-select-*');
                await exp.expEditLevelExpSelect(interaction);
                return;
            }
            if (interaction.customId.startsWith('exp-edit-level-role-')) {
                console.log('[interactionCreate] Handling exp-edit-level-role-*');
                await exp.buttons(interaction);
                return;
            }
            // Unified level interface select menus
            if (interaction.customId === 'exp-unified-level-role' || interaction.customId.startsWith('exp-unified-edit-role-')) {
                console.log('[interactionCreate] Handling unified level role select');
                await exp.buttons(interaction);
                return;
            }
            if (interaction.customId === 'exp-unified-level-exp' || interaction.customId.startsWith('exp-unified-edit-exp-')) {
                console.log('[interactionCreate] Handling unified level exp select');
                await exp.buttons(interaction);
                return;
            }
            if (interaction.customId === 'exp-unified-level-icon' || interaction.customId.startsWith('exp-unified-edit-icon-')) {
                console.log('[interactionCreate] Handling unified level icon select');
                await exp.buttons(interaction);
                return;
            }
            if (interaction.customId === 'exp-levels-channel-select') {
                await exp.select(interaction);
                return;
            }
            if (interaction.customId === 'exp-levels-channel-back') {
                await exp.expLevelChannelBack(interaction);
                return;
            }
            if (interaction.customId === 'items-notification-channel-select') {
                const command17 = require('../commands/utility/17.js');
                await command17.channelSelect(interaction);
                return;
            }
            console.log('[interactionCreate] SelectMenu triggered:', interaction.customId, interaction.values);
            const logsHandlers = require('../commands/utility/logs.js');
            try {
                if (interaction.customId === '17-thread-select') {
                    const opener = require('../commands/utility/opener.js');
                    await opener.threadSelect(interaction);
                    return;
                }
                if (interaction.customId.startsWith('logs-select')) {
                    console.log('[interactionCreate] About to call logs.selectModule');
                    await logsHandlers.selectModule(interaction);
                } else if (interaction.customId.startsWith('logs-channel-select')) {
                    console.log('[interactionCreate] About to call logs.selectChannel');
                    await logsHandlers.selectChannel(interaction);
                } else if (interaction.customId.startsWith('logs-core-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectCoreEvents');
                    await logsHandlers.selectCoreEvents(interaction);
                } else if (interaction.customId.startsWith('logs-specialty-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectSpecialtyEvents');
                    await logsHandlers.selectSpecialtyEvents(interaction);
                } else if (interaction.customId.startsWith('logs-owner-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectOwnerEvents');
                    await logsHandlers.selectOwnerEvents(interaction);

                } else if (interaction.customId.startsWith('logs-event-select')) {
                    console.log('[interactionCreate] About to call logs.selectEvents (legacy)');
                    console.log('[interactionCreate] logs.selectEvents is:', logsHandlers.selectEvents);
                    if (typeof logsHandlers.selectEvents === 'function') {
                        await logsHandlers.selectEvents(interaction);
                        console.log('[interactionCreate] logs.selectEvents returned');
                    } else {
                        console.error('[interactionCreate] logs.selectEvents is not a function!');
                        logger.error('interactionCreate', 'logs.selectEvents is not a function!', interaction.client);
                    }
                } else if (interaction.customId.startsWith('status-')) {
                    // Only owner can access status feature
                    if (interaction.user.id === process.env.OWNER) {
                        const container = await ownerStatus.handleStatusSelect(interaction);
                        if (container) {
                            await interaction.update({
                                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                                components: [container]
                            });
                        }
                        // If container is null, a modal was shown, so do nothing else!
                        return;
                    } else {
                        await interaction.reply({ content: 'who r u? no.', ephemeral: true });
                        return;
                    }
                } else {
                    // Existing sticky and other feature routing
                    if (['sticky-nick-select', 'sticky-role-select'].includes(interaction.customId)) {
                        await sticky.select(interaction);
                        return;
                    }
                    if (interaction.customId === 'exp-subcomponent-select') {
                        await exp.select(interaction);
                        return;
                    }
                    if (interaction.customId === 'exp-levels-config') {
                        await exp.select(interaction);
                        return;
                    }
                    if (interaction.customId === 'exp-text-config') {
                        await exp.select(interaction);
                        return;
                    }
                    if (interaction.customId === 'exp-voice-config') {
                        await exp.select(interaction);
                        return;
                    }
                    const [command, ...args] = interaction.customId.split("-");
                    if (interaction.customId === 'owner-features') {
                        const owner = require('../commands/utility/owner.js');
                        await owner.select(interaction, args);
                        return;
                    }
                    if (interaction.customId === 'changelog-select') {
                        const changelog = require('../commands/utility/changelog.js');
                        await changelog.select(interaction, args);
                        return;
                    }
                    if (interaction.customId === 'owner-joinnotification') {
                        const owner = require('../commands/utility/owner.js');
                        await owner.joinNotificationSelect(interaction);
                        return;
                    }
                    if (interaction.customId === 'owner-itemnotification') {
                        const owner = require('../commands/utility/owner.js');
                        await owner.itemNotificationSelect(interaction);
                        return;
                    }
                    if (interaction.customId === 'clear-data-action') {
                        const clearData = require('../commands/utility/clearData.js');
                        await clearData.select(interaction);
                        return;
                    }
                    if (interaction.customId === 'you-hub-select' || interaction.customId === 'you-feature-select' || interaction.customId === 'you-settings-select' || interaction.customId === 'you-inventory-item-select' || interaction.customId === 'you-inventory-type-select') {
                        const you = require('../commands/utility/you.js');
                        await you.select(interaction, args);
                        return;
                    }
                    // Items select menus
                    if (['items-config-select', 'items-type-select', 'items-rarity-select', 'items-image-select', 'items-drop-locations-select'].includes(interaction.customId) ||
                        interaction.customId.startsWith('items-select-') || // Handle items-select-owner and items-select-guild
                        interaction.customId.startsWith('items-param-select-')) {
                        const items = require('../commands/utility/items.js');
                        await items.select(interaction, args);
                        return;
                    }
                    // Global levels select menus
                    if (interaction.customId === 'global-levels-action' ||
                        interaction.customId === 'global-level-config-select' ||
                        interaction.customId === 'global-level-exp-select' ||
                        interaction.customId === 'global-level-icon-select' ||
                        interaction.customId === 'global-prestige-icon-select' ||
                        interaction.customId === 'global-level-edit-config-select' ||
                        interaction.customId === 'global-level-edit-exp-select' ||
                        interaction.customId === 'global-level-edit-icon-select' ||
                        interaction.customId === 'global-level-edit-prestige-icon-select' ||
                        // Reward select menus
                        interaction.customId === 'global-level-items-select' ||
                        interaction.customId === 'global-level-edit-items-select' ||
                        interaction.customId === 'global-level-xp-booster-select' ||
                        interaction.customId === 'global-level-edit-xp-booster-select' ||
                        interaction.customId === 'global-level-drop-booster-select' ||
                        interaction.customId === 'global-level-edit-drop-booster-select' ||
                        interaction.customId === 'global-level-stars-select' ||
                        interaction.customId === 'global-level-edit-stars-select') {
                        const owner = require('../commands/utility/owner.js');
                        await owner.select(interaction, args);
                        return;
                    }

                    if (client.commands.get(command)?.select) client.commands.get(command)?.select(interaction, args);
                }
            } catch (err) {
                console.error('[interactionCreate] Error handling select menu:', err);
                logger.error('interactionCreate', `Error handling select menu ${interaction.customId}: ${err.message}`, interaction.client);
                if (!interaction.replied) {
                    await interaction.reply({ content: 'An error occurred.', ephemeral: true });
                }
            }
        }
        else if (interaction.isModalSubmit()) {
            interactionCreateMetrics.modalsHandled++;
            if (interaction.customId === 'owner-joinnotification-modal') {
                const owner = require('../commands/utility/owner.js');
                await owner.joinNotificationModal(interaction);
                return;
            }
            if (interaction.customId === 'owner-itemnotification-modal') {
                const owner = require('../commands/utility/owner.js');
                await owner.itemNotificationModal(interaction);
                return;
            }
            // Handle items modals
            if (interaction.customId.startsWith('items-') && interaction.customId.includes('-modal')) {
                // Check if it's a DM message template modal (handled by 17.js)
                if (interaction.customId === 'items-dm-message-template-modal') {
                    const command17 = require('../commands/utility/17.js');
                    await command17.modalSubmit(interaction);
                    return;
                }
                // Other items modals handled by items.js
                const items = require('../commands/utility/items.js');
                await items.modalSubmit(interaction);
                return;
            }
            // Handle status modals FIRST (before exp modals)
            if (interaction.customId.startsWith('status-msg-modal') || interaction.customId.startsWith('owner-status-msg-modal')) {
                console.log('[interactionCreate] Status modal submission detected:', interaction.customId);
                // Only owner can access status feature
                if (interaction.user.id === process.env.OWNER) {
                    try {
                        console.log('[interactionCreate] Owner verified, processing modal');
                        const container = await ownerStatus.handleStatusModal(interaction);
                        console.log('[interactionCreate] Container received, updating interaction');
                        await interaction.update({
                            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                            components: [container]
                        });
                        console.log('[interactionCreate] Interaction updated successfully');
                        return;
                    } catch (error) {
                        console.error('[interactionCreate] Error processing status modal:', error);
                        await interaction.reply({ content: 'An error occurred while processing the status message.', ephemeral: true });
                        return;
                    }
                } else {
                    console.log('[interactionCreate] Non-owner tried to access status modal');
                    await interaction.reply({ content: 'who r u? no.', ephemeral: true });
                    return;
                }
            }

            // Handle dehoist modals
            if (interaction.customId === 'dehoist-editchars-modal') {
                const dehoist = require('../commands/utility/dehoist.js');
                await dehoist.modal(interaction);
                return;
            }

            // Handle global levels modals
            if (interaction.customId.startsWith('global-level-')) {
                const owner = require('../commands/utility/owner.js');
                await owner.modalSubmit(interaction);
                return;
            }

            // Handle EXP modals (fallback for any remaining modals)
            await exp.modalSubmit(interaction);
            return;

        }
        else if (interaction.isChannelSelectMenu()) {
            console.log('[interactionCreate] Channel select menu triggered:', interaction.customId);
            const [command, ...args] = interaction.customId.split("-");
            if (client.commands.get(command)?.threadSelect) {
                await client.commands.get(command).threadSelect(interaction, args);
            }
        }
        // Handle both slash commands and context menu commands
        if (!interaction.isChatInputCommand() && !interaction.isContextMenuCommand()) return;

        interactionCreateMetrics.commandsExecuted++;

        const command = interaction.client.commands.get(interaction.commandName);

        if (!command) {
            console.error(`No command matching ${interaction.commandName} was found.`);
            return;
        }

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error(error);
            // Only try to respond if the command hasn't already handled the error
            try {
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp({
                        content: 'There was an error while executing this command!',
                        flags: MessageFlags.Ephemeral
                    });
                } else {
                    await interaction.reply({
                        content: 'There was an error while executing this command!',
                        flags: MessageFlags.Ephemeral
                    });
                }
            } catch (responseError) {
                console.error('[interactionCreate] Failed to send error response:', responseError);
                // Don't throw here - just log the error
            }
        }

        } catch (error) {
            console.error('[interactionCreate] ❌ Error processing interaction:', error);
        } finally {
            // Track performance metrics
            const duration = Date.now() - startTime;
            if (interactionCreateMetrics.verboseLogging || duration > 100) {
                console.log(`[interactionCreate] ✅ Interaction processed in ${duration}ms (type: ${interaction.type})`);
            }
        }
    },

    // Enhanced optimization functions
    getCachedInteractionValidation,
    getCachedCommand,
    getInteractionCreateStats,
    performanceCleanupAndOptimization,
    clearAllInteractionCreateCaches
};

/**
 * Get comprehensive interaction create performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive interaction create performance data
 */
function getInteractionCreateStats() {
    const cacheHitRate = interactionCreateMetrics.cacheHits + interactionCreateMetrics.cacheMisses > 0 ?
        (interactionCreateMetrics.cacheHits / (interactionCreateMetrics.cacheHits + interactionCreateMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: interactionCreateMetrics.cacheHits,
            cacheMisses: interactionCreateMetrics.cacheMisses,
            databaseQueries: interactionCreateMetrics.databaseQueries,
            averageQueryTime: `${interactionCreateMetrics.averageQueryTime.toFixed(2)}ms`,
            interactionsProcessed: interactionCreateMetrics.interactionsProcessed,
            commandsExecuted: interactionCreateMetrics.commandsExecuted,
            buttonsHandled: interactionCreateMetrics.buttonsHandled,
            modalsHandled: interactionCreateMetrics.modalsHandled,
            selectMenusHandled: interactionCreateMetrics.selectMenusHandled,
            invalidatedInteractions: interactionCreateMetrics.invalidatedInteractions,
            lastOptimization: new Date(interactionCreateMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            command: commandCache.getStats(),
            interactionValidation: interactionValidationCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            command: commandCache.getStats().memoryUsage,
            interactionValidation: interactionValidationCache.getStats().memoryUsage,
            total: commandCache.getStats().memoryUsage + interactionValidationCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            invalidationRate: interactionCreateMetrics.interactionsProcessed > 0 ?
                `${(interactionCreateMetrics.invalidatedInteractions / interactionCreateMetrics.interactionsProcessed * 100).toFixed(2)}%` : '0%'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    interactionCreateMetrics.lastOptimization = Date.now();

    const stats = getInteractionCreateStats();
    if (interactionCreateMetrics.verboseLogging) {
        console.log(`[interactionCreate] 📊 Performance Report:`);
        console.log(`[interactionCreate]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[interactionCreate]   Interactions Processed: ${stats.performance.interactionsProcessed}`);
        console.log(`[interactionCreate]   Commands Executed: ${stats.performance.commandsExecuted}`);
        console.log(`[interactionCreate]   Buttons Handled: ${stats.performance.buttonsHandled}`);
        console.log(`[interactionCreate]   Modals Handled: ${stats.performance.modalsHandled}`);
        console.log(`[interactionCreate]   Select Menus Handled: ${stats.performance.selectMenusHandled}`);
        console.log(`[interactionCreate]   Invalidated Interactions: ${stats.performance.invalidatedInteractions}`);
        console.log(`[interactionCreate]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[interactionCreate]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[interactionCreate]   System Health: ${stats.systemHealth.status}`);
        console.log(`[interactionCreate]   Invalidation Rate: ${stats.systemHealth.invalidationRate}`);
    }

    return stats;
}

/**
 * Clear all interaction create caches (Enterprise-Grade Cache Management)
 */
function clearAllInteractionCreateCaches() {
    commandCache.clear();
    interactionValidationCache.clear();

    console.log('[interactionCreate] 🗑️ Cleared all interaction create caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, interactionCreateMetrics.performanceReportInterval);