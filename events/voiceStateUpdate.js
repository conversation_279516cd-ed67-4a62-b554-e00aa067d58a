const { Events, MessageFlags } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFind, optimizedFindOne, optimizedUpdateOne, optimizedInsertOne, optimizedDeleteOne } = require("../utils/database-optimizer.js");
const { defaults } = require("../utils/default_db_structures.js");
const { sendExpLevelUpLog, sendExpVoiceSessionLog, sendLogContainer } = require("../utils/sendLog.js");
const { createVoiceStateContainer } = require('../utils/logContainers.js');
const { logger } = require('../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Voice State Update Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes voice processing with comprehensive caching and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring for voice operations
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const voiceStateUpdateMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    voiceStateChanges: 0,
    voiceSessionsProcessed: 0,
    voiceExpCalculations: 0,
    voiceSessionsCreated: 0,
    voiceSessionsEnded: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for voice operations
const voiceGuildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild voice configurations
const voiceSessionCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for voice session data
const voiceMemberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member voice data

// Register caches for global cleanup
registerCache(voiceGuildConfigCache);
registerCache(voiceSessionCache);
registerCache(voiceMemberDataCache);

/**
 * Get cached voice guild configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Voice guild configuration data
 */
async function getCachedVoiceGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `voice_guild_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = voiceGuildConfigCache.get(cacheKey);
        if (cached) {
            voiceStateUpdateMetrics.cacheHits++;
            if (voiceStateUpdateMetrics.verboseLogging) {
                console.log(`[voiceStateUpdate] ⚡ Voice guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        voiceStateUpdateMetrics.cacheMisses++;
        voiceStateUpdateMetrics.databaseQueries++;

        // Get guild EXP configuration
        const guildData = await optimizedFindOne("guilds", { id: guildId });
        if (!guildData?.exp?.enabled) return null;

        const voiceEnabled = guildData?.exp?.voice?.enabled ?? true;
        if (!voiceEnabled) return null;

        // Cache the result
        voiceGuildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        voiceStateUpdateMetrics.averageQueryTime =
            (voiceStateUpdateMetrics.averageQueryTime * (voiceStateUpdateMetrics.databaseQueries - 1) + duration) /
            voiceStateUpdateMetrics.databaseQueries;

        if (voiceStateUpdateMetrics.verboseLogging || duration > 100) {
            console.log(`[voiceStateUpdate] ✅ Voice guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error('[voiceStateUpdate] ❌ Error getting voice guild config:', error);
        return null;
    }
}

/**
 * Get cached voice session data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for voice session tracking
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @param {string} channelId - Channel ID
 * @returns {Promise<Object|null>} Voice session data
 */
async function getCachedVoiceSession(guildId, userId, channelId) {
    const startTime = Date.now();
    const cacheKey = `voice_session_${guildId}_${userId}_${channelId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = voiceSessionCache.get(cacheKey);
        if (cached) {
            voiceStateUpdateMetrics.cacheHits++;
            if (voiceStateUpdateMetrics.verboseLogging) {
                console.log(`[voiceStateUpdate] ⚡ Voice session cache hit for ${userId} in ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        voiceStateUpdateMetrics.cacheMisses++;
        voiceStateUpdateMetrics.databaseQueries++;

        // Get session data
        const sessionData = await optimizedFindOne("voice_sessions", {
            guildId: guildId,
            userId: userId,
            channelId: channelId
        });

        // Cache the result (even if null)
        voiceSessionCache.set(cacheKey, sessionData);

        const duration = Date.now() - startTime;
        voiceStateUpdateMetrics.averageQueryTime =
            (voiceStateUpdateMetrics.averageQueryTime * (voiceStateUpdateMetrics.databaseQueries - 1) + duration) /
            voiceStateUpdateMetrics.databaseQueries;

        if (voiceStateUpdateMetrics.verboseLogging || duration > 100) {
            console.log(`[voiceStateUpdate] ✅ Voice session fetched for ${userId} in ${guildId}: ${duration}ms - cached for future access`);
        }

        return sessionData;
    } catch (error) {
        console.error('[voiceStateUpdate] ❌ Error getting voice session:', error);
        return null;
    }
}

/**
 * Get cached voice member data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for member voice data
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} Member voice data
 */
async function getCachedVoiceMemberData(guildId, userId) {
    const startTime = Date.now();
    const cacheKey = `voice_member_${guildId}_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = voiceMemberDataCache.get(cacheKey);
        if (cached) {
            voiceStateUpdateMetrics.cacheHits++;
            if (voiceStateUpdateMetrics.verboseLogging) {
                console.log(`[voiceStateUpdate] ⚡ Voice member data cache hit for ${userId} in ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        voiceStateUpdateMetrics.cacheMisses++;
        voiceStateUpdateMetrics.databaseQueries++;

        // Get member data for total EXP
        const memberData = await optimizedFindOne("member", {
            guildId: guildId,
            userId: userId
        });

        // Cache the result (even if null)
        voiceMemberDataCache.set(cacheKey, memberData);

        const duration = Date.now() - startTime;
        voiceStateUpdateMetrics.averageQueryTime =
            (voiceStateUpdateMetrics.averageQueryTime * (voiceStateUpdateMetrics.databaseQueries - 1) + duration) /
            voiceStateUpdateMetrics.databaseQueries;

        if (voiceStateUpdateMetrics.verboseLogging || duration > 100) {
            console.log(`[voiceStateUpdate] ✅ Voice member data fetched for ${userId} in ${guildId}: ${duration}ms - cached for future access`);
        }

        return memberData;
    } catch (error) {
        console.error('[voiceStateUpdate] ❌ Error getting voice member data:', error);
        return null;
    }
}

// Helper function to send messages with proper @silent handling
async function sendMessage(channel, content) {
	const isSilent = content.startsWith('@silent ');
	const messageContent = isSilent ? content.replace('@silent ', '') : content;
	const options = {
		content: messageContent,
		allowedMentions: { parse: [] } // Prevent pings but show mentions visually
	};

	if (isSilent) {
		options.flags = MessageFlags.SuppressNotifications;
	}

	// Use smart routing for forum channels and regular channels
	const { sendToLogChannel } = require('../commands/utility/logs.js');
	try {
		return await sendToLogChannel(channel, options, 'voiceStateUpdate');
	} catch (error) {
		console.error(`[sendMessage] Error sending to channel ${channel.name}:`, error);
		throw error;
	}
}

// Voice EXP Handler - Periodic checking system
async function processVoiceExp(client) {
    if (!mongoClient.clientConnected) return;

    try {
        // Get all guilds with voice EXP enabled

        // Get guilds with global EXP enabled
        const guildsWithGlobalExp = await optimizedFind("guilds", {
            "exp.enabled": true
        });

        // Filter for voice EXP enabled (defaults to true if not set)
        const guildsWithVoiceExp = guildsWithGlobalExp.filter(guild => {
            const voiceEnabled = guild.exp?.voice?.enabled ?? true;
            return voiceEnabled;
        });



        for (const guildData of guildsWithVoiceExp) {
            const guild = client.guilds.cache.get(guildData.id);
            if (!guild) continue;

            // Get voice EXP config with defaults
            const voiceConfig = guildData.exp.voice || {};
            const expPerMin = voiceConfig.expPerMin ?? 2;
            const cooldown = voiceConfig.cooldown ?? 1;

            for (const [, channel] of guild.channels.cache) {
                if (channel.type !== 2) continue; // Only voice channels

                const members = channel.members;
                if (members.size <= 1) {
                    // Only log if there's actually someone in the channel (to reduce spam)
                    if (members.size === 1) {
                        console.log(`[voiceExp] Skipping channel ${channel.name} - user is alone`);
                        logger.debug('voiceExp', `Skipping channel ${channel.name} - user is alone`, client);
                    }
                    continue; // Skip if alone or empty
                }

                console.log(`[voiceExp] Processing channel ${channel.name} with ${members.size} members`);

                for (const [userId, member] of members) {
                    // Skip bots
                    if (member.user.bot) {
                        continue;
                    }

                    // Check if user is muted/deafened
                    if (member.voice.mute || member.voice.deaf) {
                        continue;
                    }

                    // Check if at least one other user is not muted/deafened
                    let hasActiveUser = false;
                    let activeUsers = [];
                    for (const [otherUserId, otherMember] of members) {
                        if (otherUserId === userId) continue;
                        if (!otherMember.user.bot && !otherMember.voice.mute && !otherMember.voice.deaf) {
                            hasActiveUser = true;
                            activeUsers.push(otherMember.user.username);
                        }
                    }

                    if (!hasActiveUser) {
                        // Only log if this is unexpected (user thinks they should get EXP)
                        logger.debug('voiceExp', `Skipping ${member.user.username} - no active users in channel ${channel.name}`, client);
                        continue;
                    }

                    // Log when someone is eligible for EXP (this will help catch bugs)
                    logger.debug('voiceExp', `${member.user.username} eligible for EXP in ${channel.name} with active users: [${activeUsers.join(', ')}]`, client);

                    // Check cooldown from database
                    let memberData = await optimizedFindOne("member", { guildId: guild.id, userId });

                    if (!memberData) {
                        // Create new member data using defaults
                        await optimizedInsertOne("member", defaults.member({ guildId: guild.id, userId }));
                        memberData = await optimizedFindOne("member", { guildId: guild.id, userId });
                    }

                    // Ensure exp structure exists with voice stats
                    if (!memberData.exp) {
                        memberData.exp = {
                            total: 0,
                            lastText: 0,
                            lastVoice: 0,
                            voice: {
                                total: 0,
                                timeSpent: 0,
                                longestSession: 0,
                                sessionCount: 0,
                                lastActiveDay: null,
                                currentStreak: 0
                            },
                            text: {
                                total: 0,
                                messagesSent: 0,
                                messagesCounted: 0,
                                lastActiveDay: null,
                                currentStreak: 0
                            }
                        };
                        await optimizedUpdateOne("member",
                            { guildId: guild.id, userId },
                            { $set: { exp: memberData.exp } }
                        );
                    }

                    // Ensure voice stats structure exists
                    if (!memberData.exp.voice) {
                        await optimizedUpdateOne("member",
                            { guildId: guild.id, userId },
                            { $set: {
                                "exp.voice": {
                                    total: 0,
                                    timeSpent: 0,
                                    longestSession: 0,
                                    sessionCount: 0,
                                    lastActiveDay: null,
                                    currentStreak: 0
                                }
                            }}
                        );
                    }

                    // Check voice cooldown
                    const now = Date.now();
                    const lastVoice = memberData.exp.lastVoice || 0;
                    const msCooldown = cooldown * 60 * 1000;
                    const timeSinceLastGain = now - lastVoice;

                    if (timeSinceLastGain < msCooldown) {
                        continue;
                    }

                    console.log(`[voiceExp] 🎯 AWARDING EXP to ${member.user.username} - ${expPerMin} exp (cooldown met: ${Math.round(timeSinceLastGain/1000)}s >= ${Math.round(msCooldown/1000)}s)`);
                    logger.debug('voiceExp', `🎯 AWARDING EXP to ${member.user.username} - ${expPerMin} exp (cooldown met: ${Math.round(timeSinceLastGain/1000)}s >= ${Math.round(msCooldown/1000)}s)`, client);

                    // Calculate time spent (cooldown period)
                    const timeSpentThisPeriod = Math.min(timeSinceLastGain, msCooldown);

                    // Calculate streak
                    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
                    const voiceStats = memberData.exp.voice || {};
                    let newStreak = voiceStats.currentStreak || 0;

                    if (voiceStats.lastActiveDay !== today) {
                        // Check if this continues the streak
                        const yesterday = new Date();
                        yesterday.setDate(yesterday.getDate() - 1);
                        const yesterdayStr = yesterday.toISOString().split('T')[0];

                        if (voiceStats.lastActiveDay === yesterdayStr) {
                            // Continue streak
                            newStreak += 1;
                        } else if (voiceStats.lastActiveDay === null || voiceStats.lastActiveDay < yesterdayStr) {
                            // Start new streak
                            newStreak = 1;
                        }
                        // If lastActiveDay is today, keep current streak (shouldn't happen due to check above)
                    }

                    // Award EXP and update voice stats
                    await optimizedUpdateOne("member",
                        { guildId: guild.id, userId },
                        {
                            $inc: {
                                "exp.total": expPerMin,
                                "exp.voice.total": expPerMin,
                                "exp.voice.timeSpent": timeSpentThisPeriod
                            },
                            $set: {
                                "exp.lastVoice": now,
                                "exp.voice.lastActiveDay": today,
                                "exp.voice.currentStreak": newStreak
                            }
                        }
                    );

                    // Update local memberData for level calculation
                    const oldTotal = memberData.exp.total;
                    memberData.exp.total += expPerMin;

                    // Award global XP alongside guild XP
                    try {
                        const { awardGlobalExp } = require('../utils/globalLevels.js');
                        const globalResult = await awardGlobalExp(userId, expPerMin, 'VOICE');

                        if (globalResult.leveledUp) {
                            console.log(`[voiceExp] 🌟 GLOBAL LEVEL UP! ${member.user.username} reached global level ${globalResult.newLevel}!`);

                            // Process global level-up notifications
                            const { processGlobalLevelUp } = require('../utils/globalLevelNotifications.js');
                            await processGlobalLevelUp(userId, globalResult, client);
                        }
                    } catch (globalExpError) {
                        console.error('[voiceExp] Error awarding global XP:', globalExpError);
                        // Don't break the flow if global XP fails
                    }

                    // Process item drops from voice EXP gain
                    try {
                        const { processItemDrops, sendItemDropDM, sendGuildChannelNotification } = require('../utils/itemDrops.js');
                        const droppedItems = await processItemDrops(
                            userId,
                            guild.id,
                            'VOICE',
                            expPerMin
                        );

                        if (droppedItems.length > 0) {
                            console.log(`[voiceExp] 🎁 ${member.user.username} found ${droppedItems[0].itemName} from voice EXP!`);

                            // Send guild channel notification (no pings)
                            try {
                                await sendGuildChannelNotification(
                                    userId,
                                    guild.id,
                                    droppedItems,
                                    'VOICE',
                                    client
                                );
                            } catch (guildNotifError) {
                                console.error('[voiceExp] Error sending guild channel notification:', guildNotifError);
                            }

                            // Send DM notification
                            const dmResult = await sendItemDropDM(
                                userId,
                                guild.id,
                                droppedItems,
                                'VOICE',
                                client
                            );

                            // No fallback message - only guild notifications and DMs
                        }
                    } catch (error) {
                        console.error('[voiceExp] Error processing item drops:', error);
                    }

                    // Check for level up (same logic as text EXP system)
                    const levels = guildData.exp.levels ?? [];
                    let newLevelIndex = -1;
                    for (let i = levels.length - 1; i >= 0; i--) {
                        if (memberData.exp.total >= levels[i].exp) {
                            newLevelIndex = i;
                            break;
                        }
                    }

                    // If user qualifies for a level and doesn't already have the role
                    if (newLevelIndex >= 0) {
                        const level = levels[newLevelIndex];
                        const roleId = level.roleId;

                        if (!member.roles.cache.has(roleId)) {
                            // Check if this is a "catch-up" role assignment (user already had enough EXP)
                            const isLevelUp = memberData.exp.total - expPerMin < level.exp;

                            // FIXED: Check if user was already at maximum level before this EXP gain
                            const { getCachedLevelCalculation } = require('../utils/expCache.js');
                            const oldLevelCalc = getCachedLevelCalculation(memberData.exp.total - expPerMin, levels);
                            const wasAtMaxLevel = oldLevelCalc.isAtMaxLevel;

                            // Assign role
                            try {
                                await member.roles.add(roleId);
                                // FIXED: Only process level-up notifications if user wasn't already at max level
                                if (isLevelUp && !wasAtMaxLevel) {
                                    console.log(`[voiceExp] 🎉 LEVEL UP! ${member.user.username} reached level ${newLevelIndex + 1} (${memberData.exp.total} EXP) - Role assigned!`);
                                    logger.info('voiceExp', `🎉 LEVEL UP! ${member.user.username} reached level ${newLevelIndex + 1} (${memberData.exp.total} EXP) - Role assigned!`, client);

                                    // Send level up log
                                    await sendExpLevelUpLog(
                                        guild.id,
                                        userId,
                                        newLevelIndex + 1,
                                        memberData.exp.total,
                                        roleId,
                                        client
                                    );
                                } else if (isLevelUp && wasAtMaxLevel) {
                                    // FIXED: Log when level-up is skipped due to maximum level
                                    console.log(`[voiceExp] ⚠️  Level-up skipped for ${member.user.username}: already at maximum guild level ${newLevelIndex + 1} in ${guild.name}`);
                                }
                            } catch (err) {
                                console.error('[voiceExp] Error assigning role:', err);
                            }

                            // FIXED: Only send level up message if this was a real level-up (not at max level)
                            const levelMsgEnabled = guildData.exp.levelMsgEnabled ?? true;
                            const levelChannelId = guildData.exp.levelChannel;

                            if (levelMsgEnabled && levelChannelId && isLevelUp && !wasAtMaxLevel) {
                                try {
                                    const levelChannel = await guild.channels.fetch(levelChannelId);
                                    if (levelChannel && levelChannel.isTextBased()) {
                                        // FIXED: Calculate correct level number accounting for level 0 baseline
                                        const levels = guildData.exp.levels || [];
                                        const displayLevel = levels.length > 0 && levels[0].exp === 0 ? newLevelIndex : newLevelIndex + 1;
                                        const levelMsg = (guildData.exp.levelMsg || '{mention} leveled up to level {level} and received the {role} role.')
                                            .replace('{mention}', `<@${userId}>`)
                                            .replace('{level}', displayLevel.toString())
                                            .replace('{role}', `<@&${roleId}>`)
                                            .replace('{exp}', memberData.exp.total.toLocaleString())
                                            .replace('{server}', guild.name);
                                        await sendMessage(levelChannel, levelMsg);
                                    }
                                } catch (err) {
                                    console.error('[voiceExp] Error sending level up message:', err);
                                }
                            }
                        }
                    }

                    // Track session EXP for message on leave (don't send message here)

                    // Check if this is a new session (document doesn't exist yet)
                    const existingSession = await optimizedFindOne("voice_sessions", {
                        guildId: guild.id,
                        userId,
                        channelId: channel.id
                    });

                    const isNewSession = !existingSession;

                    await optimizedUpdateOne("voice_sessions",
                        { guildId: guild.id, userId, channelId: channel.id },
                        {
                            $inc: { sessionExp: expPerMin },
                            $set: {
                                lastUpdate: now,
                                isNewSession: isNewSession // Set flag based on whether session existed
                            },
                            $setOnInsert: {
                                sessionStart: now
                            }
                        },
                        { upsert: true }
                    );
                }
            }
        }
    } catch (error) {
        console.error('[voiceExp] Error processing voice EXP:', error);
    }
}

// Handle voice EXP message when user leaves voice channel (Enterprise-Grade Optimized)
async function handleVoiceExpOnLeave(oldState, newState) {
    const startTime = Date.now();

    // Check if user left a voice channel
    const leftVoiceChannel = oldState.channelId && !newState.channelId;
    if (!leftVoiceChannel) return;

    // Skip bots
    if (oldState.member.user.bot) return;

    try {
        voiceStateUpdateMetrics.voiceSessionsEnded++;
        voiceStateUpdateMetrics.parallelOperations++;

        // OPTIMIZED: Use Promise.allSettled for parallel data fetching
        const [guildDataResult, sessionDataResult] = await Promise.allSettled([
            getCachedVoiceGuildConfig(oldState.guild.id),
            getCachedVoiceSession(oldState.guild.id, oldState.member.id, oldState.channelId)
        ]);

        // Handle partial failures
        if (guildDataResult.status === 'rejected') {
            console.error('[voiceStateUpdate] ❌ Error getting guild config:', guildDataResult.reason);
            voiceStateUpdateMetrics.partialFailures++;
            return;
        }

        if (sessionDataResult.status === 'rejected') {
            console.error('[voiceStateUpdate] ❌ Error getting session data:', sessionDataResult.reason);
            voiceStateUpdateMetrics.partialFailures++;
            return;
        }

        const guildData = guildDataResult.value;
        const sessionData = sessionDataResult.value;

        if (!guildData?.exp?.enabled) return;
        const voiceEnabled = guildData?.exp?.voice?.enabled ?? true;
        if (!voiceEnabled) return;

        const voiceConfig = guildData.exp.voice || {};
        const msgEnabled = voiceConfig.msgEnabled ?? true;
        if (!msgEnabled) return; // Message disabled

        if (!sessionData || !sessionData.sessionExp || sessionData.sessionExp <= 0) return;

        // Find the voice channel's text channel
        const voiceChannel = oldState.channel;
        if (!voiceChannel) return;

        let textChannel = null;

        // First, check if the voice channel has text chat enabled
        // Voice channels can send text messages directly if they have text chat enabled
        if (voiceChannel.type === 2 && voiceChannel.permissionsFor(oldState.guild.members.me).has('SendMessages')) {
            try {
                // Test if we can send to this voice channel as text
                textChannel = voiceChannel;
            } catch (e) {
                textChannel = null;
            }
        }

        // If that doesn't work, try to find a separate text channel with the same name
        if (!textChannel) {
            textChannel = oldState.guild.channels.cache.find(channel =>
                channel.type === 0 && // Regular text channel
                channel.name.toLowerCase() === voiceChannel.name.toLowerCase()
            );
        }

        // If no exact match, try to find text channel with similar name (without spaces/dashes)
        if (!textChannel) {
            const voiceNameClean = voiceChannel.name.toLowerCase().replace(/[\s-_]/g, '');
            textChannel = oldState.guild.channels.cache.find(channel =>
                channel.type === 0 && // Regular text channel
                channel.name.toLowerCase().replace(/[\s-_]/g, '') === voiceNameClean
            );
        }

        // If still no text channel found, skip message
        if (!textChannel || !textChannel.isTextBased()) {
            // Clean up session data
            await optimizedDeleteOne("voice_sessions", {
                guildId: oldState.guild.id,
                userId: oldState.member.id,
                channelId: oldState.channelId
            });
            return;
        }

        // Send EXP gain message
        const msg = voiceConfig.msg ?? '{mention} spent {duration} in voice and earned {exp} exp.';

        // Calculate session duration for message
        const sessionDurationMs = sessionData.sessionStart ?
            (Date.now() - sessionData.sessionStart) : 0;
        const sessionDurationMinutes = Math.round(sessionDurationMs / (60 * 1000));

        // Format duration for display (e.g., "2m", "1h23m", "2h")
        const { formatDuration } = require('../utils/statsUtils.js');
        const formattedDuration = formatDuration(sessionDurationMs);

        // Get member data for total EXP
        const memberData = await optimizedFindOne("member", {
            guildId: oldState.guild.id,
            userId: oldState.member.id
        });
        const totalExp = memberData?.exp?.total || 0;

        const expMsg = msg
            .replace('{mention}', `<@${oldState.member.id}>`)
            .replace('{exp}', sessionData.sessionExp)
            .replace('{duration}', formattedDuration)
            .replace('{totalexp}', totalExp.toLocaleString())
            .replace('{server}', oldState.guild.name);

        await sendMessage(textChannel, expMsg);

        // Smart validation: Check if session start predates bot's last startup
        // If the session started before the bot was online, it's definitely stale
        const botStartTime = process.uptime() * 1000; // Bot uptime in milliseconds
        const botStartedAt = Date.now() - botStartTime;

        if (sessionData.sessionStart < botStartedAt) {
            console.log(`[voiceExp] WARNING: Session for ${oldState.member.user.username} started before bot startup (session: ${new Date(sessionData.sessionStart).toISOString()}, bot started: ${new Date(botStartedAt).toISOString()}). Cleaning up stale data...`);
            // Clean up the stale session and skip processing
            await optimizedDeleteOne("voice_sessions", {
                guildId: oldState.guild.id,
                userId: oldState.member.id,
                channelId: oldState.channelId
            });
            return;
        }

        // Update member voice stats if this was a session with EXP
        if (sessionData.sessionExp > 0) {


            // Get current member data to check longest session
            const memberData = await optimizedFindOne("member", {
                guildId: oldState.guild.id,
                userId: oldState.member.id
            });

            if (memberData) {
                const currentLongest = memberData.exp?.voice?.longestSession || 0;
                const newLongest = Math.max(currentLongest, sessionDurationMs);

                const updateData = {
                    $set: {
                        "exp.voice.longestSession": newLongest
                    }
                };

                // Increment session count if this was a new session
                if (sessionData.isNewSession) {
                    updateData.$inc = { "exp.voice.sessionCount": 1 };
                }

                await optimizedUpdateOne("member",
                    { guildId: oldState.guild.id, userId: oldState.member.id },
                    updateData
                );
            }
        }

        // Send voice session log
        await sendExpVoiceSessionLog(
            oldState.guild.id,
            oldState.member.id,
            sessionData.sessionExp,
            sessionDurationMinutes, // Use the variable calculated earlier
            oldState.client,
            sessionDurationMs // Pass milliseconds for better formatting
        );

        // Clean up session data
        await optimizedDeleteOne("voice_sessions", {
            guildId: oldState.guild.id,
            userId: oldState.member.id,
            channelId: oldState.channelId
        });

    } catch (error) {
        console.error('[voiceExp] Error handling voice leave message:', error);
    }
}

// Handle voice session stats update (independent of messages) (Enterprise-Grade Optimized)
async function handleVoiceSessionStats(oldState, newState) {
    const startTime = Date.now();

    // Check if user left a voice channel
    const leftVoiceChannel = oldState.channelId && !newState.channelId;
    if (!leftVoiceChannel) return;

    // Skip bots
    if (oldState.member.user.bot) return;

    try {
        voiceStateUpdateMetrics.voiceSessionsProcessed++;
        voiceStateUpdateMetrics.parallelOperations++;

        // OPTIMIZED: Use Promise.allSettled for parallel data fetching
        const [guildDataResult, sessionDataResult, memberDataResult] = await Promise.allSettled([
            getCachedVoiceGuildConfig(oldState.guild.id),
            getCachedVoiceSession(oldState.guild.id, oldState.member.id, oldState.channelId),
            getCachedVoiceMemberData(oldState.guild.id, oldState.member.id)
        ]);

        // Handle partial failures
        if (guildDataResult.status === 'rejected') {
            console.error('[voiceStateUpdate] ❌ Error getting guild config:', guildDataResult.reason);
            voiceStateUpdateMetrics.partialFailures++;
            return;
        }

        if (sessionDataResult.status === 'rejected') {
            console.error('[voiceStateUpdate] ❌ Error getting session data:', sessionDataResult.reason);
            voiceStateUpdateMetrics.partialFailures++;
            return;
        }

        if (memberDataResult.status === 'rejected') {
            console.error('[voiceStateUpdate] ❌ Error getting member data:', memberDataResult.reason);
            voiceStateUpdateMetrics.partialFailures++;
            return;
        }

        const guildData = guildDataResult.value;
        const sessionData = sessionDataResult.value;
        const memberData = memberDataResult.value;

        if (!guildData?.exp?.enabled) return;
        const voiceEnabled = guildData?.exp?.voice?.enabled ?? true;
        if (!voiceEnabled) return;

        // Update session stats even if no EXP was gained or messages are disabled
        if (sessionData && sessionData.sessionStart) {
            const sessionDurationMs = Date.now() - sessionData.sessionStart;

            // Smart validation: Check if session start predates bot's last startup
            const botStartTime = process.uptime() * 1000; // Bot uptime in milliseconds
            const botStartedAt = Date.now() - botStartTime;

            if (sessionData.sessionStart < botStartedAt) {
                console.log(`[voiceSessionStats] WARNING: Session for ${oldState.member.user.username} started before bot startup. Skipping stats update...`);
                return;
            }

            if (memberData) {
                const currentLongest = memberData.exp?.voice?.longestSession || 0;
                const newLongest = Math.max(currentLongest, sessionDurationMs);

                const updateData = {
                    $set: {
                        "exp.voice.longestSession": newLongest
                    }
                };

                // Increment session count if this was a new session (regardless of EXP)
                if (sessionData.isNewSession) {
                    updateData.$inc = { "exp.voice.sessionCount": 1 };
                }

                await optimizedUpdateOne("member",
                    { guildId: oldState.guild.id, userId: oldState.member.id },
                    updateData
                );

                console.log(`[voiceSessionStats] Updated session stats for ${oldState.member.user.username}: duration=${Math.round(sessionDurationMs/1000)}s, longest=${Math.round(newLongest/1000)}s, newSession=${sessionData.isNewSession}`);
            }
        }

    } catch (error) {
        console.error('[voiceExp] Error handling voice session stats:', error);
    }
}

// Handle basic voice state logging (separate from EXP)
async function handleBasicVoiceStateLogging(oldState, newState, guildData, client) {
    // Skip if logging is disabled
    if (!guildData?.logs?.enabled) return;

    // Skip bots
    if (oldState.member.user.bot) return;

    try {
        // Determine the action
        let action = '';
        let channelInfo = '';

        if (!oldState.channelId && newState.channelId) {
            // User joined a voice channel
            action = 'Joined voice channel';
            channelInfo = `**channel**: <#${newState.channelId}>`;
        } else if (oldState.channelId && !newState.channelId) {
            // User left a voice channel
            action = 'Left voice channel';
            channelInfo = `**channel**: <#${oldState.channelId}>`;
        } else if (oldState.channelId && newState.channelId && oldState.channelId !== newState.channelId) {
            // User moved between voice channels
            action = 'Moved voice channels';
            channelInfo = `**from**: <#${oldState.channelId}>\n**to**: <#${newState.channelId}>`;
        } else {
            // State change within same channel (mute, deafen, etc.)
            const changes = [];
            if (oldState.mute !== newState.mute) {
                changes.push(newState.mute ? 'Server muted' : 'Server unmuted');
            }
            if (oldState.deaf !== newState.deaf) {
                changes.push(newState.deaf ? 'Server deafened' : 'Server undeafened');
            }
            if (oldState.selfMute !== newState.selfMute) {
                changes.push(newState.selfMute ? 'Self muted' : 'Self unmuted');
            }
            if (oldState.selfDeaf !== newState.selfDeaf) {
                changes.push(newState.selfDeaf ? 'Self deafened' : 'Self undeafened');
            }
            if (oldState.streaming !== newState.streaming) {
                changes.push(newState.streaming ? 'Started streaming' : 'Stopped streaming');
            }
            if (oldState.selfVideo !== newState.selfVideo) {
                changes.push(newState.selfVideo ? 'Enabled camera' : 'Disabled camera');
            }

            if (changes.length === 0) return; // No relevant changes

            action = changes.join(', ');
            channelInfo = `**channel**: <#${newState.channelId}>`;
        }

        // Create Components v2 container for voice state update
        const container = createVoiceStateContainer({
            userMention: `<@${oldState.member.id}>`,
            userTag: oldState.member.user.tag,
            action: action,
            channelInfo: channelInfo
        });

        // Send container to all configured channels
        await sendLogContainer(oldState.guild.id, 'voiceStateUpdate', container, client);

    } catch (error) {
        console.error('[voiceStateLogging] Error handling voice state logging:', error);
    }
}

module.exports = {
    name: Events.VoiceStateUpdate,
    processVoiceExp,
    handleVoiceSessionStats,
    async execute(_client, oldState, newState) {
        const startTime = Date.now();

        try {
            if (!mongoClient.clientConnected) {
                console.log("mongodb not ready yet");
                return;
            }

            voiceStateUpdateMetrics.voiceStateChanges++;

            // OPTIMIZED: Use Promise.allSettled for parallel voice processing
            voiceStateUpdateMetrics.parallelOperations++;

            const [voiceExpResult, voiceStatsResult] = await Promise.allSettled([
                handleVoiceExpOnLeave(oldState, newState),
                handleVoiceSessionStats(oldState, newState)
            ]);

            // Handle partial failures
            if (voiceExpResult.status === 'rejected') {
                console.error('[voiceStateUpdate] ❌ Error handling voice EXP:', voiceExpResult.reason);
                voiceStateUpdateMetrics.partialFailures++;
            }
            if (voiceStatsResult.status === 'rejected') {
                console.error('[voiceStateUpdate] ❌ Error handling voice stats:', voiceStatsResult.reason);
                voiceStateUpdateMetrics.partialFailures++;
            }

        // Ensure guild data exists for other features
        let guildData = await getCachedVoiceGuildConfig(oldState.guild.id);
        if (!guildData) {
            guildData = await optimizedFindOne("guilds", { id: oldState.guild.id });
            if (!guildData) {
                const { defaults } = require("../utils/default_db_structures.js");
                await optimizedInsertOne("guilds", defaults.guild(oldState.guild.id));
                guildData = await optimizedFindOne("guilds", { id: oldState.guild.id });
            }
        }

        // Basic voice state logging (separate from EXP system)
        await handleBasicVoiceStateLogging(oldState, newState, guildData, _client);

        } catch (error) {
            console.error('[voiceStateUpdate] ❌ Error processing voice state update:', error);
        } finally {
            // Track performance metrics
            const duration = Date.now() - startTime;
            if (voiceStateUpdateMetrics.verboseLogging || duration > 100) {
                console.log(`[voiceStateUpdate] ✅ Voice state update processed in ${duration}ms`);
            }
        }
    },

    // Enhanced optimization functions
    getCachedVoiceGuildConfig,
    getCachedVoiceSession,
    getCachedVoiceMemberData,
    getVoiceStateUpdateStats,
    performanceCleanupAndOptimization,
    clearAllVoiceStateUpdateCaches
};

/**
 * Get comprehensive voice state update performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive voice state update performance data
 */
function getVoiceStateUpdateStats() {
    const cacheHitRate = voiceStateUpdateMetrics.cacheHits + voiceStateUpdateMetrics.cacheMisses > 0 ?
        (voiceStateUpdateMetrics.cacheHits / (voiceStateUpdateMetrics.cacheHits + voiceStateUpdateMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: voiceStateUpdateMetrics.cacheHits,
            cacheMisses: voiceStateUpdateMetrics.cacheMisses,
            databaseQueries: voiceStateUpdateMetrics.databaseQueries,
            averageQueryTime: `${voiceStateUpdateMetrics.averageQueryTime.toFixed(2)}ms`,
            voiceStateChanges: voiceStateUpdateMetrics.voiceStateChanges,
            voiceSessionsProcessed: voiceStateUpdateMetrics.voiceSessionsProcessed,
            voiceExpCalculations: voiceStateUpdateMetrics.voiceExpCalculations,
            voiceSessionsCreated: voiceStateUpdateMetrics.voiceSessionsCreated,
            voiceSessionsEnded: voiceStateUpdateMetrics.voiceSessionsEnded,
            parallelOperations: voiceStateUpdateMetrics.parallelOperations,
            partialFailures: voiceStateUpdateMetrics.partialFailures,
            lastOptimization: new Date(voiceStateUpdateMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            voiceGuildConfig: voiceGuildConfigCache.getStats(),
            voiceSession: voiceSessionCache.getStats(),
            voiceMemberData: voiceMemberDataCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            voiceGuildConfig: voiceGuildConfigCache.getStats().memoryUsage,
            voiceSession: voiceSessionCache.getStats().memoryUsage,
            voiceMemberData: voiceMemberDataCache.getStats().memoryUsage,
            total: voiceGuildConfigCache.getStats().memoryUsage +
                   voiceSessionCache.getStats().memoryUsage +
                   voiceMemberDataCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    voiceStateUpdateMetrics.lastOptimization = Date.now();

    const stats = getVoiceStateUpdateStats();
    if (voiceStateUpdateMetrics.verboseLogging) {
        console.log(`[voiceStateUpdate] 📊 Performance Report:`);
        console.log(`[voiceStateUpdate]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[voiceStateUpdate]   Voice State Changes: ${stats.performance.voiceStateChanges}`);
        console.log(`[voiceStateUpdate]   Voice Sessions Processed: ${stats.performance.voiceSessionsProcessed}`);
        console.log(`[voiceStateUpdate]   Voice EXP Calculations: ${stats.performance.voiceExpCalculations}`);
        console.log(`[voiceStateUpdate]   Voice Sessions Created: ${stats.performance.voiceSessionsCreated}`);
        console.log(`[voiceStateUpdate]   Voice Sessions Ended: ${stats.performance.voiceSessionsEnded}`);
        console.log(`[voiceStateUpdate]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[voiceStateUpdate]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[voiceStateUpdate]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[voiceStateUpdate]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[voiceStateUpdate]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Clear all voice state update caches (Enterprise-Grade Cache Management)
 */
function clearAllVoiceStateUpdateCaches() {
    voiceGuildConfigCache.clear();
    voiceSessionCache.clear();
    voiceMemberDataCache.clear();

    console.log('[voiceStateUpdate] 🗑️ Cleared all voice state update caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, voiceStateUpdateMetrics.performanceReportInterval);