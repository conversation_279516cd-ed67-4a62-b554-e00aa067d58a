const { Events } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne, optimizedDeleteMany } = require("../utils/database-optimizer.js");

// Performance monitoring for ready event optimization
const readyEventMetrics = {
    startTime: 0,
    phases: {},
    totalDuration: 0
};

module.exports = {
    name: Events.ClientReady,
    once: true,
    async execute(client) {
        readyEventMetrics.startTime = Date.now();
        console.log(`Ready! Logged in as ${client.user.tag}`);
        console.log('[ready] 🚀 Starting optimized ready event sequence...');

        try {
            // Phase 1: Critical Discord Client Setup (Sequential - Required)
            await initializeCriticalSystems(client);

            // Phase 2: Parallel System Initialization (Independent Operations)
            await initializeSystemsInParallel(client);

            // Phase 3: Finalization and Monitoring
            await finalizeInitialization(client);

            // Generate performance report
            readyEventMetrics.totalDuration = Date.now() - readyEventMetrics.startTime;
            generateReadyEventReport();

        } catch (error) {
            console.error('[ready] ❌ Critical error during ready event:', error);
            // Continue with degraded functionality rather than crashing
        }
    },
};

// Phase 1: Critical Discord Client Setup (Must be sequential)
async function initializeCriticalSystems(client) {
    const phaseStart = Date.now();

    try {
        // Fetch slash commands and store their mention strings
        const commandsStart = Date.now();
        try {
            const commands = await client.application.commands.fetch();
            const slashCommands = commands.filter(cmd => cmd.type === 1); // CHAT_INPUT
            const slashMentions = slashCommands.map(cmd => `</${cmd.name}:${cmd.id}>`);
            client._slashCommandMentions = slashMentions.join(' ');
            console.log(`[ready] ✅ Loaded ${slashCommands.size || 0} slash commands for mentions`);
        } catch (e) {
            client._slashCommandMentions = '';
            console.error('[ready] ⚠️  Failed to fetch slash command mentions:', e.message);
        }
        const commandsDuration = Date.now() - commandsStart;

        // Initialize client presence data
        const presenceStart = Date.now();
        await initializeClientPresence(client);
        const presenceDuration = Date.now() - presenceStart;

        // Initialize refresh threads system (Critical for bot functionality)
        const refreshStart = Date.now();
        await initializeRefreshThreadsSystem(client);
        const refreshDuration = Date.now() - refreshStart;

        readyEventMetrics.phases.critical = {
            duration: Date.now() - phaseStart,
            commands: commandsDuration,
            presence: presenceDuration,
            refresh: refreshDuration
        };

        console.log(`[ready] ✅ Phase 1: Critical systems initialized in ${readyEventMetrics.phases.critical.duration}ms`);

    } catch (error) {
        console.error('[ready] ❌ Error in critical systems initialization:', error.message);
        throw error; // Critical systems must succeed
    }
}

// Phase 2: Parallel System Initialization (Independent operations for maximum performance)
async function initializeSystemsInParallel(client) {
    const phaseStart = Date.now();
    console.log('[ready] 🔄 Phase 2: Starting parallel system initialization...');

    // Create independent initialization tasks that can run in parallel
    const initializationTasks = [
        {
            name: 'voiceCleanup',
            task: initializeVoiceCleanup(),
            critical: false
        },
        {
            name: 'emojiCleanup',
            task: initializeEmojiCleanup(client),
            critical: false
        },
        {
            name: 'itemLeaderboards',
            task: initializeItemLeaderboards(),
            critical: false
        },
        {
            name: 'performanceIndexes',
            task: initializePerformanceIndexes(),
            critical: true // Critical for /you command performance
        },
        {
            name: 'messageCacheIndexes',
            task: initializeMessageCacheIndexes(),
            critical: false
        },
        {
            name: 'statusRotation',
            task: initializeStatusRotation(client),
            critical: false
        }
    ];

    // Execute all tasks in parallel with individual error handling
    const results = await Promise.allSettled(initializationTasks.map(async (taskInfo) => {
        const taskStart = Date.now();
        try {
            await taskInfo.task;
            const duration = Date.now() - taskStart;
            console.log(`[ready] ✅ ${taskInfo.name} completed in ${duration}ms`);
            return { name: taskInfo.name, success: true, duration, error: null };
        } catch (error) {
            const duration = Date.now() - taskStart;
            const logLevel = taskInfo.critical ? 'error' : 'warn';
            console[logLevel](`[ready] ${taskInfo.critical ? '❌' : '⚠️'} ${taskInfo.name} failed in ${duration}ms:`, error.message);
            return { name: taskInfo.name, success: false, duration, error: error.message };
        }
    }));

    // Analyze results and generate metrics
    const successful = results.filter(r => r.value?.success).length;
    const failed = results.filter(r => !r.value?.success).length;
    const criticalFailed = results.filter(r => !r.value?.success &&
        initializationTasks.find(t => t.name === r.value?.name)?.critical).length;

    readyEventMetrics.phases.parallel = {
        duration: Date.now() - phaseStart,
        successful,
        failed,
        criticalFailed,
        tasks: results.map(r => r.value)
    };

    console.log(`[ready] ✅ Phase 2: Parallel initialization completed in ${readyEventMetrics.phases.parallel.duration}ms`);
    console.log(`[ready] 📊 Results: ${successful} successful, ${failed} failed${criticalFailed > 0 ? ` (${criticalFailed} critical)` : ''}`);

    if (criticalFailed > 0) {
        console.error('[ready] ❌ Critical systems failed - bot may have degraded performance');
    }
}

// Phase 3: Finalization and Monitoring
async function finalizeInitialization(client) {
    const phaseStart = Date.now();

    try {
        // Any final setup that depends on other systems being initialized
        console.log('[ready] 🏁 Finalizing initialization...');

        // Trigger initial voice EXP processing
        try {
            const { processVoiceExp } = require('../events/voiceStateUpdate.js');
            processVoiceExp(client);
        } catch (error) {
            console.warn('[ready] ⚠️  Initial voice EXP processing failed:', error.message);
        }

        readyEventMetrics.phases.finalization = {
            duration: Date.now() - phaseStart
        };

        console.log(`[ready] ✅ Phase 3: Finalization completed in ${readyEventMetrics.phases.finalization.duration}ms`);

    } catch (error) {
        console.error('[ready] ❌ Error during finalization:', error.message);
        // Non-critical, continue
    }
}

// Supporting Functions for Modular Initialization

async function initializeClientPresence(client) {
    try {
        let clientData = await optimizedFindOne("clients", { id: client.user.id });

        if (clientData == null) {
            await optimizedInsertOne("clients", {
                id: client.user.id,
                presence: { type: 0, status: 'online', descriptions: [] },
            });
            clientData = await optimizedFindOne("clients", { id: client.user.id });
        }

        const data = clientData.presence;
        await client.user.setPresence({
            activities: [{ name: '🦥 who r u?', type: Number(data.type) }],
            status: data.status
        });

        console.log('[ready] ✅ Client presence initialized');
    } catch (error) {
        console.error('[ready] ❌ Failed to initialize client presence:', error.message);
        throw error;
    }
}

async function initializeRefreshThreadsSystem(client) {
    try {
        console.log('[refreshThreads] Bot is ready! Initializing refresh system...');

        const { createRefreshThreadsFunction, activeIntervals } = global;

        if (createRefreshThreadsFunction && activeIntervals) {
            // Create the refresh function with proper client reference
            const refreshThreadsWithClient = createRefreshThreadsFunction(client);

            // Run initial refresh
            console.log('[refreshThreads] Running initial refresh...');
            await refreshThreadsWithClient();

            // Set up intervals with proper client reference
            const refreshInterval = setInterval(refreshThreadsWithClient, 2 * 60 * 1000);
            const voiceExpInterval = setInterval(() => {
                if (client.isReady()) {
                    const { processVoiceExp } = require('../events/voiceStateUpdate.js');
                    processVoiceExp(client);
                }
            }, 60 * 1000);

            // Track intervals for cleanup
            activeIntervals.push(refreshInterval, voiceExpInterval);
            console.log('[refreshThreads] ✅ Refresh intervals created successfully');
        } else {
            throw new Error('Refresh system functions not found in global scope');
        }
    } catch (error) {
        console.error('[refreshThreads] ❌ Failed to initialize refresh system:', error.message);
        throw error;
    }
}

// Parallel Initialization Functions (Independent operations)

async function initializeVoiceCleanup() {
    console.log('[voiceExp] Cleaning up stale voice sessions...');
    const botStartedAt = Date.now() - (process.uptime() * 1000);
    const result = await optimizedDeleteMany("voice_sessions", {
        sessionStart: { $lt: botStartedAt }
    });
    if (result.deletedCount > 0) {
        console.log(`[voiceExp] Cleaned up ${result.deletedCount} stale voice sessions from before bot startup`);
    }
}

async function initializeEmojiCleanup(client) {
    console.log('[emojiCleanup] Starting emoji cleanup system...');
    const { startEmojiCleanupSystem } = require('../utils/emojiCleanup.js');
    startEmojiCleanupSystem(client);
}

async function initializeItemLeaderboards() {
    console.log('[itemLeaderboards] Initializing database indexes...');
    const { ensureLeaderboardIndexes } = require('../utils/itemRecords.js');
    await ensureLeaderboardIndexes();
}

async function initializePerformanceIndexes() {
    console.log('[performance] Creating performance indexes...');
    const { createIndexes } = require('../scripts/createIndexes.js');
    await createIndexes();
}

async function initializeMessageCacheIndexes() {
    console.log('[messageCache] Initializing database indexes...');
    const { initializeIndexes: initMessageCacheIndexes } = require('../utils/messageCache.js');
    await initMessageCacheIndexes();
}

async function initializeStatusRotation(client) {
    console.log('[statusRotation] Starting dynamic status rotation...');
    const { dynamicRotateStatus } = global;
    if (dynamicRotateStatus) {
        dynamicRotateStatus(client);
    } else {
        throw new Error('dynamicRotateStatus function not found in global scope');
    }
}

// Performance Reporting
function generateReadyEventReport() {
    console.log('\n[ready] 📊 READY EVENT PERFORMANCE REPORT');
    console.log('[ready] ==========================================');

    // Phase breakdown
    console.log('[ready] 🕐 Phase Performance Breakdown:');
    if (readyEventMetrics.phases.critical) {
        console.log(`[ready]   Critical Systems: ${readyEventMetrics.phases.critical.duration}ms`);
        console.log(`[ready]     - Commands: ${readyEventMetrics.phases.critical.commands}ms`);
        console.log(`[ready]     - Presence: ${readyEventMetrics.phases.critical.presence}ms`);
        console.log(`[ready]     - Refresh: ${readyEventMetrics.phases.critical.refresh}ms`);
    }

    if (readyEventMetrics.phases.parallel) {
        console.log(`[ready]   Parallel Systems: ${readyEventMetrics.phases.parallel.duration}ms`);
        console.log(`[ready]     - Success Rate: ${readyEventMetrics.phases.parallel.successful}/${readyEventMetrics.phases.parallel.successful + readyEventMetrics.phases.parallel.failed}`);

        // Individual task performance
        readyEventMetrics.phases.parallel.tasks.forEach(task => {
            const status = task.success ? '✅' : '❌';
            console.log(`[ready]     - ${task.name}: ${status} ${task.duration}ms`);
        });
    }

    if (readyEventMetrics.phases.finalization) {
        console.log(`[ready]   Finalization: ${readyEventMetrics.phases.finalization.duration}ms`);
    }

    // Performance summary
    console.log('\n[ready] ⚡ Performance Summary:');
    console.log(`[ready]   Total Ready Event Time: ${readyEventMetrics.totalDuration}ms`);

    const parallelSavings = readyEventMetrics.phases.parallel ?
        Math.max(0, readyEventMetrics.phases.parallel.tasks.reduce((sum, task) => sum + task.duration, 0) - readyEventMetrics.phases.parallel.duration) : 0;

    if (parallelSavings > 0) {
        console.log(`[ready]   Parallel Execution Savings: ${parallelSavings}ms`);
        console.log(`[ready]   Performance Improvement: ${Math.round((parallelSavings / (readyEventMetrics.totalDuration + parallelSavings)) * 100)}%`);
    }

    // System status with dynamic cache count
    console.log('\n[ready] 🚀 System Status:');
    console.log('[ready]   ✅ Discord Client: Ready and optimized');
    console.log('[ready]   ✅ Database Operations: 301+ optimized operations active');

    // Get actual cache count from LRU cache system
    try {
        const { getGlobalCacheStats } = require('../utils/LRUCache.js');
        const cacheStats = getGlobalCacheStats();
        console.log(`[ready]   ✅ LRU Cache System: ${cacheStats.cacheCount} caches with performance monitoring`);
    } catch (error) {
        console.log('[ready]   ✅ LRU Cache System: Cache system active with performance monitoring');
    }

    console.log('[ready]   ✅ Refresh Threads: Initialized and running');
    console.log('[ready]   ✅ Performance Monitoring: Active and reporting');

    console.log('[ready] ==========================================\n');
}


