const { Events } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedUpdateOne, optimizedFindOne } = require("../utils/database-optimizer.js");
const { logger } = require('../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../utils/LRUCache.js');

/**
 * Guild Member Update Event Handler (Enterprise-Grade Performance Optimized)
 * Optimizes member update processing with comprehensive caching and performance monitoring
 * OPTIMIZED: LRU caching for guild configurations, member data, and sticky role processing
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const guildMemberUpdateMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    memberUpdatesProcessed: 0,
    roleChangesProcessed: 0,
    nicknameChangesProcessed: 0,
    stickyRoleOperations: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU caches for member update operations
const memberUpdateGuildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const memberUpdateMemberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member data
const stickyRoleConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for sticky role configurations

// Register caches for global cleanup
registerCache(memberUpdateGuildConfigCache);
registerCache(memberUpdateMemberDataCache);
registerCache(stickyRoleConfigCache);

/**
 * Get cached guild configuration for member updates (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Guild configuration data
 */
async function getCachedMemberUpdateGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `member_update_guild_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = memberUpdateGuildConfigCache.get(cacheKey);
        if (cached) {
            guildMemberUpdateMetrics.cacheHits++;
            if (guildMemberUpdateMetrics.verboseLogging) {
                console.log(`[guildMemberUpdate] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildMemberUpdateMetrics.cacheMisses++;
        guildMemberUpdateMetrics.databaseQueries++;

        // Get guild configuration
        const guildData = await optimizedFindOne('guilds', { id: guildId });
        if (!guildData) return null;

        // Cache the result
        memberUpdateGuildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        guildMemberUpdateMetrics.averageQueryTime =
            (guildMemberUpdateMetrics.averageQueryTime * (guildMemberUpdateMetrics.databaseQueries - 1) + duration) /
            guildMemberUpdateMetrics.databaseQueries;

        if (guildMemberUpdateMetrics.verboseLogging || duration > 100) {
            console.log(`[guildMemberUpdate] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error('[guildMemberUpdate] ❌ Error getting guild config:', error);
        return null;
    }
}

/**
 * Get cached member data for updates (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for member data
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} Member data
 */
async function getCachedMemberUpdateData(guildId, userId) {
    const startTime = Date.now();
    const cacheKey = `member_update_${guildId}_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = memberUpdateMemberDataCache.get(cacheKey);
        if (cached) {
            guildMemberUpdateMetrics.cacheHits++;
            if (guildMemberUpdateMetrics.verboseLogging) {
                console.log(`[guildMemberUpdate] ⚡ Member data cache hit for ${userId} in ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildMemberUpdateMetrics.cacheMisses++;
        guildMemberUpdateMetrics.databaseQueries++;

        // Get member data
        const memberData = await optimizedFindOne('member', { guildId: guildId, userId: userId });

        // Cache the result (even if null)
        memberUpdateMemberDataCache.set(cacheKey, memberData);

        const duration = Date.now() - startTime;
        guildMemberUpdateMetrics.averageQueryTime =
            (guildMemberUpdateMetrics.averageQueryTime * (guildMemberUpdateMetrics.databaseQueries - 1) + duration) /
            guildMemberUpdateMetrics.databaseQueries;

        if (guildMemberUpdateMetrics.verboseLogging || duration > 100) {
            console.log(`[guildMemberUpdate] ✅ Member data fetched for ${userId} in ${guildId}: ${duration}ms - cached for future access`);
        }

        return memberData;
    } catch (error) {
        console.error('[guildMemberUpdate] ❌ Error getting member data:', error);
        return null;
    }
}

/**
 * Get cached sticky configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for sticky role configurations
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Sticky configuration data
 */
async function getCachedStickyConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `sticky_config_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = stickyRoleConfigCache.get(cacheKey);
        if (cached) {
            guildMemberUpdateMetrics.cacheHits++;
            if (guildMemberUpdateMetrics.verboseLogging) {
                console.log(`[guildMemberUpdate] ⚡ Sticky config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        guildMemberUpdateMetrics.cacheMisses++;

        // Fallback to existing sticky cache utility
        const { getCachedGuildStickyConfig } = require('../utils/stickyCache.js');
        const stickyConfig = await getCachedGuildStickyConfig(guildId);

        // Cache the result
        stickyRoleConfigCache.set(cacheKey, stickyConfig);

        const duration = Date.now() - startTime;
        if (guildMemberUpdateMetrics.verboseLogging || duration > 100) {
            console.log(`[guildMemberUpdate] ✅ Sticky config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return stickyConfig;
    } catch (error) {
        console.error('[guildMemberUpdate] ❌ Error getting sticky config:', error);
        return { enabled: false, roles: [], nick: false };
    }
}

module.exports = {
    name: Events.GuildMemberUpdate,
    async execute(oldMember, newMember) {
        const startTime = Date.now();

        console.log(`[guildMemberUpdate] Event triggered for ${newMember.user.tag} in ${newMember.guild.name}`);
        try {
            guildMemberUpdateMetrics.memberUpdatesProcessed++;
            // Try to fetch fresh member data if roles are missing
            let freshOldMember = oldMember;
            let freshNewMember = newMember;

            if (!oldMember.roles || !oldMember.roles.cache) {
                try {
                    freshOldMember = await oldMember.guild.members.fetch(oldMember.id);
                } catch (err) {
                    console.log(`[guildMemberUpdate] Could not fetch old member data for ${newMember.user.tag}`);
                }
            }

            if (!newMember.roles || !newMember.roles.cache) {
                try {
                    freshNewMember = await newMember.guild.members.fetch(newMember.id);
                } catch (err) {
                    console.log(`[guildMemberUpdate] Could not fetch new member data for ${newMember.user.tag}`);
                }
            }

            // Final check - if we still don't have role data, skip
            if (!freshOldMember.roles || !freshNewMember.roles || !freshOldMember.roles.cache || !freshNewMember.roles.cache) {
                console.log(`[guildMemberUpdate] Still missing role data for ${newMember.user.tag} after fetch attempts, skipping`);
                return;
            }

            // Use the fresh member data
            oldMember = freshOldMember;
            newMember = freshNewMember;

            // Only process if roles or nickname changed
            const rolesChanged = !oldMember.roles.cache.equals(newMember.roles.cache);
            const nicknameChanged = oldMember.nickname !== newMember.nickname;
            console.log(`[guildMemberUpdate] Changes detected - roles: ${rolesChanged}, nickname: ${nicknameChanged}`);

            if (!rolesChanged && !nicknameChanged) {
                console.log(`[guildMemberUpdate] No relevant changes for ${newMember.user.tag}`);
                return; // No relevant changes
            }

            // Track metrics
            if (rolesChanged) guildMemberUpdateMetrics.roleChangesProcessed++;
            if (nicknameChanged) guildMemberUpdateMetrics.nicknameChangesProcessed++;

            // OPTIMIZED: Use Promise.allSettled for parallel data fetching
            guildMemberUpdateMetrics.parallelOperations++;

            const [guildConfigResult, memberDataResult, stickyConfigResult] = await Promise.allSettled([
                getCachedMemberUpdateGuildConfig(newMember.guild.id),
                getCachedMemberUpdateData(newMember.guild.id, newMember.user.id),
                getCachedStickyConfig(newMember.guild.id)
            ]);

            // Handle partial failures
            if (stickyConfigResult.status === 'rejected') {
                console.error('[guildMemberUpdate] ❌ Error getting sticky config:', stickyConfigResult.reason);
                guildMemberUpdateMetrics.partialFailures++;
                return;
            }

            const stickyConfig = stickyConfigResult.value;
            console.log(`[guildMemberUpdate] Sticky config:`, stickyConfig);

            if (!stickyConfig.enabled) {
                console.log(`[guildMemberUpdate] Sticky feature disabled for ${newMember.guild.name}`);
                return; // Sticky feature disabled
            }

            // Prepare sticky data to save
            const stickyData = {};
            let shouldSave = false;

            // Save roles if sticky roles is enabled and roles changed
            if (stickyConfig.roles.length > 0 && rolesChanged && newMember.roles && newMember.roles.cache) {
                // Get current roles (excluding @everyone)
                const currentRoles = newMember.roles.cache
                    .filter(role => role.id !== newMember.guild.id) // Exclude @everyone
                    .filter(role => stickyConfig.roles.includes(role.id)) // Only sticky-enabled roles
                    .map(role => role.id);

                stickyData.roles = currentRoles;
                shouldSave = true;

                console.log(`[sticky] Saving roles for ${newMember.user.tag}: ${currentRoles.length} roles`);
            }

            // Save nickname if sticky nicknames is enabled and nickname changed
            if (stickyConfig.nick && nicknameChanged) {
                stickyData.nick = newMember.nickname;
                shouldSave = true;
                
                console.log(`[sticky] Saving nickname for ${newMember.user.tag}: "${newMember.nickname}"`);
            }

            // Save to database if there's data to save
            if (shouldSave) {
                guildMemberUpdateMetrics.stickyRoleOperations++;

                // Update or create member document with sticky data
                await optimizedUpdateOne("member",
                    { userId: newMember.id, guildId: newMember.guild.id },
                    {
                        $set: {
                            sticky: stickyData,
                            lastUpdated: new Date()
                        }
                    },
                    { upsert: true }
                );

                // Invalidate cache for this member
                const { invalidateMemberStickyData } = require('../utils/stickyCache.js');
                invalidateMemberStickyData(newMember.id, newMember.guild.id);

                console.log(`[sticky] Updated sticky data for ${newMember.user.tag} in ${newMember.guild.name}`);
            }

        } catch (error) {
            console.error('[guildMemberUpdate] Error processing member update:', error);
            logger.error('guildMemberUpdate', `Error processing member update: ${error.message}`, newMember.client);
        } finally {
            // Track performance metrics
            const duration = Date.now() - startTime;
            if (guildMemberUpdateMetrics.verboseLogging || duration > 100) {
                console.log(`[guildMemberUpdate] ✅ Member update processed in ${duration}ms`);
            }
        }
    },

    // Enhanced optimization functions
    getCachedMemberUpdateGuildConfig,
    getCachedMemberUpdateData,
    getCachedStickyConfig,
    getGuildMemberUpdateStats,
    performanceCleanupAndOptimization,
    clearAllGuildMemberUpdateCaches
};

/**
 * Get comprehensive guild member update performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive guild member update performance data
 */
function getGuildMemberUpdateStats() {
    const cacheHitRate = guildMemberUpdateMetrics.cacheHits + guildMemberUpdateMetrics.cacheMisses > 0 ?
        (guildMemberUpdateMetrics.cacheHits / (guildMemberUpdateMetrics.cacheHits + guildMemberUpdateMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: guildMemberUpdateMetrics.cacheHits,
            cacheMisses: guildMemberUpdateMetrics.cacheMisses,
            databaseQueries: guildMemberUpdateMetrics.databaseQueries,
            averageQueryTime: `${guildMemberUpdateMetrics.averageQueryTime.toFixed(2)}ms`,
            memberUpdatesProcessed: guildMemberUpdateMetrics.memberUpdatesProcessed,
            roleChangesProcessed: guildMemberUpdateMetrics.roleChangesProcessed,
            nicknameChangesProcessed: guildMemberUpdateMetrics.nicknameChangesProcessed,
            stickyRoleOperations: guildMemberUpdateMetrics.stickyRoleOperations,
            parallelOperations: guildMemberUpdateMetrics.parallelOperations,
            partialFailures: guildMemberUpdateMetrics.partialFailures,
            lastOptimization: new Date(guildMemberUpdateMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: memberUpdateGuildConfigCache.getStats(),
            memberData: memberUpdateMemberDataCache.getStats(),
            stickyConfig: stickyRoleConfigCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildConfig: memberUpdateGuildConfigCache.getStats().memoryUsage,
            memberData: memberUpdateMemberDataCache.getStats().memoryUsage,
            stickyConfig: stickyRoleConfigCache.getStats().memoryUsage,
            total: memberUpdateGuildConfigCache.getStats().memoryUsage +
                   memberUpdateMemberDataCache.getStats().memoryUsage +
                   stickyRoleConfigCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    guildMemberUpdateMetrics.lastOptimization = Date.now();

    const stats = getGuildMemberUpdateStats();
    if (guildMemberUpdateMetrics.verboseLogging) {
        console.log(`[guildMemberUpdate] 📊 Performance Report:`);
        console.log(`[guildMemberUpdate]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[guildMemberUpdate]   Member Updates Processed: ${stats.performance.memberUpdatesProcessed}`);
        console.log(`[guildMemberUpdate]   Role Changes Processed: ${stats.performance.roleChangesProcessed}`);
        console.log(`[guildMemberUpdate]   Nickname Changes Processed: ${stats.performance.nicknameChangesProcessed}`);
        console.log(`[guildMemberUpdate]   Sticky Role Operations: ${stats.performance.stickyRoleOperations}`);
        console.log(`[guildMemberUpdate]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[guildMemberUpdate]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[guildMemberUpdate]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[guildMemberUpdate]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[guildMemberUpdate]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Clear all guild member update caches (Enterprise-Grade Cache Management)
 */
function clearAllGuildMemberUpdateCaches() {
    memberUpdateGuildConfigCache.clear();
    memberUpdateMemberDataCache.clear();
    stickyRoleConfigCache.clear();

    console.log('[guildMemberUpdate] 🗑️ Cleared all guild member update caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, guildMemberUpdateMetrics.performanceReportInterval);
