const { Events } = require('discord.js');
const { mongoClient } = require("../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne, optimizedFind } = require("../utils/database-optimizer.js");
const { defaults } = require("../utils/default_db_structures");
const { sendLog, sendLogContainer } = require("../utils/sendLog.js");
const { createGuildLeaveContainer } = require('../utils/logContainers.js');

module.exports = {
    name: Events.GuildDelete,
    async execute(client, guild) {
        console.log(`[guildDelete] Bot left guild: ${guild.name} (${guild.id})`);

        var guildData = await optimizedFindOne("guilds", { id: guild.id });
        if (guildData == null) {
            await optimizedInsertOne("guilds", defaults.guild(guild.id));
            guildData = await optimizedFindOne("guilds", { id: guild.id });
        }

        // Send owner-only log to all OTHER guilds where owner has configured botLeftServer
        try {
            const allGuilds = await optimizedFind("guilds", { id: { $ne: guild.id } }); // Exclude the guild we just left

            for (const otherGuildData of allGuilds) {
                if (otherGuildData.logs && otherGuildData.logs.enabled) {
                    const hasOwnerEvent = otherGuildData.logs.channels.some(ch =>
                        ch.events.includes('botLeftServer')
                    );

                    if (hasOwnerEvent) {
                        // Create Components v2 container for owner-only bot left server log
                        const container = createGuildLeaveContainer({
                            guildName: guild.name,
                            guildId: guild.id
                        });

                        await sendLogContainer(otherGuildData.id, 'botLeftServer', container, client);
                    }
                }
            }
        } catch (error) {
            console.error('[guildDelete] Error sending owner logs:', error);
        }

        // Regular guildDelete logging with Components v2
        const channels = (guildData.logs.channels.filter(ch => ch.events.includes("guildDelete")) ?? []).map(l => guild.channels.cache.get(l.id)).filter(ch => ch);

        if (channels.length > 0) {
            // Create Components v2 container for guild leave log
            const container = createGuildLeaveContainer({
                guildName: guild.name,
                guildId: guild.id
            });

            await sendLogContainer(guild.id, 'guildDelete', container, client);
        }
    },
};