/**
 * Comprehensive Deep-Dive Discord Bot Feature Test Suite
 * 
 * This test suite validates every Discord bot feature interaction at the granular level,
 * including all modal forms, select menus, button interactions, permission boundaries,
 * database operations, and UI state management.
 * 
 * Success Criteria: 100% success rate (no failures allowed)
 * All interactions must complete within Discord's 3-second timeout
 */

const { Client, GatewayIntentBits, MessageFlags, ButtonStyle, TextInputStyle } = require('discord.js');
const { mongoClient } = require('../mongo/client.js');

// Import all command modules for testing
const items = require('../commands/utility/items.js');
const exp = require('../commands/utility/exp.js');
const sticky = require('../commands/utility/sticky.js');
const logs = require('../commands/utility/logs.js');
const you = require('../commands/utility/you.js');
const owner = require('../commands/utility/owner.js');
const changelog = require('../commands/utility/changelog.js');
const clearData = require('../commands/utility/clearData.js');
const dehoist = require('../commands/utility/dehoist.js');
const featuresMenu = require('../commands/utility/featuresMenu.js');
const lookup = require('../commands/utility/lookup.js');
const opener = require('../commands/utility/opener.js');
const ownerGlobalLevels = require('../commands/utility/owner-global-levels.js');
const ownerGlobalLevelsHandlers = require('../commands/utility/owner-global-levels-handlers.js');
const ownerJoinNotification = require('../commands/utility/owner-join-notification.js');
const ownerServers = require('../commands/utility/owner-servers.js');
const ownerStatus = require('../commands/utility/owner-status.js');
const expDb = require('../commands/utility/exp_db.js');
const instantiate = require('../commands/utility/instantiate.js');

// CRITICAL FIX: Handle unhandled promise rejections to prevent test framework crashes
process.on('unhandledRejection', (reason, promise) => {
    console.error('🚨 UNHANDLED PROMISE REJECTION DETECTED:');
    console.error('Reason:', reason);
    console.error('Promise:', promise);

    // Track this as a test framework error
    if (global.testFramework) {
        global.testFramework.testResults.unhandledRejections = global.testFramework.testResults.unhandledRejections || [];
        global.testFramework.testResults.unhandledRejections.push({
            reason: reason.toString(),
            timestamp: Date.now(),
            stack: reason.stack
        });
    }
});

// Enhanced error tracking and Discord API simulation
class DiscordAPISimulator {
    constructor() {
        this.rateLimits = new Map();
        this.networkLatency = { min: 50, max: 200 };
        this.errorScenarios = new Map();
    }

    simulateNetworkLatency() {
        const { min, max } = this.networkLatency;
        return min + Math.random() * (max - min);
    }

    checkRateLimit(endpoint) {
        const limit = this.rateLimits.get(endpoint);
        if (limit && Date.now() < limit.resetTime) {
            if (limit.remaining <= 0) {
                throw new Error(`Rate limit exceeded for ${endpoint}. Reset at ${new Date(limit.resetTime)}`);
            }
            limit.remaining--;
        }
        return true;
    }

    simulateDiscordError(probability = 0.05, context = 'general') {
        if (Math.random() < probability) {
            // Simulate realistic production error patterns based on context
            let errorCode, errorMessage;

            if (context === 'interaction_timeout') {
                // Simulate the exact errors happening in production
                if (Math.random() < 0.7) {
                    errorCode = 10062;
                    errorMessage = 'Unknown interaction';
                } else {
                    errorCode = 40060;
                    errorMessage = 'Interaction has already been acknowledged';
                }
            } else if (context === 'network_failure') {
                errorCode = 50035;
                errorMessage = 'Invalid Form Body';
            } else {
                // General errors
                const errors = [
                    { code: 10062, message: 'Unknown interaction' },
                    { code: 40060, message: 'Interaction has already been acknowledged' },
                    { code: 50013, message: 'Missing Permissions' },
                    { code: 50035, message: 'Invalid Form Body' }
                ];
                const error = errors[Math.floor(Math.random() * errors.length)];
                errorCode = error.code;
                errorMessage = error.message;
            }

            const discordError = new Error(errorMessage);
            discordError.code = errorCode;

            // CRITICAL FIX: Log the error for test framework detection instead of throwing
            // This prevents unhandled promise rejections while still allowing error detection
            console.error(`Discord API Error ${errorCode}: ${errorMessage}`);

            // Return the error instead of throwing to allow proper error handling
            return discordError;
        }
        return true;
    }

    // Simulate realistic network timing that causes production issues
    simulateRealisticNetworkLatency() {
        // Production shows high variability in response times
        const baseLatency = 50 + Math.random() * 200; // 50-250ms base
        const jitter = Math.random() * 600; // Up to 600ms jitter
        const networkSpike = Math.random() < 0.1 ? Math.random() * 1000 : 0; // 10% chance of 0-1000ms spike

        return Math.floor(baseLatency + jitter + networkSpike);
    }
}

class ValidationErrorDetector {
    constructor() {
        this.validationErrors = [];
        this.originalBuilders = {};
        this.setupBuilderInterception();
    }

    setupBuilderInterception() {
        // Intercept Discord.js builders to catch undefined values
        const { StringSelectMenuBuilder, ButtonBuilder, EmbedBuilder } = require('discord.js');

        // Store original methods
        this.originalBuilders.StringSelectMenuBuilder = {
            addOptions: StringSelectMenuBuilder.prototype.addOptions
        };

        const validationDetector = this;

        // Intercept StringSelectMenuBuilder.addOptions to catch undefined values
        StringSelectMenuBuilder.prototype.addOptions = function(...options) {
            options.forEach((option, index) => {
                if (typeof option === 'object' && option !== null) {
                    Object.entries(option).forEach(([key, value]) => {
                        if (value === undefined) {
                            const error = new Error(`StringSelectMenuBuilder.addOptions: ${key} is undefined at option index ${index}`);
                            error.builderType = 'StringSelectMenuBuilder';
                            error.method = 'addOptions';
                            error.field = key;
                            error.optionIndex = index;
                            validationDetector.validationErrors.push(error);
                            throw error;
                        }
                    });
                }
            });
            return validationDetector.originalBuilders.StringSelectMenuBuilder.addOptions.call(this, ...options);
        };
    }

    restoreOriginalBuilders() {
        const { StringSelectMenuBuilder } = require('discord.js');
        if (this.originalBuilders.StringSelectMenuBuilder) {
            StringSelectMenuBuilder.prototype.addOptions = this.originalBuilders.StringSelectMenuBuilder.addOptions;
        }
    }

    getValidationErrors() {
        return this.validationErrors;
    }

    reset() {
        this.validationErrors = [];
    }
}

// Enhanced console error tracking
class ConsoleErrorTracker {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.criticalPatterns = [
            /Cannot read properties of undefined/,
            /TypeError:/,
            /ReferenceError:/,
            /Discord API error/,
            /Interaction timeout/,
            /Rate limit exceeded/,
            /\[imageUploader\] Error/,
            /\[stickyCache\] Error/,
            /Error fetching/,
            /Error validating/,
            /UnhandledPromiseRejectionWarning/,
            /DeprecationWarning/,
            /MaxListenersExceededWarning/
        ];
        this.setupConsoleInterception();
    }

    setupConsoleInterception() {
        const originalError = console.error;
        const originalWarn = console.warn;
        const originalLog = console.log;

        console.error = (...args) => {
            const message = args.join(' ');
            this.errors.push({ message, timestamp: Date.now(), stack: new Error().stack });
            originalError.apply(console, args);
        };

        console.warn = (...args) => {
            const message = args.join(' ');
            this.warnings.push({ message, timestamp: Date.now() });
            originalWarn.apply(console, args);
        };

        console.log = (...args) => {
            const message = args.join(' ');
            // Check for error patterns in log messages
            if (this.criticalPatterns.some(pattern => pattern.test(message))) {
                this.errors.push({ message, timestamp: Date.now(), stack: new Error().stack });
            }
            originalLog.apply(console, args);
        };
    }

    getCriticalErrors() {
        return this.errors.filter(error =>
            this.criticalPatterns.some(pattern => pattern.test(error.message))
        );
    }

    reset() {
        this.errors = [];
        this.warnings = [];
    }
}

class ComprehensiveDeepDiveTestSuite {
    constructor() {
        this.client = null;
        this.discordSimulator = new DiscordAPISimulator();
        this.errorTracker = new ConsoleErrorTracker();
        this.validationDetector = new ValidationErrorDetector();
        this.interactionStates = new Map(); // Track interaction lifecycle

        // CRITICAL FIX: Set global reference for unhandled rejection tracking
        global.testFramework = this;

        this.testResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            testDetails: [],
            timingData: [],
            consoleErrors: [],
            discordAPIErrors: [],
            interactionTimingIssues: [],
            validationErrors: [],
            unhandledRejections: [], // Track unhandled promise rejections
            featureCoverage: {
                items: { total: 0, passed: 0 },
                levels: { total: 0, passed: 0 },
                sticky: { total: 0, passed: 0 },
                logs: { total: 0, passed: 0 },
                userSettings: { total: 0, passed: 0 },
                admin: { total: 0, passed: 0 },
                changelog: { total: 0, passed: 0 },
                clearData: { total: 0, passed: 0 },
                dehoist: { total: 0, passed: 0 },
                featuresMenu: { total: 0, passed: 0 },
                lookup: { total: 0, passed: 0 },
                opener: { total: 0, passed: 0 },
                ownerModules: { total: 0, passed: 0 }
            }
        };
        this.currentTestPhase = '';

        // Test environment configuration
        this.testGuildId = process.env.GUILDIDTWO || '417175807795134475';
        this.testUserId = process.env.OWNER || '97757532835033088';
        this.regularUserId = '987654321098765432'; // Non-owner user for permission testing
        this.testChannelId = '123456789012345680';

        // Discord API timeout constants - REALISTIC PRODUCTION SIMULATION
        this.DISCORD_INTERACTION_TIMEOUT = 3000; // 3 seconds
        this.DISCORD_FOLLOWUP_TIMEOUT = 15 * 60 * 1000; // 15 minutes
        this.DISCORD_NETWORK_JITTER = 800; // Network variability up to 800ms

        // Production error simulation settings
        this.simulateProductionErrors = true;
        this.productionErrorRate = 0.12; // 12% chance of realistic production errors
        this.networkFailureRate = 0.08; // 8% chance of network-related failures
    }

    /**
     * Initialize test environment
     */
    async initialize() {
        console.log('🚀 Initializing Comprehensive Deep-Dive Test Suite...');
        
        // Create mock Discord client
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ]
        });

        // Set up global permission function
        global.hasFeaturePermission = this.mockHasFeaturePermission.bind(this);

        // Mock client login
        this.client.user = {
            id: '1234567890123456789',
            username: 'TestBot',
            discriminator: '1076',
            tag: 'TestBot#1076'
        };

        console.log(`✅ Test client logged in as ${this.client.user.tag}`);

        // Wait for MongoDB connection
        console.log('⏳ Waiting for MongoDB connection...');
        await this.waitForDatabase();
        console.log('✅ MongoDB connection confirmed');

        // Set up test data
        await this.setupTestData();
        console.log('✅ Test data initialized');
    }

    /**
     * Mock permission function for testing
     */
    mockHasFeaturePermission(member, feature) {
        // Server owners always have full access
        const isServerOwner = member.guild.ownerId === member.user.id;
        if (isServerOwner) return true;

        // Mock permission logic for testing
        const permissions = {
            'items': 'Administrator',
            'exp': 'Administrator', 
            'sticky': 'Administrator',
            'logs': 'Administrator'
        };

        return member.permissions.has(permissions[feature]);
    }

    /**
     * Wait for database connection
     */
    async waitForDatabase() {
        let attempts = 0;
        const maxAttempts = 30;
        
        while (attempts < maxAttempts) {
            try {
                await mongoClient.db('test').admin().ping();
                return;
            } catch (error) {
                attempts++;
                if (attempts >= maxAttempts) {
                    throw new Error('Database connection timeout');
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }

    /**
     * Set up test data in database
     */
    async setupTestData() {
        // Clean up any existing test data
        await this.cleanupTestData();

        // Create test guild data
        const guildCol = mongoClient.db('test').collection('guilds');
        await guildCol.insertOne({
            id: this.testGuildId,
            exp: {
                enabled: true,
                levels: [
                    { roleId: '123456789012345678', exp: 0 },
                    { roleId: '123456789012345679', exp: 1000 }
                ],
                levelMsgEnabled: true,
                levelChannel: null,
                levelMsg: '{mention} leveled up to level {level} and received the {role} role.',
                text: { enabled: true, expPerMin: 1, cooldown: 1, minChars: 4 },
                voice: { enabled: true, expPerMin: 2, cooldown: 1, msgEnabled: true }
            },
            sticky: {
                roles: [],
                nick: null
            },
            logs: {
                channels: [],
                enabled: false
            },
            items: {
                enabled: true
            }
        });

        // Create test user data
        const userCol = mongoClient.db('test').collection('users');
        await userCol.insertOne({
            id: this.testUserId,
            itemDMNotificationsEnabled: true,
            globalLevelDMNotificationsEnabled: true,
            notificationCenterEnabled: true
        });
    }

    /**
     * Clean up test data
     */
    async cleanupTestData() {
        const collections = ['guilds', 'users', 'custom_items', 'user_inventory', 'item_creation_temp', 'exp_create_level_temp'];
        
        for (const collectionName of collections) {
            try {
                const col = mongoClient.db('test').collection(collectionName);
                await col.deleteMany({ 
                    $or: [
                        { id: this.testGuildId },
                        { guildId: this.testGuildId },
                        { userId: this.testUserId },
                        { userId: this.regularUserId }
                    ]
                });
            } catch (error) {
                // Collection might not exist, ignore error
            }
        }
    }

    /**
     * Create mock interaction object
     */
    createMockInteraction(type, options = {}) {
        const interactionId = `test_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        
        const createdAt = Date.now();

        // Enhanced guild simulation with proper member structure
        const guild = {
            id: this.testGuildId,
            ownerId: this.testUserId,
            name: 'Test Guild',
            roles: {
                cache: new Map([
                    ['123456789012345678', { id: '123456789012345678', name: 'Level 0', position: 1 }],
                    ['123456789012345679', { id: '123456789012345679', name: 'Level 1', position: 2 }]
                ])
            },
            members: {
                cache: new Map(),
                me: {
                    id: process.env.CLIENT_ID || '590188133400838144',
                    roles: {
                        highest: { position: 10 }
                    }
                }
            }
        };

        const user = {
            id: options.userId || this.testUserId,
            username: 'TestUser',
            displayAvatarURL: () => 'https://cdn.discordapp.com/embed/avatars/0.png'
        };

        const member = {
            id: user.id,
            user: user,
            permissions: {
                has: (permission) => {
                    // Server owner always has permissions
                    if (user.id === this.testUserId) return true;
                    // Mock permission checking
                    return permission === 'Administrator';
                }
            },
            roles: {
                cache: new Map(),
                highest: { position: 1 }
            },
            guild: guild
        };

        // Enhanced channel simulation with message fetching
        const channel = {
            id: this.testChannelId || '123456789012345680',
            name: 'test-channel',
            type: 0, // Text channel
            messages: {
                fetch: async (fetchOptions = {}) => {
                    // Simulate Discord API constraint
                    this.discordSimulator.checkRateLimit('messages.fetch');
                    await new Promise(resolve => setTimeout(resolve, this.discordSimulator.simulateNetworkLatency()));

                    // Simulate potential Discord API errors
                    const errorResult = this.discordSimulator.simulateDiscordError(0.02); // 2% error rate
                    if (errorResult instanceof Error) {
                        throw errorResult;
                    }

                    // Return mock messages collection that behaves like Discord.js Collection
                    const mockMessages = new Map([
                        ['msg1', {
                            id: 'msg1',
                            content: 'Test message 1',
                            attachments: new Map(),
                            author: { id: this.testUserId },
                            createdTimestamp: Date.now() - 1000 * 60 * 30 // 30 minutes ago
                        }],
                        ['msg2', {
                            id: 'msg2',
                            content: 'Test message 2',
                            attachments: new Map(),
                            author: { id: this.testUserId },
                            createdTimestamp: Date.now() - 1000 * 60 * 60 // 1 hour ago
                        }]
                    ]);

                    // Add array-like methods that imageUploader expects
                    mockMessages.every = function(callback) {
                        for (const [key, value] of this) {
                            if (!callback(value, key, this)) return false;
                        }
                        return true;
                    };

                    mockMessages.filter = function(callback) {
                        const filtered = new Map();
                        for (const [key, value] of this) {
                            if (callback(value, key, this)) {
                                filtered.set(key, value);
                            }
                        }
                        // Add array-like methods to filtered result too
                        filtered.every = this.every;
                        filtered.filter = this.filter;
                        filtered.last = this.last;
                        return filtered;
                    };

                    mockMessages.last = function() {
                        const entries = Array.from(this.entries());
                        return entries.length > 0 ? entries[entries.length - 1][1] : null;
                    };

                    return mockMessages;
                }
            },
            send: async (options) => {
                this.discordSimulator.checkRateLimit('channel.send');
                await new Promise(resolve => setTimeout(resolve, this.discordSimulator.simulateNetworkLatency()));
                console.log(`[MOCK] send completed for ${interactionId}`);
                return { id: `msg_${Date.now()}` };
            }
        };

        // Track interaction state for lifecycle validation
        const interactionState = {
            id: interactionId,
            createdAt: createdAt,
            deferred: false,
            replied: false,
            deferCount: 0,
            replyCount: 0,
            expired: false
        };
        this.interactionStates.set(interactionId, interactionState);

        const mockInteraction = {
            id: interactionId,
            type: type,
            user: user,
            member: member,
            guild: guild,
            channel: channel,
            client: this.client,
            replied: false,
            deferred: false,
            customId: options.customId || 'test-interaction',
            values: options.values || [],
            commandName: options.commandName || 'test',
            createdTimestamp: createdAt,
            guildId: guild.id,
            channelId: channel.id,

            // Add message property for interactions that need it (like logs)
            message: {
                channelId: channel.id,
                id: `msg_${interactionId}`,
                author: user
            },

            // Enhanced interaction methods with Discord API validation
            deferReply: async () => {
                const state = this.interactionStates.get(interactionId);

                // REALISTIC PRODUCTION ERROR SIMULATION
                // Simulate network latency that can cause timing issues
                const networkLatency = this.discordSimulator.simulateRealisticNetworkLatency();
                await new Promise(resolve => setTimeout(resolve, networkLatency));

                // Check if interaction has expired due to realistic network delays
                const timeElapsed = Date.now() - state.createdAt;
                if (timeElapsed > this.DISCORD_INTERACTION_TIMEOUT) {
                    state.expired = true;
                    const error = new Error('Unknown interaction');
                    error.code = 10062;
                    this.testResults.interactionTimingIssues.push({
                        interactionId,
                        issue: 'Interaction expired due to network latency',
                        timeElapsed,
                        networkLatency,
                        timestamp: Date.now()
                    });
                    throw error;
                }

                // Check for double-deferring
                if (state.deferred || state.replied) {
                    state.deferCount++;
                    const error = new Error('Interaction has already been acknowledged.');
                    error.code = 40060;
                    this.testResults.interactionTimingIssues.push({
                        interactionId,
                        issue: 'Double defer attempt',
                        timestamp: Date.now()
                    });
                    throw error;
                }

                // Simulate production-level Discord API errors
                this.discordSimulator.checkRateLimit('interaction.deferReply');
                const errorResult = this.discordSimulator.simulateDiscordError(this.productionErrorRate, 'interaction_timeout');
                if (errorResult instanceof Error) {
                    throw errorResult;
                }

                state.deferred = true;
                state.deferCount++;
                mockInteraction.deferred = true;
                console.log(`[MOCK] deferReply completed for ${interactionId}`);
                return { id: interactionId };
            },

            deferUpdate: async () => {
                const state = this.interactionStates.get(interactionId);

                // REALISTIC PRODUCTION ERROR SIMULATION FOR deferUpdate
                // Simulate network latency that can cause timing issues (logs.js line 738 error)
                const networkLatency = this.discordSimulator.simulateRealisticNetworkLatency();
                await new Promise(resolve => setTimeout(resolve, networkLatency));

                // Check if interaction has expired due to realistic network delays
                const timeElapsed = Date.now() - state.createdAt;
                if (timeElapsed > this.DISCORD_INTERACTION_TIMEOUT) {
                    state.expired = true;
                    const error = new Error('Unknown interaction');
                    error.code = 10062;
                    this.testResults.interactionTimingIssues.push({
                        interactionId,
                        issue: 'deferUpdate expired due to network latency (logs.js pattern)',
                        timeElapsed,
                        networkLatency,
                        timestamp: Date.now()
                    });
                    throw error;
                }

                // Check for double-deferring
                if (state.deferred || state.replied) {
                    state.deferCount++;
                    const error = new Error('Interaction has already been acknowledged.');
                    error.code = 40060;
                    this.testResults.interactionTimingIssues.push({
                        interactionId,
                        issue: 'Double defer attempt',
                        timestamp: Date.now()
                    });
                    throw error;
                }

                // Simulate production-level Discord API errors
                this.discordSimulator.checkRateLimit('interaction.deferUpdate');
                const errorResult = this.discordSimulator.simulateDiscordError(this.productionErrorRate, 'interaction_timeout');
                if (errorResult instanceof Error) {
                    throw errorResult;
                }

                state.deferred = true;
                state.deferCount++;
                mockInteraction.deferred = true;
                console.log(`[MOCK] deferUpdate completed for ${interactionId}`);
                return { id: interactionId };
            },

            reply: async (options) => {
                const state = this.interactionStates.get(interactionId);

                // Check if interaction has expired
                if (Date.now() - state.createdAt > this.DISCORD_INTERACTION_TIMEOUT) {
                    state.expired = true;
                    const error = new Error('Interaction has already been acknowledged.');
                    error.code = 40060;
                    throw error;
                }

                // Check for double-replying
                if (state.replied || state.deferred) {
                    state.replyCount++;
                    const error = new Error('Interaction has already been acknowledged.');
                    error.code = 40060;
                    this.testResults.interactionTimingIssues.push({
                        interactionId,
                        issue: 'Double reply attempt',
                        timestamp: Date.now()
                    });
                    throw error;
                }

                // Simulate Discord API timing
                this.discordSimulator.checkRateLimit('interaction.reply');
                await new Promise(resolve => setTimeout(resolve, this.discordSimulator.simulateNetworkLatency()));
                const errorResult = this.discordSimulator.simulateDiscordError(0.01);
                if (errorResult instanceof Error) {
                    throw errorResult;
                }

                state.replied = true;
                state.replyCount++;
                mockInteraction.replied = true;
                console.log(`[MOCK] reply completed for ${interactionId}`);
                return { id: interactionId };
            },

            editReply: async (options) => {
                const state = this.interactionStates.get(interactionId);

                // Validate proper sequence: must be deferred or replied first
                if (!state.replied && !state.deferred) {
                    const error = new Error('The reply to this interaction has not been sent or deferred.');
                    error.code = 10062;
                    this.testResults.interactionTimingIssues.push({
                        interactionId,
                        issue: 'editReply without defer/reply',
                        timestamp: Date.now()
                    });
                    throw error;
                }

                // Simulate Discord API timing
                this.discordSimulator.checkRateLimit('interaction.editReply');
                await new Promise(resolve => setTimeout(resolve, this.discordSimulator.simulateNetworkLatency()));
                const errorResult = this.discordSimulator.simulateDiscordError(0.01);
                if (errorResult instanceof Error) {
                    throw errorResult;
                }

                console.log(`[MOCK] editReply completed for ${interactionId}`);
                return { id: interactionId };
            },
            
            update: async (options) => {
                await new Promise(resolve => setTimeout(resolve, 45 + Math.random() * 75));
                console.log(`[MOCK] update completed for ${interactionId}`);
                return { id: interactionId };
            },
            
            showModal: async (modal) => {
                await new Promise(resolve => setTimeout(resolve, 20 + Math.random() * 40));
                console.log(`[MOCK] showModal completed for ${interactionId}`);
                return { id: interactionId };
            },
            
            // Mock field access for modal submissions
            fields: {
                getTextInputValue: (customId) => {
                    const mockValues = {
                        'item-name': 'Test Item',
                        'item-description': 'A test item for testing',
                        'level-msg-template-input': '{mention} leveled up!',
                        'blocked-chars': '!@#$%'
                    };
                    return mockValues[customId] || 'test-value';
                }
            },
            
            // Mock interaction type checks
            isChatInputCommand: () => type === 1,
            isButton: () => type === 2,
            isStringSelectMenu: () => type === 3,
            isModalSubmit: () => type === 5
        };
        
        // Set the guild reference in member object for permission checking
        mockInteraction.member.guild = mockInteraction.guild;
        
        return mockInteraction;
    }

    /**
     * Record successful test
     */
    recordSuccess(testName, feature, timing = null) {
        this.testResults.passedTests++;
        this.testResults.featureCoverage[feature].passed++;
        
        if (timing) {
            this.testResults.timingData.push({
                test: testName,
                feature: feature,
                duration: timing,
                timestamp: Date.now()
            });
        }
        
        this.testResults.testDetails.push({
            name: testName,
            feature: feature,
            status: 'PASSED',
            duration: timing,
            timestamp: Date.now()
        });
        
        console.log(`✅ ${testName} (${timing}ms)`);
    }

    /**
     * Record failed test
     */
    recordError(testName, feature, error, context = {}) {
        this.testResults.failedTests++;

        this.testResults.testDetails.push({
            name: testName,
            feature: feature,
            status: 'FAILED',
            error: error,
            context: context,
            timestamp: Date.now()
        });

        console.log(`❌ ${testName} failed: ${error}`);
    }

    /**
     * Execute a single test with enhanced error tracking and Discord API validation
     */
    async executeTest(testName, feature, testFunction) {
        const startTime = Date.now();
        this.testResults.totalTests++;
        this.testResults.featureCoverage[feature].total++;

        // Reset error trackers for this test
        this.errorTracker.reset();
        this.validationDetector.reset();

        // CRITICAL FIX: Track Discord API errors in console output during test execution
        const originalConsoleError = console.error;
        const testDiscordAPIErrors = [];

        console.error = (...args) => {
            const message = args.join(' ');

            // CRITICAL: Detect Discord API error patterns in console output
            if (message.includes('Error: Unknown interaction') ||
                message.includes('code: 10062') ||
                message.includes('Error: Interaction already acknowledged') ||
                message.includes('code: 40060') ||
                message.includes('InteractionNotReplied') ||
                message.includes('Error in execute:')) {

                testDiscordAPIErrors.push({
                    testName,
                    feature,
                    message,
                    timestamp: Date.now(),
                    code: message.includes('10062') ? 10062 :
                          message.includes('40060') ? 40060 : 'UNKNOWN'
                });
            }

            // Call original console.error
            originalConsoleError.apply(console, args);
        };

        try {
            // Execute the test function with enhanced error detection
            await testFunction();
            const duration = Date.now() - startTime;

            // CRITICAL FIX: Check for Discord API errors captured from console output
            if (testDiscordAPIErrors.length > 0) {
                // Add to global Discord API errors tracking
                this.testResults.discordAPIErrors.push(...testDiscordAPIErrors);

                const errorMessages = testDiscordAPIErrors.map(e => `${e.code}: ${e.message}`).join('; ');
                throw new Error(`Discord API errors detected in console output: ${errorMessages}`);
            }

            // Validate 3-second timeout requirement (Discord API constraint)
            if (duration > this.DISCORD_INTERACTION_TIMEOUT) {
                throw new Error(`Test exceeded Discord 3-second timeout (${duration}ms)`);
            }

            // Check for critical console errors during test execution
            const criticalErrors = this.errorTracker.getCriticalErrors();
            if (criticalErrors.length > 0) {
                // Log the critical errors for analysis
                this.testResults.consoleErrors.push({
                    testName,
                    feature,
                    errors: criticalErrors,
                    timestamp: Date.now()
                });

                // Fail the test if critical errors occurred
                const errorMessages = criticalErrors.map(e => e.message).join('; ');
                throw new Error(`Critical console errors detected: ${errorMessages}`);
            }

            // Check for Discord API errors during test execution
            const discordErrors = this.testResults.discordAPIErrors.filter(e =>
                e.timestamp >= startTime && e.timestamp <= Date.now()
            );
            if (discordErrors.length > 0) {
                const errorMessages = discordErrors.map(e => `${e.code}: ${e.message}`).join('; ');
                throw new Error(`Discord API errors detected: ${errorMessages}`);
            }

            // Check for interaction timing issues
            const timingIssues = this.testResults.interactionTimingIssues.filter(i =>
                i.timestamp >= startTime && i.timestamp <= Date.now()
            );
            if (timingIssues.length > 0) {
                const issueMessages = timingIssues.map(i => i.issue).join('; ');
                throw new Error(`Interaction timing issues detected: ${issueMessages}`);
            }

            // Check for validation errors (undefined values in Discord.js builders)
            const validationErrors = this.validationDetector.getValidationErrors();
            if (validationErrors.length > 0) {
                this.testResults.validationErrors.push({
                    testName,
                    feature,
                    errors: validationErrors,
                    timestamp: Date.now()
                });

                const errorMessages = validationErrors.map(e =>
                    `${e.builderType}.${e.method}: ${e.field} is undefined`
                ).join('; ');
                throw new Error(`Validation errors detected: ${errorMessages}`);
            }

            this.recordSuccess(testName, feature, duration);
            return true;
        } catch (error) {
            // CRITICAL FIX: Add any console-captured Discord API errors to the error
            if (testDiscordAPIErrors.length > 0) {
                this.testResults.discordAPIErrors.push(...testDiscordAPIErrors);
            }
            const duration = Date.now() - startTime;

            // Enhanced error categorization for production issues
            let errorCategory = 'GENERAL';
            if (error.code) {
                errorCategory = 'DISCORD_API';
                this.testResults.discordAPIErrors.push({
                    testName,
                    feature,
                    code: error.code,
                    message: error.message,
                    timestamp: Date.now()
                });

                // Log specific production error patterns
                if (error.code === 10062) {
                    console.log(`🚨 PRODUCTION ERROR DETECTED: Unknown interaction (${testName})`);
                } else if (error.code === 40060) {
                    console.log(`🚨 PRODUCTION ERROR DETECTED: Interaction already acknowledged (${testName})`);
                }
            } else if (error.message.includes('timeout')) {
                errorCategory = 'TIMEOUT';
            } else if (error.message.includes('console errors')) {
                errorCategory = 'CONSOLE_ERROR';
            } else if (error.message.includes('timing issues')) {
                errorCategory = 'INTERACTION_TIMING';
            } else if (error.message.includes('validation errors')) {
                errorCategory = 'VALIDATION_ERROR';
            } else if (error.builderType) {
                // Catch Discord.js builder validation errors
                errorCategory = 'BUILDER_VALIDATION';
                console.log(`🚨 BUILDER VALIDATION ERROR: ${error.builderType}.${error.method} - ${error.field} is undefined`);
            }

            this.recordError(testName, feature, error.message, {
                duration,
                errorCategory,
                errorCode: error.code
            });
            return false;
        } finally {
            // CRITICAL FIX: Always restore original console.error
            console.error = originalConsoleError;
        }
    }

    /**
     * Test Items System - Create Item Workflow
     */
    async testItemsCreateWorkflow() {
        console.log('\n📦 Testing Items System - Create Item Workflow...');

        // Test 1: Initial items interface load
        await this.executeTest('Items Interface Load', 'items', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'items' });
            await items.execute(interaction);
        });

        // Test 2: Add new item selection
        await this.executeTest('Items Add New Selection', 'items', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'items-select-guild',
                values: ['add-new']
            });
            await items.select(interaction, []);
        });

        // Test 3: Item type selection
        await this.executeTest('Items Type Selection', 'items', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'items-type-select',
                values: ['ANIMAL']
            });
            await items.select(interaction, []);
        });

        // Test 4: Item rarity selection
        await this.executeTest('Items Rarity Selection', 'items', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'items-rarity-select',
                values: ['COMMON']
            });
            await items.select(interaction, []);
        });

        // Test 5: Item name modal
        await this.executeTest('Items Name Modal', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-name-button'
            });
            await items.buttons(interaction, []);
        });

        // Test 6: Item name modal submission
        await this.executeTest('Items Name Modal Submit', 'items', async () => {
            const interaction = this.createMockInteraction(5, {
                customId: 'items-name-modal'
            });
            await items.modalSubmit(interaction);
        });

        // Test 7: Item description modal
        await this.executeTest('Items Description Modal', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-description-button'
            });
            await items.buttons(interaction, []);
        });

        // Test 8: Item description modal submission
        await this.executeTest('Items Description Modal Submit', 'items', async () => {
            const interaction = this.createMockInteraction(5, {
                customId: 'items-description-modal'
            });
            await items.modalSubmit(interaction);
        });

        // Test 9: Drop location selection
        await this.executeTest('Items Drop Location Selection', 'items', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'items-drop-location-select',
                values: ['TEXT', 'VOICE']
            });
            await items.select(interaction, []);
        });

        // Test 10: Final item creation
        await this.executeTest('Items Final Creation', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-create-final'
            });
            await items.buttons(interaction, []);
        });
    }

    /**
     * Test Items System - Edit Item Workflow
     */
    async testItemsEditWorkflow() {
        console.log('\n📝 Testing Items System - Edit Item Workflow...');

        // First create an item to edit
        const itemCol = mongoClient.db('test').collection('custom_items');
        const testItem = {
            _id: 'test-item-id',
            name: 'Test Animal',
            type: 'ANIMAL',
            rarity: 'COMMON',
            emote: '🐻',
            parameters: { weight: '50 lbs' },
            description: 'A test animal',
            dropLocations: ['TEXT'],
            createdBy: this.testUserId,
            guildId: this.testGuildId
        };
        await itemCol.insertOne(testItem);

        // Test 1: Select existing item for editing
        await this.executeTest('Items Edit Selection', 'items', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'items-select-guild',
                values: ['test-item-id']
            });
            await items.select(interaction, []);
        });

        // Test 2: Edit item name
        await this.executeTest('Items Edit Name', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-name-button'
            });
            await items.buttons(interaction, []);
        });

        // Test 3: Update item
        await this.executeTest('Items Update Final', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-create-final'
            });
            await items.buttons(interaction, []);
        });

        // Test 4: Toggle item disable
        await this.executeTest('Items Toggle Disable', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-toggle-disable'
            });
            await items.buttons(interaction, []);
        });

        // Test 5: Delete item (requires 3 clicks)
        await this.executeTest('Items Delete Click 1', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-delete-item'
            });
            await items.buttons(interaction, []);
        });

        await this.executeTest('Items Delete Click 2', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-delete-item'
            });
            await items.buttons(interaction, []);
        });

        await this.executeTest('Items Delete Final', 'items', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-delete-item'
            });
            await items.buttons(interaction, []);
        });
    }

    /**
     * Test Levels System - Level Management Workflow
     */
    async testLevelsSystemWorkflow() {
        console.log('\n📊 Testing Levels System - Level Management Workflow...');

        // Test 1: EXP interface load
        await this.executeTest('EXP Interface Load', 'levels', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'exp' });
            await exp.execute(interaction);
        });

        // Test 2: Levels configuration selection
        await this.executeTest('EXP Levels Config Selection', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-config-select',
                values: ['levels']
            });
            await exp.select(interaction, []);
        });

        // Test 3: Add new level
        await this.executeTest('EXP Add Level', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-levels-config',
                values: ['add-level']
            });
            await exp.select(interaction, []);
        });

        // Test 4: Level role selection
        await this.executeTest('EXP Level Role Selection', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-level-role-select',
                values: ['123456789012345678']
            });
            await exp.select(interaction, []);
        });

        // Test 5: Level EXP selection
        await this.executeTest('EXP Level EXP Selection', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-level-exp-select',
                values: ['1000']
            });
            await exp.select(interaction, []);
        });

        // Test 6: Create level confirmation
        await this.executeTest('EXP Create Level Confirm', 'levels', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'exp-create-level-confirm'
            });
            await exp.buttons(interaction, []);
        });

        // Test 7: Edit level message template
        await this.executeTest('EXP Edit Level Message', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-levels-config',
                values: ['edit-level-msg']
            });
            await exp.select(interaction, []);
        });

        // Test 8: Level message modal submission
        await this.executeTest('EXP Level Message Modal Submit', 'levels', async () => {
            const interaction = this.createMockInteraction(5, {
                customId: 'exp-level-msg-template-modal'
            });
            await exp.modalSubmit(interaction);
        });

        // Test 9: Text EXP configuration
        await this.executeTest('EXP Text Config', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-config-select',
                values: ['text']
            });
            await exp.select(interaction, []);
        });

        // Test 10: Voice EXP configuration
        await this.executeTest('EXP Voice Config', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-config-select',
                values: ['voice']
            });
            await exp.select(interaction, []);
        });
    }

    /**
     * Test Global Levels System
     */
    async testGlobalLevelsSystem() {
        console.log('\n🌍 Testing Global Levels System...');

        // Test 1: Global levels interface (owner only) - handled by owner.js
        await this.executeTest('Global Levels Interface Load', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'owner-features',
                values: ['global_levels'],
                userId: this.testUserId
            });
            await owner.select(interaction, []);
        });

        // Test 2: Create new global level
        await this.executeTest('Global Level Create', 'levels', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'global-level-create-final',
                userId: this.testUserId
            });
            await owner.buttons(interaction, []);
        });

        // Test 3: Global level name configuration
        await this.executeTest('Global Level Name Config', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'global-level-config-select',
                values: ['name'],
                userId: this.testUserId
            });
            await owner.select(interaction, []);
        });

        // Test 4: Global level EXP configuration
        await this.executeTest('Global Level EXP Config', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'global-level-exp-select',
                values: ['1000'],
                userId: this.testUserId
            });
            await owner.select(interaction, []);
        });

        // Test 5: Global level rewards configuration
        await this.executeTest('Global Level Rewards Config', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'global-level-stars-select',
                values: ['1000'],
                userId: this.testUserId
            });
            await owner.select(interaction, []);
        });
    }

    /**
     * Test Sticky Roles System
     */
    async testStickyRolesSystem() {
        console.log('\n🏷️ Testing Sticky Roles System...');

        // Test 1: Sticky roles interface load
        await this.executeTest('Sticky Interface Load', 'sticky', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'sticky' });
            await sticky.execute(interaction);
        });

        // Test 2: Add sticky role
        await this.executeTest('Sticky Add Role', 'sticky', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'sticky-role-select',
                values: ['123456789012345678']
            });
            await sticky.select(interaction, []);
        });

        // Test 3: Remove sticky role
        await this.executeTest('Sticky Remove Role', 'sticky', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'sticky-remove-select',
                values: ['123456789012345678']
            });
            await sticky.select(interaction, []);
        });

        // Test 4: Sticky nickname configuration
        await this.executeTest('Sticky Nickname Config', 'sticky', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'sticky-nick-toggle'
            });
            await sticky.buttons(interaction, []);
        });
    }

    /**
     * Test Logging System
     */
    async testLoggingSystem() {
        console.log('\n📝 Testing Logging System...');

        // Test 1: Logs interface load
        await this.executeTest('Logs Interface Load', 'logs', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'logs' });
            await logs.execute(interaction);
        });

        // Test 2: Add log channel
        await this.executeTest('Logs Add Channel', 'logs', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'logs-channel-select',
                values: ['123456789012345680']
            });
            await logs.selectChannel(interaction);
        });

        // Test 3: Configure log events
        await this.executeTest('Logs Configure Events', 'logs', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'logs-core-event-select-123456789012345680',
                values: ['messageUpdate', 'messageDelete']
            });
            await logs.selectCoreEvents(interaction);
        });

        // Test 4: Enable/disable logging
        await this.executeTest('Logs Toggle Enable', 'logs', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'logs-enabler'
            });
            await logs.toggleEnabled(interaction);
        });

        // Test 5: Remove log channel (using specialty events as example)
        await this.executeTest('Logs Remove Channel', 'logs', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'logs-specialty-event-select-123456789012345680',
                values: []
            });
            await logs.selectSpecialtyEvents(interaction);
        });
    }

    /**
     * Test User Settings and Profile System
     */
    async testUserSettingsSystem() {
        console.log('\n👤 Testing User Settings and Profile System...');

        // Test 1: User profile load
        await this.executeTest('User Profile Load', 'userSettings', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'you' });
            await you.execute(interaction);
        });

        // Test 2: Settings page navigation
        await this.executeTest('User Settings Navigation', 'userSettings', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'you-hub-menu',
                values: ['settings']
            });
            await you.select(interaction, []);
        });

        // Test 3: Toggle item DM notifications
        await this.executeTest('User Toggle Item DM', 'userSettings', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'you-toggle-item-dm'
            });
            await you.buttons(interaction, []);
        });

        // Test 4: Toggle global level DM notifications
        await this.executeTest('User Toggle Global Level DM', 'userSettings', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'you-toggle-global-level-dm'
            });
            await you.buttons(interaction, []);
        });

        // Test 5: Toggle notification center
        await this.executeTest('User Toggle Notification Center', 'userSettings', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'you-toggle-notification-center'
            });
            await you.buttons(interaction, []);
        });

        // Test 6: Inventory navigation
        await this.executeTest('User Inventory Navigation', 'userSettings', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'you-hub-menu',
                values: ['inventory']
            });
            await you.select(interaction, []);
        });

        // Test 7: Daily rewards (Starfall) navigation
        await this.executeTest('User Daily Rewards Navigation', 'userSettings', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'you-hub-menu',
                values: ['daily']
            });
            await you.select(interaction, []);
        });

        // Test 8: Starfall button interaction
        await this.executeTest('User Starfall Button', 'userSettings', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'starfall-button-1'
            });
            await you.buttons(interaction, []);
        });
    }

    /**
     * Test Administrative Features
     */
    async testAdministrativeFeatures() {
        console.log('\n⚙️ Testing Administrative Features...');

        // Test 1: Owner panel access
        await this.executeTest('Owner Panel Access', 'admin', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'owner',
                userId: this.testUserId
            });
            await owner.execute(interaction);
        });

        // Test 2: Feature enable/disable toggle (Items)
        await this.executeTest('Admin Toggle Items Feature', 'admin', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'items-global-disable'
            });
            await items.buttons(interaction, []);
        });

        // Test 3: Feature enable/disable toggle (EXP)
        await this.executeTest('Admin Toggle EXP Feature', 'admin', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'exp-global-disable'
            });
            await exp.buttons(interaction, []);
        });

        // Test 4: Global item DM notifications toggle
        await this.executeTest('Admin Toggle Global Item DM', 'admin', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'owner-toggle-item-dm',
                userId: this.testUserId
            });
            await owner.buttons(interaction, []);
        });

        // Test 5: Permission boundary test (regular user with admin permissions)
        await this.executeTest('Admin Permission Boundary Test', 'admin', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'items',
                userId: this.regularUserId
            });
            // Mock admin permissions for this test
            interaction.member.permissions.has = (permission) => {
                return permission === 'Administrator' || permission === 'ManageGuild' || permission === 'KickMembers';
            };
            // Should work with admin access
            await items.execute(interaction);
        });
    }

    /**
     * Test Permission Boundaries
     */
    async testPermissionBoundaries() {
        console.log('\n🔒 Testing Permission Boundaries...');

        // Test server owner permissions
        await this.executeTest('Server Owner Full Access', 'admin', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'items',
                userId: this.testUserId // Server owner
            });
            await items.execute(interaction);
        });

        // Test regular user with admin permissions
        await this.executeTest('Admin User Access', 'admin', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'items',
                userId: this.regularUserId
            });
            // Mock admin permissions
            interaction.member.permissions.has = () => true;
            await items.execute(interaction);
        });

        // Test regular user without permissions
        await this.executeTest('No Permission User Access', 'admin', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'items',
                userId: this.regularUserId
            });
            // Mock no permissions
            interaction.member.permissions.has = () => false;
            await items.execute(interaction);
        });
    }

    /**
     * Test Form Validation and Edge Cases
     */
    async testFormValidationAndEdgeCases() {
        console.log('\n✅ Testing Form Validation and Edge Cases...');

        // Test 1: Invalid item name (too long)
        await this.executeTest('Invalid Item Name Validation', 'items', async () => {
            const interaction = this.createMockInteraction(5, {
                customId: 'items-name-modal'
            });
            // Override mock to return invalid data
            interaction.fields.getTextInputValue = () => 'A'.repeat(100); // Too long
            await items.modalSubmit(interaction);
        });

        // Test 2: Empty required fields
        await this.executeTest('Empty Required Fields Validation', 'items', async () => {
            const interaction = this.createMockInteraction(5, {
                customId: 'items-description-modal'
            });
            interaction.fields.getTextInputValue = () => ''; // Empty
            await items.modalSubmit(interaction);
        });

        // Test 3: Invalid EXP values
        await this.executeTest('Invalid EXP Values Validation', 'levels', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'exp-level-exp-select',
                values: ['-1'] // Invalid negative value
            });
            await exp.select(interaction, []);
        });

        // Test 4: Disabled option selection
        await this.executeTest('Disabled Option Selection', 'items', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'items-select-guild',
                values: ['disabled-option'],
                userId: this.regularUserId
            });
            interaction.member.permissions.has = () => false; // No permissions
            await items.select(interaction, []);
        });
    }

    /**
     * Test Changelog System
     */
    async testChangelogSystem() {
        console.log('\n📋 Testing Changelog System...');

        // Test 1: Changelog select menu interaction
        await this.executeTest('Changelog Version Selection', 'changelog', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'changelog-select',
                values: ['1.0.0']
            });
            await changelog.select(interaction, []);
        });

        // Test 2: Production error scenario - changelog select timeout
        await this.executeTest('Changelog Select Timeout', 'changelog', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(3, {
                    customId: 'changelog-select',
                    values: ['1.0.0']
                });
                await changelog.select(interaction, []);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test Clear Data System
     */
    async testClearDataSystem() {
        console.log('\n🗑️ Testing Clear Data System...');

        // Test 1: Clear data select menu interaction
        await this.executeTest('Clear Data Selection', 'clearData', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'clear-data-select',
                values: ['clear-temp']
            });
            await clearData.select(interaction, []);
        });

        // Test 2: Production error scenario - clear data timeout
        await this.executeTest('Clear Data Timeout', 'clearData', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(3, {
                    customId: 'clear-data-select',
                    values: ['clear-temp']
                });
                await clearData.select(interaction, []);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test Dehoist System
     */
    async testDehoistSystem() {
        console.log('\n🚫 Testing Dehoist System...');

        // Test 1: Dehoist interface load
        await this.executeTest('Dehoist Interface Load', 'dehoist', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'dehoist' });
            await dehoist.execute(interaction);
        });

        // Test 2: Dehoist button interactions
        await this.executeTest('Dehoist Button Interaction', 'dehoist', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'dehoist-scan-button'
            });
            await dehoist.buttons(interaction, []);
        });

        // Test 3: Dehoist select menu interactions
        await this.executeTest('Dehoist Select Menu', 'dehoist', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'dehoist-config-select',
                values: ['enable']
            });
            await dehoist.selectMenu(interaction);
        });

        // Test 4: Production error scenario - dehoist timeout
        await this.executeTest('Dehoist Execute Timeout', 'dehoist', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, { commandName: 'dehoist' });
                await dehoist.execute(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test Features Menu System
     */
    async testFeaturesMenuSystem() {
        console.log('\n🎛️ Testing Features Menu System...');

        // Test 1: Build select menu with different configurations
        await this.executeTest('Features Menu Build (Owner)', 'featuresMenu', async () => {
            const menu = featuresMenu.buildSelectMenu(false, this.testUserId, null);
            if (!menu || !menu.components || !menu.components[0]) {
                throw new Error('Features menu not built correctly');
            }
        });

        // Test 2: Build select menu with 17 option
        await this.executeTest('Features Menu Build (With 17)', 'featuresMenu', async () => {
            const menu = featuresMenu.buildSelectMenu(true, this.testUserId, 'items');
            if (!menu || !menu.components || !menu.components[0]) {
                throw new Error('Features menu with 17 option not built correctly');
            }
        });

        // Test 3: Build select menu for regular user
        await this.executeTest('Features Menu Build (Regular User)', 'featuresMenu', async () => {
            const menu = featuresMenu.buildSelectMenu(false, this.regularUserId, null);
            if (!menu || !menu.components || !menu.components[0]) {
                throw new Error('Features menu for regular user not built correctly');
            }
        });
    }

    /**
     * Test Lookup System
     */
    async testLookupSystem() {
        console.log('\n🔍 Testing Lookup System...');

        // Test 1: Lookup command execution
        await this.executeTest('Lookup Command Execute', 'lookup', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'lookup'
            });
            // Add targetUser and targetMember to interaction
            interaction.targetUser = {
                id: this.regularUserId,
                tag: 'TestUser#1234',
                createdTimestamp: Date.now() - 86400000,
                fetchFlags: async () => ({ toArray: () => [] }),
                fetch: async () => { return interaction.targetUser; }, // Add missing fetch method
                displayAvatarURL: (options) => 'https://cdn.discordapp.com/embed/avatars/0.png'
            };
            interaction.targetMember = {
                joinedTimestamp: Date.now() - 3600000,
                fetch: async () => { return interaction.targetMember; }, // Add missing fetch method
                roles: {
                    cache: new Map([
                        ['123456789012345678', { id: '123456789012345678', name: 'Level 0' }]
                    ])
                },
                nickname: null,
                premiumSince: null
            };
            await lookup.execute(interaction);
        });

        // Test 2: Lookup self-reference check
        await this.executeTest('Lookup Self Reference', 'lookup', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'lookup',
                userId: this.testUserId
            });
            interaction.targetUser = {
                id: this.testUserId,
                tag: 'TestOwner#1234',
                fetch: async () => { return interaction.targetUser; }
            };
            await lookup.execute(interaction);
        });

        // Test 3: Production error scenario - lookup timeout
        await this.executeTest('Lookup Execute Timeout', 'lookup', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, {
                    commandName: 'lookup'
                });
                interaction.targetUser = {
                    id: this.regularUserId,
                    tag: 'TestUser#1234',
                    createdTimestamp: Date.now() - 86400000,
                    fetchFlags: async () => ({ toArray: () => [] }),
                    fetch: async () => { return interaction.targetUser; },
                    displayAvatarURL: (options) => 'https://cdn.discordapp.com/embed/avatars/0.png'
                };
                interaction.targetMember = {
                    joinedTimestamp: Date.now() - 3600000,
                    fetch: async () => { return interaction.targetMember; },
                    roles: {
                        cache: new Map([
                            ['123456789012345678', { id: '123456789012345678', name: 'Level 0' }]
                        ])
                    },
                    nickname: null,
                    premiumSince: null
                };
                await lookup.execute(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test Opener System
     */
    async testOpenerSystem() {
        console.log('\n🔓 Testing Opener System...');

        // Test 1: Opener interface load
        await this.executeTest('Opener Interface Load', 'opener', async () => {
            const interaction = this.createMockInteraction(1, { commandName: 'opener' });
            await opener.execute(interaction);
        });

        // Test 2: Opener button interactions
        await this.executeTest('Opener Button Interaction', 'opener', async () => {
            const interaction = this.createMockInteraction(2, {
                customId: 'opener-enable-button'
            });
            await opener.buttons(interaction, []);
        });

        // Test 3: Opener thread select interactions
        await this.executeTest('Opener Thread Select', 'opener', async () => {
            const interaction = this.createMockInteraction(3, {
                customId: 'opener-thread-select',
                values: ['123456789012345678']
            });
            await opener.threadSelect(interaction);
        });

        // Test 4: Production error scenario - opener timeout
        await this.executeTest('Opener Execute Timeout', 'opener', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, { commandName: 'opener' });
                await opener.execute(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test Owner Module Systems
     */
    async testOwnerModuleSystems() {
        console.log('\n👑 Testing Owner Module Systems...');

        // Test 1: Owner global levels interface
        await this.executeTest('Owner Global Levels Interface', 'ownerModules', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'owner',
                userId: this.testUserId
            });
            // Test buildGlobalLevelsContainer function
            const container = await ownerGlobalLevels.buildGlobalLevelsContainer(interaction);
            if (!container) {
                throw new Error('Global levels container not built correctly');
            }
        });

        // Test 2: Owner servers container
        await this.executeTest('Owner Servers Container', 'ownerModules', async () => {
            const mockClient = {
                guilds: {
                    cache: {
                        map: (callback) => {
                            const guilds = [{
                                id: '123456789012345678',
                                name: 'Test Guild',
                                memberCount: 100,
                                joinedAt: new Date()
                            }];
                            return guilds.map(callback);
                        }
                    }
                }
            };
            const container = await ownerServers.buildServersContainer(mockClient);
            if (!container) {
                throw new Error('Servers container not built correctly');
            }
        });

        // Test 3: Owner status container
        await this.executeTest('Owner Status Container', 'ownerModules', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'owner',
                userId: this.testUserId
            });
            const container = await ownerStatus.buildStatusContainer(interaction);
            if (!container) {
                throw new Error('Status container not built correctly');
            }
        });

        // Test 4: Owner join notification functions
        await this.executeTest('Owner Join Notification', 'ownerModules', async () => {
            const config = await ownerJoinNotification.getJoinNotificationConfig();
            if (typeof config !== 'object') {
                throw new Error('Join notification config not retrieved correctly');
            }
        });

        // Test 5: Production error scenario - owner modules timeout
        await this.executeTest('Owner Modules Timeout', 'ownerModules', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, {
                    commandName: 'owner',
                    userId: this.testUserId
                });
                const container = await ownerGlobalLevels.buildGlobalLevelsContainer(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test EXP Database System
     */
    async testExpDbSystem() {
        console.log('\n📊 Testing EXP Database System...');

        // Test 1: EXP database operations
        await this.executeTest('EXP DB Operations', 'levels', async () => {
            // Test database utility functions from exp_db.js
            // Since exp_db.js contains utility functions, we test them indirectly
            const interaction = this.createMockInteraction(1, { commandName: 'exp' });
            await exp.execute(interaction);
        });

        // Test 2: Production error scenario - exp db timeout
        await this.executeTest('EXP DB Timeout', 'levels', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, { commandName: 'exp' });
                await exp.execute(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test Instantiate System
     */
    async testInstantiateSystem() {
        console.log('\n⚡ Testing Instantiate System...');

        // Test 1: Instantiate command execution
        await this.executeTest('Instantiate Command Execute', 'admin', async () => {
            const interaction = this.createMockInteraction(1, {
                commandName: 'instantiate',
                userId: this.testUserId
            });
            // Mock channel.send method
            interaction.channel.send = async () => ({ id: 'mock-message-id' });
            await instantiate.execute(interaction);
        });

        // Test 2: Production error scenario - instantiate timeout
        await this.executeTest('Instantiate Execute Timeout', 'admin', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, {
                    commandName: 'instantiate',
                    userId: this.testUserId
                });
                interaction.channel.send = async () => ({ id: 'mock-message-id' });
                await instantiate.execute(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });
    }

    /**
     * Test specific production error scenarios that are failing in live bot
     */
    async testProductionErrorScenarios() {
        console.log('\n🚨 Testing Production Error Scenarios...');

        // Test 1: /17 command line 171 deferReply timeout (Error 10062)
        await this.executeTest('17 Command deferReply Timeout', 'admin', async () => {
            // Force high network latency to simulate production conditions
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(1, { commandName: '17' });
                // This should trigger the 10062 error due to realistic network simulation
                await require('../commands/utility/17.js').execute(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });

        // Test 2: logs.js selectChannel line 738 deferUpdate timeout (Error 10062)
        await this.executeTest('Logs selectChannel deferUpdate Timeout', 'logs', async () => {
            const originalErrorRate = this.productionErrorRate;
            this.productionErrorRate = 0.8; // 80% chance of error for this test

            try {
                const interaction = this.createMockInteraction(3, {
                    values: ['123456789012345680'],
                    customId: 'logs-channel-select'
                });
                // This should trigger the 10062 error in deferUpdate
                await require('../commands/utility/logs.js').selectChannel(interaction);
            } finally {
                this.productionErrorRate = originalErrorRate;
            }
        });

        // Test 3: items.js line 1113 StringSelectMenuBuilder undefined values
        await this.executeTest('Items StringSelectMenuBuilder Validation', 'items', async () => {
            // Create a scenario where item data might have undefined values
            const interaction = this.createMockInteraction(1, { commandName: 'items' });

            // Mock corrupted item data that would cause undefined values
            const originalFindOne = require('../utils/database-optimizer.js').optimizedFindOne;
            require('../utils/database-optimizer.js').optimizedFindOne = async (collection, query) => {
                if (collection === 'custom_items') {
                    return {
                        items: [{
                            id: 'test-item',
                            name: 'Test Item',
                            type: undefined, // This should trigger validation error
                            rarity: 'common',
                            emote: '📦'
                        }]
                    };
                }
                return originalFindOne(collection, query);
            };

            try {
                await require('../commands/utility/items.js').execute(interaction);
            } finally {
                // Restore original function
                require('../utils/database-optimizer.js').optimizedFindOne = originalFindOne;
            }
        });

        // Test 4: Error handling after initial interaction failure (Error 40060)
        await this.executeTest('Error Response After Initial Failure', 'admin', async () => {
            const interaction = this.createMockInteraction(1, { commandName: '17' });

            // Force the interaction to fail initially
            interaction.deferReply = async () => {
                const error = new Error('Unknown interaction');
                error.code = 10062;
                throw error;
            };

            // The error handling should trigger 40060 when trying to respond
            try {
                await require('../commands/utility/17.js').execute(interaction);
            } catch (error) {
                // This should catch the cascading error handling issues
                if (error.code !== 10062 && error.code !== 40060) {
                    throw new Error(`Expected Discord API error codes 10062 or 40060, got: ${error.code || 'no code'}`);
                }
            }
        });
    }

    /**
     * Run all test suites
     */
    async runAllTests() {
        console.log('🚀 Starting Comprehensive Deep-Dive Test Suite...\n');
        const overallStartTime = Date.now();

        try {
            // Initialize test environment
            await this.initialize();

            // Run all test suites
            await this.testItemsCreateWorkflow();
            await this.testItemsEditWorkflow();
            await this.testLevelsSystemWorkflow();
            await this.testGlobalLevelsSystem();
            await this.testStickyRolesSystem();
            await this.testLoggingSystem();
            await this.testUserSettingsSystem();
            await this.testAdministrativeFeatures();
            await this.testPermissionBoundaries();
            await this.testFormValidationAndEdgeCases();

            // Test additional command coverage
            await this.testChangelogSystem();
            await this.testClearDataSystem();
            await this.testDehoistSystem();
            await this.testFeaturesMenuSystem();
            await this.testLookupSystem();
            await this.testOpenerSystem();
            await this.testOwnerModuleSystems();
            await this.testExpDbSystem();
            await this.testInstantiateSystem();

            // Test specific production error scenarios
            await this.testProductionErrorScenarios();

            // Generate final report
            const overallDuration = Date.now() - overallStartTime;
            await this.generateFinalReport(overallDuration);

        } catch (error) {
            console.error('❌ Test suite initialization failed:', error);
            process.exit(1);
        } finally {
            // Clean up test data and restore original builders
            await this.cleanupTestData();
            this.validationDetector.restoreOriginalBuilders();
        }
    }

    /**
     * Generate comprehensive test report
     */
    async generateFinalReport(overallDuration) {
        console.log('\n' + '='.repeat(80));
        console.log('📊 COMPREHENSIVE DEEP-DIVE TEST SUITE RESULTS');
        console.log('='.repeat(80));

        // Overall statistics
        const successRate = ((this.testResults.passedTests / this.testResults.totalTests) * 100).toFixed(1);
        console.log(`\n📈 OVERALL STATISTICS:`);
        console.log(`   Total Tests: ${this.testResults.totalTests}`);
        console.log(`   Passed: ${this.testResults.passedTests}`);
        console.log(`   Failed: ${this.testResults.failedTests}`);
        console.log(`   Success Rate: ${successRate}%`);
        console.log(`   Total Duration: ${overallDuration}ms`);

        // Feature coverage breakdown
        console.log(`\n🎯 FEATURE COVERAGE:`);
        Object.entries(this.testResults.featureCoverage).forEach(([feature, stats]) => {
            const featureSuccessRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : '0.0';
            console.log(`   ${feature.padEnd(15)}: ${stats.passed}/${stats.total} (${featureSuccessRate}%)`);
        });

        // Performance analysis
        console.log(`\n⚡ PERFORMANCE ANALYSIS:`);
        if (this.testResults.timingData.length > 0) {
            const avgDuration = this.testResults.timingData.reduce((sum, t) => sum + t.duration, 0) / this.testResults.timingData.length;
            const maxDuration = Math.max(...this.testResults.timingData.map(t => t.duration));
            const minDuration = Math.min(...this.testResults.timingData.map(t => t.duration));

            console.log(`   Average Response Time: ${avgDuration.toFixed(1)}ms`);
            console.log(`   Fastest Response: ${minDuration}ms`);
            console.log(`   Slowest Response: ${maxDuration}ms`);

            // Check 3-second timeout compliance
            const timeoutViolations = this.testResults.timingData.filter(t => t.duration > 3000);
            if (timeoutViolations.length > 0) {
                console.log(`   ⚠️  Timeout Violations: ${timeoutViolations.length}`);
            } else {
                console.log(`   ✅ All tests completed within 3-second timeout`);
            }
        }

        // Console Error Analysis
        console.log(`\n🔍 CONSOLE ERROR ANALYSIS:`);
        if (this.testResults.consoleErrors.length > 0) {
            console.log(`   ❌ Critical Console Errors Detected: ${this.testResults.consoleErrors.length}`);
            this.testResults.consoleErrors.forEach(errorGroup => {
                console.log(`   Test: ${errorGroup.testName} (${errorGroup.feature})`);
                errorGroup.errors.forEach(error => {
                    console.log(`     - ${error.message}`);
                });
            });
        } else {
            console.log(`   ✅ No critical console errors detected`);
        }

        // Discord API Error Analysis
        console.log(`\n🌐 DISCORD API ERROR ANALYSIS:`);
        if (this.testResults.discordAPIErrors.length > 0) {
            console.log(`   ❌ Discord API Errors Detected: ${this.testResults.discordAPIErrors.length}`);
            const errorsByCode = {};
            this.testResults.discordAPIErrors.forEach(error => {
                if (!errorsByCode[error.code]) errorsByCode[error.code] = [];
                errorsByCode[error.code].push(error);
            });
            Object.entries(errorsByCode).forEach(([code, errors]) => {
                console.log(`   Error ${code}: ${errors.length} occurrences`);
                errors.slice(0, 3).forEach(error => {
                    console.log(`     - ${error.testName}: ${error.message}`);
                });
            });
        } else {
            console.log(`   ✅ No Discord API errors detected`);
        }

        // Interaction Timing Issues Analysis
        console.log(`\n⏱️  INTERACTION TIMING ANALYSIS:`);
        if (this.testResults.interactionTimingIssues.length > 0) {
            console.log(`   ❌ Timing Issues Detected: ${this.testResults.interactionTimingIssues.length}`);
            const issuesByType = {};
            this.testResults.interactionTimingIssues.forEach(issue => {
                if (!issuesByType[issue.issue]) issuesByType[issue.issue] = 0;
                issuesByType[issue.issue]++;
            });
            Object.entries(issuesByType).forEach(([issueType, count]) => {
                console.log(`   ${issueType}: ${count} occurrences`);
            });
        } else {
            console.log(`   ✅ No interaction timing issues detected`);
        }

        // Validation Error Analysis (NEW)
        console.log(`\n🔍 VALIDATION ERROR ANALYSIS:`);
        if (this.testResults.validationErrors.length > 0) {
            console.log(`   ❌ Validation Errors Detected: ${this.testResults.validationErrors.length}`);
            this.testResults.validationErrors.forEach(validationError => {
                console.log(`   Test: ${validationError.testName} (${validationError.feature})`);
                validationError.errors.forEach(error => {
                    console.log(`     - ${error.builderType}.${error.method}: ${error.field} is undefined`);
                });
            });
        } else {
            console.log(`   ✅ No validation errors detected`);
        }

        // Unhandled Promise Rejection Analysis (NEW)
        console.log(`\n🚨 UNHANDLED PROMISE REJECTION ANALYSIS:`);
        if (this.testResults.unhandledRejections && this.testResults.unhandledRejections.length > 0) {
            console.log(`   ❌ Unhandled Promise Rejections Detected: ${this.testResults.unhandledRejections.length}`);
            this.testResults.unhandledRejections.forEach((rejection, index) => {
                console.log(`   Rejection ${index + 1}:`);
                console.log(`     - Reason: ${rejection.reason}`);
                console.log(`     - Timestamp: ${new Date(rejection.timestamp).toISOString()}`);
                if (rejection.stack) {
                    console.log(`     - Stack: ${rejection.stack.split('\n')[0]}`);
                }
            });
        } else {
            console.log(`   ✅ No unhandled promise rejections detected`);
        }

        // Failed tests details with enhanced categorization
        if (this.testResults.failedTests > 0) {
            console.log(`\n❌ FAILED TESTS ANALYSIS:`);
            const failedByCategory = {};
            this.testResults.testDetails
                .filter(test => test.status === 'FAILED')
                .forEach(test => {
                    const category = test.errorCategory || 'GENERAL';
                    if (!failedByCategory[category]) failedByCategory[category] = [];
                    failedByCategory[category].push(test);
                });

            Object.entries(failedByCategory).forEach(([category, tests]) => {
                console.log(`   ${category} (${tests.length} tests):`);
                tests.forEach(test => {
                    console.log(`     - ${test.name} (${test.feature}): ${test.error}`);
                });
            });
        }

        // Enhanced Success criteria validation
        console.log(`\n🎯 SUCCESS CRITERIA VALIDATION:`);
        const meetsSuccessRate = successRate === '100.0';
        const meetsTimeoutRequirement = this.testResults.timingData.every(t => t.duration <= 3000);
        const meetsMinimumTests = this.testResults.totalTests >= 20;
        const noConsoleErrors = this.testResults.consoleErrors.length === 0;
        const noDiscordAPIErrors = this.testResults.discordAPIErrors.length === 0;
        const noTimingIssues = this.testResults.interactionTimingIssues.length === 0;

        console.log(`   ✅ 100% Success Rate: ${meetsSuccessRate ? 'PASSED' : 'FAILED'}`);
        console.log(`   ✅ 3-Second Timeout: ${meetsTimeoutRequirement ? 'PASSED' : 'FAILED'}`);
        console.log(`   ✅ Minimum 20 Tests: ${meetsMinimumTests ? 'PASSED' : 'FAILED'}`);
        console.log(`   ✅ No Console Errors: ${noConsoleErrors ? 'PASSED' : 'FAILED'}`);
        console.log(`   ✅ No Discord API Errors: ${noDiscordAPIErrors ? 'PASSED' : 'FAILED'}`);
        console.log(`   ✅ No Timing Issues: ${noTimingIssues ? 'PASSED' : 'FAILED'}`);

        const overallSuccess = meetsSuccessRate && meetsTimeoutRequirement && meetsMinimumTests &&
                              noConsoleErrors && noDiscordAPIErrors && noTimingIssues;

        console.log('\n' + '='.repeat(80));
        if (overallSuccess) {
            console.log('🎉 COMPREHENSIVE TEST SUITE: PASSED');
            console.log('All success criteria met - bot features validated successfully!');
        } else {
            console.log('❌ COMPREHENSIVE TEST SUITE: FAILED');
            console.log('One or more success criteria not met - review failed tests above.');
        }
        console.log('='.repeat(80));

        // Exit with appropriate code
        process.exit(overallSuccess ? 0 : 1);
    }
}

// Export the test suite
module.exports = ComprehensiveDeepDiveTestSuite;
