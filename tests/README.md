# 🧪 Automated Discord Bot Testing Suite

This testing suite automatically discovers and tests **ALL** interactions in your Discord bot, including buttons, select menus, modals, and slash commands.

## 🚀 Quick Start

### Run All Tests
```bash
# Run comprehensive test suite (recommended)
node tests/comprehensive-test-runner.js

# Or run basic tests
node tests/run-interaction-tests.js
```

### Scan Only (Discovery)
```bash
# Just discover interactions without testing
node -e "const scanner = require('./tests/interaction-scanner.js'); const s = new scanner(); s.scanCodebase().then(() => s.printResults());"
```

## 📋 What Gets Tested

### ✅ Automatically Discovered:
- **Slash Commands** (`/17`, `/you`)
- **Buttons** (all `setCustomId` patterns)
- **Select Menus** (all select menu interactions)
- **Modals** (all modal submissions)

### 🔍 Discovery Process:
1. **Code Scanning**: Searches all `.js` files for interaction patterns
2. **Pattern Matching**: Uses regex to find `setCustomId`, `customId:`, and interaction handlers
3. **Smart Filtering**: Identifies interaction types based on naming conventions
4. **Test Generation**: Creates appropriate test data for each interaction type

## 📊 Test Reports

### Console Output:
```
🎯 COMPREHENSIVE TEST RESULTS
============================================
📊 Discovery Summary:
🔍 Total Interactions Found: 45
⚡ Commands: 2
🔘 Buttons: 28
📋 Select Menus: 12
📝 Modals: 3

🧪 Testing Summary:
📈 Coverage: 100%
✅ Passed: 43
❌ Failed: 2
⏭️ Skipped: 0
🎯 Success Rate: 95.56%
```

### JSON Reports:
- `comprehensive-report-[timestamp].json` - Full detailed report
- `test-report-[timestamp].json` - Basic test results

## 🔧 How It Works

### 1. **InteractionScanner**
- Recursively scans your codebase
- Finds all interaction patterns using regex
- Categorizes by interaction type
- Generates test data automatically

### 2. **InteractionTester**
- Creates mock Discord interactions
- Simulates button clicks, menu selections, modal submissions
- Captures errors and response times
- Tracks success/failure rates

### 3. **ComprehensiveTestRunner**
- Combines scanning and testing
- Provides detailed coverage analysis
- Generates actionable reports
- Gives recommendations for fixes

## 🎯 Mock Interaction Examples

### Button Test:
```javascript
// Simulates clicking a button with customId 'exp-disable'
const mockInteraction = {
    isButton: () => true,
    customId: 'exp-disable',
    update: async (data) => { /* mock response */ },
    guild: mockGuild,
    user: mockUser
};
```

### Select Menu Test:
```javascript
// Simulates selecting values from a menu
const mockInteraction = {
    isStringSelectMenu: () => true,
    customId: 'features-select',
    values: ['exp', 'dehoist'],
    update: async (data) => { /* mock response */ }
};
```

### Modal Test:
```javascript
// Simulates modal submission
const mockInteraction = {
    isModalSubmit: () => true,
    customId: 'items-create-modal',
    fields: {
        getTextInputValue: (id) => 'test-value'
    }
};
```

## 🚨 Error Detection

The suite catches and reports:
- **Runtime Errors**: Exceptions during interaction handling
- **Missing Handlers**: Interactions without corresponding code
- **Invalid Responses**: Malformed interaction responses
- **Performance Issues**: Slow interaction processing

## 💡 Best Practices

### Before Deployment:
```bash
# Run full test suite
node tests/comprehensive-test-runner.js

# Check exit code
echo $?  # 0 = all passed, 1 = some failed
```

### CI/CD Integration:
```yaml
# GitHub Actions example
- name: Test Bot Interactions
  run: node tests/comprehensive-test-runner.js
```

### Regular Testing:
- Run tests after adding new features
- Run tests before major deployments
- Include in your development workflow

## 🔧 Customization

### Add Custom Test Data:
```javascript
// In comprehensive-test-runner.js
const customTestData = {
    'my-special-button': { 
        specialProperty: 'value' 
    }
};
```

### Skip Certain Interactions:
```javascript
// In interaction-scanner.js
shouldSkipInteraction(customId) {
    const skipList = ['debug-only-button'];
    return skipList.includes(customId);
}
```

## 📈 Benefits

- **🚀 Faster Development**: Catch issues before manual testing
- **🛡️ Reliability**: Ensure all interactions work correctly
- **📊 Coverage**: Know exactly what's tested
- **🔄 Automation**: No more manual clicking through every feature
- **📋 Documentation**: Auto-generated interaction inventory

## 🆘 Troubleshooting

### Common Issues:

**"No guilds available for testing"**
- Make sure your bot is in at least one server
- Check bot permissions

**"Database connection failed"**
- Verify your `.env` file has correct `MONGO` connection string
- Ensure MongoDB is running

**"Command not found"**
- Check that command files are in the correct directory structure
- Verify command exports are correct

### Debug Mode:
```bash
# Add debug logging
DEBUG=true node tests/comprehensive-test-runner.js
```

---

**🎉 Happy Testing!** This suite will help you catch interaction bugs before your users do!
