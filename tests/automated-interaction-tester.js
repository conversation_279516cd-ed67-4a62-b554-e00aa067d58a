/**
 * Automated Interaction Testing System
 * Systematically tests all buttons, select menus, modals, and commands
 */

require('dotenv').config();
const { Client, GatewayIntentBits, Collection } = require('discord.js');
const fs = require('fs');
const path = require('path');

class InteractionTester {
    constructor() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMembers,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildVoiceStates
            ]
        });
        
        this.testResults = {
            commands: {},
            buttons: {},
            selectMenus: {},
            modals: {},
            errors: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0
            }
        };
        
        this.testGuildId = null;
        this.testChannelId = null;
        this.testUserId = null;
    }

    /**
     * Initialize the testing environment
     */
    async initialize() {
        console.log('🤖 Initializing Automated Interaction Tester...');
        
        // Load commands
        this.client.commands = new Collection();
        const commandsPath = path.join(__dirname, '../commands');
        await this.loadCommands(commandsPath);
        
        // Set up event handlers for testing
        this.setupTestEventHandlers();
        
        await this.client.login(process.env.TOKEN);
        
        // Wait for ready
        await new Promise(resolve => {
            this.client.once('ready', () => {
                console.log(`✅ Logged in as ${this.client.user.tag}`);
                resolve();
            });
        });
        
        // Set up test environment
        await this.setupTestEnvironment();
    }

    /**
     * Load all commands for testing
     */
    async loadCommands(dir) {
        const files = fs.readdirSync(dir);
        
        for (const file of files) {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory()) {
                await this.loadCommands(filePath);
            } else if (file.endsWith('.js')) {
                try {
                    const command = require(filePath);
                    if (command.data && command.execute) {
                        this.client.commands.set(command.data.name, command);
                        console.log(`📝 Loaded command: ${command.data.name}`);
                    }
                } catch (error) {
                    console.error(`❌ Error loading command ${file}:`, error.message);
                }
            }
        }
    }

    /**
     * Set up test environment
     */
    async setupTestEnvironment() {
        // Use the first available guild for testing
        const guild = this.client.guilds.cache.first();
        if (!guild) {
            throw new Error('No guilds available for testing');
        }
        
        this.testGuildId = guild.id;
        this.testUserId = this.client.user.id;
        
        // Find or create a test channel
        let testChannel = guild.channels.cache.find(ch => ch.name === 'bot-testing');
        if (!testChannel) {
            testChannel = guild.channels.cache.find(ch => ch.type === 0); // Text channel
        }
        
        if (!testChannel) {
            throw new Error('No suitable test channel found');
        }
        
        this.testChannelId = testChannel.id;
        
        console.log(`🏠 Test Guild: ${guild.name} (${guild.id})`);
        console.log(`📝 Test Channel: ${testChannel.name} (${testChannel.id})`);
    }

    /**
     * Set up event handlers for testing
     */
    setupTestEventHandlers() {
        // Load the actual interaction handler
        const interactionHandler = require('../events/interactionCreate.js');
        
        this.client.on('interactionCreate', async (interaction) => {
            try {
                await interactionHandler.execute(this.client, interaction);
            } catch (error) {
                this.recordError('interactionCreate', error, interaction);
            }
        });
    }

    /**
     * Create a mock interaction for testing
     */
    createMockInteraction(type, customId, options = {}) {
        const baseInteraction = {
            client: this.client,
            guild: this.client.guilds.cache.get(this.testGuildId),
            channel: this.client.channels.cache.get(this.testChannelId),
            user: this.client.user,
            member: this.client.guilds.cache.get(this.testGuildId)?.members.cache.get(this.testUserId),
            customId: customId,
            replied: false,
            deferred: false,
            ephemeral: false,
            ...options
        };

        // Add type-specific methods
        switch (type) {
            case 'button':
                return {
                    ...baseInteraction,
                    isButton: () => true,
                    isStringSelectMenu: () => false,
                    isModalSubmit: () => false,
                    isChatInputCommand: () => false,
                    isContextMenuCommand: () => false,
                    update: async (data) => {
                        console.log(`🔄 Button update: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    reply: async (data) => {
                        console.log(`💬 Button reply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    deferUpdate: async () => {
                        console.log(`⏳ Button defer: ${customId}`);
                        this.deferred = true;
                    },
                    editReply: async (data) => {
                        console.log(`✏️ Button editReply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    followUp: async (data) => {
                        console.log(`➡️ Button followUp: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    }
                };
                
            case 'selectMenu':
                return {
                    ...baseInteraction,
                    isButton: () => false,
                    isStringSelectMenu: () => true,
                    isModalSubmit: () => false,
                    isChatInputCommand: () => false,
                    isContextMenuCommand: () => false,
                    values: options.values || ['test-value'],
                    update: async (data) => {
                        console.log(`🔄 Select menu update: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    reply: async (data) => {
                        console.log(`💬 Select menu reply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    deferUpdate: async () => {
                        console.log(`⏳ Select menu defer: ${customId}`);
                        this.deferred = true;
                    },
                    editReply: async (data) => {
                        console.log(`✏️ Select menu editReply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    followUp: async (data) => {
                        console.log(`➡️ Select menu followUp: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    }
                };
                
            case 'modal':
                return {
                    ...baseInteraction,
                    isButton: () => false,
                    isStringSelectMenu: () => false,
                    isModalSubmit: () => true,
                    isChatInputCommand: () => false,
                    isContextMenuCommand: () => false,
                    fields: {
                        getTextInputValue: (id) => options.fieldValues?.[id] || 'test-value'
                    },
                    update: async (data) => {
                        console.log(`🔄 Modal update: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    reply: async (data) => {
                        console.log(`💬 Modal reply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    deferUpdate: async () => {
                        console.log(`⏳ Modal defer: ${customId}`);
                        this.deferred = true;
                    },
                    editReply: async (data) => {
                        console.log(`✏️ Modal editReply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    followUp: async (data) => {
                        console.log(`➡️ Modal followUp: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    }
                };
                
            case 'command':
                return {
                    ...baseInteraction,
                    isButton: () => false,
                    isStringSelectMenu: () => false,
                    isModalSubmit: () => false,
                    isChatInputCommand: () => true,
                    isContextMenuCommand: () => false,
                    commandName: customId,
                    options: {
                        getString: (name) => options.stringOptions?.[name] || null,
                        getInteger: (name) => options.integerOptions?.[name] || null,
                        getUser: (name) => options.userOptions?.[name] || null,
                        getChannel: (name) => options.channelOptions?.[name] || null
                    },
                    reply: async (data) => {
                        console.log(`💬 Command reply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    deferReply: async () => {
                        console.log(`⏳ Command defer: ${customId}`);
                        this.deferred = true;
                    },
                    editReply: async (data) => {
                        console.log(`✏️ Command editReply: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    },
                    followUp: async (data) => {
                        console.log(`➡️ Command followUp: ${customId}`, data ? 'with data' : 'no data');
                        return { id: 'mock-message' };
                    }
                };
        }
    }

    /**
     * Test a specific interaction
     */
    async testInteraction(type, customId, options = {}) {
        console.log(`\n🧪 Testing ${type}: ${customId}`);
        
        try {
            const mockInteraction = this.createMockInteraction(type, customId, options);
            
            // Emit the interaction event
            this.client.emit('interactionCreate', mockInteraction);
            
            // Wait a bit for async processing
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            this.recordSuccess(type, customId);
            console.log(`✅ ${type} ${customId} - PASSED`);
            
        } catch (error) {
            this.recordError(type, error, { customId });
            console.log(`❌ ${type} ${customId} - FAILED: ${error.message}`);
        }
    }

    /**
     * Record successful test
     */
    recordSuccess(type, customId) {
        if (!this.testResults[type + 's']) {
            this.testResults[type + 's'] = {};
        }
        this.testResults[type + 's'][customId] = { status: 'passed' };
        this.testResults.summary.total++;
        this.testResults.summary.passed++;
    }

    /**
     * Record test error
     */
    recordError(type, error, interaction) {
        const errorInfo = {
            type,
            customId: interaction.customId || interaction.commandName || 'unknown',
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.errors.push(errorInfo);
        
        if (!this.testResults[type + 's']) {
            this.testResults[type + 's'] = {};
        }
        this.testResults[type + 's'][errorInfo.customId] = { 
            status: 'failed', 
            error: error.message 
        };
        
        this.testResults.summary.total++;
        this.testResults.summary.failed++;
    }

    /**
     * Generate test report
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: this.testResults.summary,
            successRate: this.testResults.summary.total > 0 ? 
                (this.testResults.summary.passed / this.testResults.summary.total * 100).toFixed(2) + '%' : '0%',
            details: this.testResults
        };
        
        // Save to file
        const reportPath = path.join(__dirname, `test-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`\n📊 Test Report Generated: ${reportPath}`);
        console.log(`📈 Success Rate: ${report.successRate}`);
        console.log(`✅ Passed: ${report.summary.passed}`);
        console.log(`❌ Failed: ${report.summary.failed}`);
        console.log(`⏭️ Skipped: ${report.summary.skipped}`);
        
        return report;
    }

    /**
     * Clean up and disconnect
     */
    async cleanup() {
        console.log('\n🧹 Cleaning up...');
        await this.client.destroy();
    }

    /**
     * Run comprehensive tests on all known interactions
     */
    async runAllTests() {
        console.log('\n🚀 Starting Comprehensive Interaction Testing...\n');

        // Test all slash commands
        await this.testAllCommands();

        // Test all known buttons
        await this.testAllButtons();

        // Test all known select menus
        await this.testAllSelectMenus();

        // Test all known modals
        await this.testAllModals();

        // Generate final report
        return this.generateReport();
    }

    /**
     * Test all slash commands
     */
    async testAllCommands() {
        console.log('🎯 Testing Slash Commands...');

        const commands = ['17', 'you'];

        for (const commandName of commands) {
            await this.testInteraction('command', commandName);
        }
    }

    /**
     * Test all known buttons
     */
    async testAllButtons() {
        console.log('🔘 Testing Buttons...');

        const buttons = [
            // Feature toggles
            'exp-disable', 'exp-enable',
            'dehoist-disable', 'dehoist-enable',
            'sticky-disable', 'sticky-enable',
            'opener-disable', 'opener-enable',
            'logs-disable', 'logs-enable',
            'items-disable', 'items-enable',

            // EXP system
            'exp-text-disable', 'exp-voice-disable',
            'exp-global-disable',

            // Dehoist system
            'dehoist-chars', 'dehoist-scan',

            // Items system
            'items-create', 'items-edit', 'items-delete',
            'items-preview', 'items-test-drop',

            // Logs system
            'logs-add-channel', 'logs-remove-channel',

            // Opener system
            'opener-refresh', 'opener-clear'
        ];

        for (const buttonId of buttons) {
            await this.testInteraction('button', buttonId);
        }
    }

    /**
     * Test all known select menus
     */
    async testAllSelectMenus() {
        console.log('📋 Testing Select Menus...');

        const selectMenus = [
            // Feature selection
            { id: 'features-select', values: ['exp'] },
            { id: 'features-select', values: ['dehoist'] },
            { id: 'features-select', values: ['sticky'] },
            { id: 'features-select', values: ['opener'] },
            { id: 'features-select', values: ['logs'] },
            { id: 'features-select', values: ['items'] },

            // EXP settings
            { id: 'exp-text-select', values: ['enable'] },
            { id: 'exp-voice-select', values: ['enable'] },
            { id: 'exp-global-select', values: ['enable'] },

            // Dehoist settings
            { id: 'dehoist-nick-select', values: ['enable'] },
            { id: 'dehoist-role-select', values: ['role1', 'role2'] },

            // Sticky settings
            { id: 'sticky-nick-select', values: ['enable'] },
            { id: 'sticky-role-select', values: ['role1'] },

            // Opener settings
            { id: 'opener-thread-select', values: ['thread1'] },

            // Logs settings
            { id: 'logs-channel-select', values: ['channel1'] },
            { id: 'logs-events-select', values: ['messageDelete'] },

            // Items settings
            { id: 'items-type-select', values: ['AQUATIC'] },
            { id: 'items-rarity-select', values: ['COMMON'] },
            { id: 'items-edit-select', values: ['item1'] }
        ];

        for (const menu of selectMenus) {
            await this.testInteraction('selectMenu', menu.id, { values: menu.values });
        }
    }

    /**
     * Test all known modals
     */
    async testAllModals() {
        console.log('📝 Testing Modals...');

        const modals = [
            // Items modals
            {
                id: 'items-create-modal',
                fieldValues: {
                    'item-name': 'Test Fish',
                    'item-description': 'A test fish for testing'
                }
            },
            {
                id: 'items-edit-modal',
                fieldValues: {
                    'item-name': 'Updated Fish',
                    'item-description': 'An updated test fish'
                }
            },

            // Dehoist modals
            {
                id: 'dehoist-chars-modal',
                fieldValues: {
                    'blocked-chars': '!@#$%'
                }
            },

            // Other potential modals
            {
                id: 'logs-channel-modal',
                fieldValues: {
                    'channel-name': 'test-logs'
                }
            }
        ];

        for (const modal of modals) {
            await this.testInteraction('modal', modal.id, { fieldValues: modal.fieldValues });
        }
    }
}

module.exports = InteractionTester;
