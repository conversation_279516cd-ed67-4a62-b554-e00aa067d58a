/**
 * Comprehensive Automated Test Suite for Discord Bot Interaction Validation
 * 
 * Tests interaction timeout errors, command invalidation issues, and double-processing problems
 * without requiring manual user input.
 * 
 * Coverage:
 * - Both slash commands (/17 and /you) with full execution flow
 * - All component interactions (buttons, select menus) within each command session
 * - Cross-command invalidation scenarios
 * - Rapid interaction sequences to detect race conditions
 * - Error recovery scenarios
 */

const { Client, GatewayIntentBits, SlashCommandBuilder, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const { mongoClient } = require('../mongo/client.js');
const { clearUserCommands } = require('../utils/commandInvalidation.js');
require('dotenv').config();

// Set up global permission function for test environment (from index.js)
const permissions = {
    dehoist: PermissionFlagsBits.Administrator,
    logs: PermissionFlagsBits.Administrator,
    sticky: PermissionFlagsBits.Administrator,
    exp: PermissionFlagsBits.Administrator,
    opener: PermissionFlagsBits.Administrator,
    items: PermissionFlagsBits.Administrator
};

function hasFeaturePermission(member, feature) {
    if (!member || !permissions[feature]) return false;

    // Server owners always have full access to all bot features
    const isServerOwner = member.guild.ownerId === member.user.id;
    if (isServerOwner) return true;

    return member.permissions.has(permissions[feature]);
}

// Export for use in other files (same as index.js)
global.hasFeaturePermission = hasFeaturePermission;

class InteractionTimeoutTester {
    constructor() {
        this.client = null;
        this.testResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            errors: [],
            timingData: [],
            invalidationTests: [],
            doubleProcessingTests: []
        };
        this.testStartTime = Date.now();
        this.activeInteractions = new Map(); // Track active test interactions
    }

    /**
     * Initialize the test client and connect to Discord
     */
    async initialize() {
        console.log('🚀 Initializing Comprehensive Interaction Timeout Test Suite...');
        
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ]
        });

        // Set up event listeners for monitoring
        this.setupEventListeners();

        // Login as the bot
        await this.client.login(process.env.TOKEN);
        
        // Wait for ready event
        await new Promise(resolve => {
            this.client.once('ready', () => {
                console.log(`✅ Test client logged in as ${this.client.user.tag}`);
                resolve();
            });
        });

        // Wait for MongoDB connection
        await this.waitForDatabase();
    }

    /**
     * Set up event listeners to monitor interaction processing
     */
    setupEventListeners() {
        // Monitor interaction creation events
        this.client.on('interactionCreate', (interaction) => {
            const interactionId = interaction.id;
            const timestamp = Date.now();
            
            // Track interaction timing
            this.activeInteractions.set(interactionId, {
                startTime: timestamp,
                type: interaction.type,
                customId: interaction.customId || interaction.commandName,
                userId: interaction.user.id
            });

            // Log interaction details
            console.log(`[TEST-MONITOR] Interaction created: ${interaction.customId || interaction.commandName} (${interactionId})`);
        });

        // Monitor for Discord API errors
        this.client.on('error', (error) => {
            console.error('[TEST-MONITOR] Discord client error:', error);
            this.recordError('CLIENT_ERROR', error.message, { error: error.stack });
        });

        // Monitor for warnings (like interaction timeouts)
        this.client.on('warn', (warning) => {
            console.warn('[TEST-MONITOR] Discord client warning:', warning);
            if (warning.includes('interaction') || warning.includes('timeout')) {
                this.recordError('CLIENT_WARNING', warning, { type: 'timeout_warning' });
            }
        });
    }

    /**
     * Wait for MongoDB connection to be ready
     */
    async waitForDatabase() {
        console.log('⏳ Waiting for MongoDB connection...');
        let attempts = 0;
        const maxAttempts = 30;

        while (attempts < maxAttempts) {
            try {
                await mongoClient.db('test').admin().ping();
                console.log('✅ MongoDB connection confirmed');
                return;
            } catch (error) {
                attempts++;
                console.log(`⏳ MongoDB connection attempt ${attempts}/${maxAttempts}...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        throw new Error('Failed to connect to MongoDB after 30 attempts');
    }

    /**
     * Record test error with detailed context
     */
    recordError(type, message, context = {}) {
        const error = {
            type,
            message,
            context,
            timestamp: Date.now(),
            testPhase: this.currentTestPhase || 'unknown'
        };
        
        this.testResults.errors.push(error);
        this.testResults.failedTests++;
        
        console.error(`❌ [${type}] ${message}`, context);
    }

    /**
     * Record successful test
     */
    recordSuccess(testName, timing = null) {
        this.testResults.passedTests++;
        
        if (timing) {
            this.testResults.timingData.push({
                test: testName,
                duration: timing,
                timestamp: Date.now()
            });
        }
        
        console.log(`✅ ${testName} ${timing ? `(${timing}ms)` : ''}`);
    }

    /**
     * Create a mock interaction object for testing
     */
    createMockInteraction(type, options = {}) {
        const mockInteraction = {
            id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: type,
            user: {
                id: process.env.OWNER || '97757532835033088',
                username: 'TestUser',
                tag: 'TestUser#0000',
                displayAvatarURL: function(options = {}) {
                    return `https://cdn.discordapp.com/avatars/${this.id}/test_avatar.${options.extension || 'png'}?size=${options.size || 256}`;
                }
            },
            guild: {
                id: '123456789012345678', // Test guild ID
                ownerId: process.env.OWNER || '97757532835033088' // Make test user the server owner
            },
            member: {
                id: process.env.OWNER || '97757532835033088',
                user: {
                    id: process.env.OWNER || '97757532835033088'
                },
                permissions: { has: () => true },
                guild: null // Will be set after guild is created
            },
            client: this.client,
            replied: false,
            deferred: false,
            customId: options.customId || 'test-interaction',
            commandName: options.commandName || null,
            values: options.values || [],
            
            // Mock interaction methods
            deferReply: async (options = {}) => {
                const startTime = Date.now();
                try {
                    // Simulate Discord API call delay
                    await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
                    
                    if (mockInteraction.replied || mockInteraction.deferred) {
                        throw new Error('Interaction has already been acknowledged.');
                    }
                    
                    mockInteraction.deferred = true;
                    const duration = Date.now() - startTime;
                    
                    console.log(`[MOCK] deferReply completed in ${duration}ms for ${mockInteraction.id}`);
                    return { id: mockInteraction.id };
                } catch (error) {
                    console.error(`[MOCK] deferReply failed for ${mockInteraction.id}:`, error.message);
                    throw error;
                }
            },
            
            deferUpdate: async () => {
                const startTime = Date.now();
                try {
                    await new Promise(resolve => setTimeout(resolve, 30 + Math.random() * 70));
                    
                    if (mockInteraction.replied || mockInteraction.deferred) {
                        throw new Error('Interaction has already been acknowledged.');
                    }
                    
                    mockInteraction.deferred = true;
                    const duration = Date.now() - startTime;
                    
                    console.log(`[MOCK] deferUpdate completed in ${duration}ms for ${mockInteraction.id}`);
                    return { id: mockInteraction.id };
                } catch (error) {
                    console.error(`[MOCK] deferUpdate failed for ${mockInteraction.id}:`, error.message);
                    throw error;
                }
            },
            
            reply: async (options) => {
                const startTime = Date.now();
                try {
                    await new Promise(resolve => setTimeout(resolve, 40 + Math.random() * 80));
                    
                    if (mockInteraction.replied || mockInteraction.deferred) {
                        throw new Error('Interaction has already been acknowledged.');
                    }
                    
                    mockInteraction.replied = true;
                    const duration = Date.now() - startTime;
                    
                    console.log(`[MOCK] reply completed in ${duration}ms for ${mockInteraction.id}`);
                    return { id: mockInteraction.id };
                } catch (error) {
                    console.error(`[MOCK] reply failed for ${mockInteraction.id}:`, error.message);
                    throw error;
                }
            },
            
            editReply: async (options) => {
                const startTime = Date.now();
                try {
                    await new Promise(resolve => setTimeout(resolve, 35 + Math.random() * 65));
                    
                    if (!mockInteraction.replied && !mockInteraction.deferred) {
                        throw new Error('The reply to this interaction has not been sent or deferred.');
                    }
                    
                    const duration = Date.now() - startTime;
                    console.log(`[MOCK] editReply completed in ${duration}ms for ${mockInteraction.id}`);
                    return { id: mockInteraction.id };
                } catch (error) {
                    console.error(`[MOCK] editReply failed for ${mockInteraction.id}:`, error.message);
                    throw error;
                }
            },
            
            update: async (options) => {
                const startTime = Date.now();
                try {
                    await new Promise(resolve => setTimeout(resolve, 45 + Math.random() * 85));
                    
                    if (mockInteraction.replied || mockInteraction.deferred) {
                        throw new Error('Interaction has already been acknowledged.');
                    }
                    
                    mockInteraction.replied = true;
                    const duration = Date.now() - startTime;
                    
                    console.log(`[MOCK] update completed in ${duration}ms for ${mockInteraction.id}`);
                    return { id: mockInteraction.id };
                } catch (error) {
                    console.error(`[MOCK] update failed for ${mockInteraction.id}:`, error.message);
                    throw error;
                }
            },
            
            // Helper methods for testing
            isChatInputCommand: () => type === 1,
            isButton: () => type === 3,
            isStringSelectMenu: () => type === 3,
            isChannelSelectMenu: () => type === 8,
            isContextMenuCommand: () => type === 2
        };

        // Set the guild reference in member object for permission checking
        mockInteraction.member.guild = mockInteraction.guild;

        return mockInteraction;
    }

    /**
     * Test slash command execution with timing validation
     */
    async testSlashCommand(commandName) {
        this.currentTestPhase = `SLASH_COMMAND_${commandName.toUpperCase()}`;
        console.log(`\n🧪 Testing /${commandName} slash command execution...`);

        const startTime = Date.now();
        let testPassed = false;

        try {
            // Create mock slash command interaction
            const interaction = this.createMockInteraction(1, { commandName });

            // Load the command module
            const commandModule = require(`../commands/utility/${commandName}.js`);

            // Test command execution with timeout protection
            const executionPromise = commandModule.execute(interaction);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Command execution timeout (>3000ms)')), 3000)
            );

            await Promise.race([executionPromise, timeoutPromise]);

            const duration = Date.now() - startTime;

            // Validate interaction was acknowledged within Discord's 3-second limit
            if (duration > 3000) {
                throw new Error(`Command execution took ${duration}ms, exceeding Discord's 3-second limit`);
            }

            // Validate interaction state
            if (!interaction.replied && !interaction.deferred) {
                throw new Error('Interaction was not acknowledged (neither replied nor deferred)');
            }

            this.recordSuccess(`/${commandName} slash command execution`, duration);
            testPassed = true;

        } catch (error) {
            this.recordError('SLASH_COMMAND_EXECUTION', `/${commandName} failed: ${error.message}`, {
                commandName,
                duration: Date.now() - startTime,
                stack: error.stack
            });
        }

        this.testResults.totalTests++;
        return testPassed;
    }

    /**
     * Test component interactions within a command session
     */
    async testComponentInteractions(commandName, componentTests) {
        this.currentTestPhase = `COMPONENT_INTERACTIONS_${commandName.toUpperCase()}`;
        console.log(`\n🧪 Testing component interactions for /${commandName}...`);

        let allTestsPassed = true;

        for (const componentTest of componentTests) {
            const startTime = Date.now();

            try {
                console.log(`  Testing ${componentTest.type}: ${componentTest.customId}`);

                // Create mock component interaction
                const interaction = this.createMockInteraction(3, {
                    customId: componentTest.customId,
                    values: componentTest.values || []
                });

                // Simulate that this interaction is part of an active command session
                // by ensuring the command is registered first
                const { registerCommand } = require('../utils/commandInvalidation.js');
                await registerCommand(interaction.user.id, commandName, 'test_session_id', this.client);

                // Load the command module and test the appropriate method
                const commandModule = require(`../commands/utility/${commandName}.js`);

                let methodToTest;
                if (componentTest.type === 'select') {
                    methodToTest = commandModule.select || commandModule.handleSelectMenu;
                } else if (componentTest.type === 'button') {
                    methodToTest = commandModule.buttons || commandModule.handleButton;
                }

                if (!methodToTest) {
                    throw new Error(`No ${componentTest.type} handler found in ${commandName}.js`);
                }

                // Test component interaction with timeout protection
                const interactionPromise = methodToTest.call(commandModule, interaction, componentTest.args || []);
                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Component interaction timeout (>3000ms)')), 3000)
                );

                await Promise.race([interactionPromise, timeoutPromise]);

                const duration = Date.now() - startTime;

                // Validate timing
                if (duration > 3000) {
                    throw new Error(`Component interaction took ${duration}ms, exceeding Discord's 3-second limit`);
                }

                this.recordSuccess(`${componentTest.type}: ${componentTest.customId}`, duration);

            } catch (error) {
                this.recordError('COMPONENT_INTERACTION', `${componentTest.type} ${componentTest.customId} failed: ${error.message}`, {
                    commandName,
                    componentTest,
                    duration: Date.now() - startTime,
                    stack: error.stack
                });
                allTestsPassed = false;
            }

            this.testResults.totalTests++;
        }

        return allTestsPassed;
    }

    /**
     * Test command invalidation scenarios
     */
    async testCommandInvalidation() {
        this.currentTestPhase = 'COMMAND_INVALIDATION';
        console.log(`\n🧪 Testing command invalidation scenarios...`);

        const { registerCommand, isInteractionInvalidated } = require('../utils/commandInvalidation.js');
        const testUserId = process.env.OWNER || '97757532835033088';
        let allTestsPassed = true;

        try {
            // Test 1: Register /17 command
            console.log('  Test 1: Registering /17 command...');
            await registerCommand(testUserId, '17', 'session_1', this.client);

            // Test 2: Component interaction should NOT be invalidated (same command session)
            console.log('  Test 2: Testing component interaction within same session...');
            const componentInteraction = this.createMockInteraction(3, { customId: '17-select' });
            componentInteraction.user.id = testUserId;

            const isInvalidated1 = await isInteractionInvalidated(componentInteraction);
            if (isInvalidated1) {
                throw new Error('Component interaction incorrectly invalidated within same command session');
            }
            this.recordSuccess('Component interaction allowed within same session');

            // Test 3: Register /you command (should invalidate /17)
            console.log('  Test 3: Registering /you command (should invalidate /17)...');
            await registerCommand(testUserId, 'you', 'session_2', this.client);

            // Test 4: /17 component interaction should now be invalidated
            console.log('  Test 4: Testing /17 component interaction after /you registration...');
            const invalidatedInteraction = this.createMockInteraction(3, { customId: '17-select' });
            invalidatedInteraction.user.id = testUserId;

            const isInvalidated2 = await isInteractionInvalidated(invalidatedInteraction);
            if (!isInvalidated2) {
                throw new Error('/17 component interaction should be invalidated after /you registration');
            }
            this.recordSuccess('/17 interaction correctly invalidated after /you registration');

            // Test 5: /you component interaction should NOT be invalidated
            console.log('  Test 5: Testing /you component interaction (should be active)...');
            const activeInteraction = this.createMockInteraction(3, { customId: 'you-select' });
            activeInteraction.user.id = testUserId;

            const isInvalidated3 = await isInteractionInvalidated(activeInteraction);
            if (isInvalidated3) {
                throw new Error('/you component interaction incorrectly invalidated');
            }
            this.recordSuccess('/you interaction correctly allowed (active session)');

            // Test 6: Slash commands should NEVER be invalidated
            console.log('  Test 6: Testing slash command invalidation (should never be blocked)...');
            const slashInteraction = this.createMockInteraction(1, { commandName: '17' });
            slashInteraction.user.id = testUserId;

            const isInvalidated4 = await isInteractionInvalidated(slashInteraction);
            if (isInvalidated4) {
                throw new Error('Slash command incorrectly blocked by invalidation system');
            }
            this.recordSuccess('Slash command correctly never blocked by invalidation');

        } catch (error) {
            this.recordError('COMMAND_INVALIDATION', `Invalidation test failed: ${error.message}`, {
                stack: error.stack
            });
            allTestsPassed = false;
        }

        this.testResults.totalTests += 6;
        this.testResults.invalidationTests.push({
            timestamp: Date.now(),
            passed: allTestsPassed,
            details: 'Cross-command invalidation and same-session component interaction tests'
        });

        return allTestsPassed;
    }

    /**
     * Test rapid interaction sequences to detect race conditions
     */
    async testRapidInteractions() {
        this.currentTestPhase = 'RAPID_INTERACTIONS';
        console.log(`\n🧪 Testing rapid interaction sequences...`);

        let allTestsPassed = true;
        const testUserId = process.env.OWNER || '97757532835033088';

        try {
            // Register initial command
            const { registerCommand } = require('../utils/commandInvalidation.js');
            await registerCommand(testUserId, '17', 'rapid_test_session', this.client);

            // Create multiple rapid interactions
            const rapidInteractions = [];
            for (let i = 0; i < 5; i++) {
                const interaction = this.createMockInteraction(3, {
                    customId: `17-select-rapid-${i}`
                });
                interaction.user.id = testUserId;
                rapidInteractions.push(interaction);
            }

            // Execute all interactions simultaneously
            console.log('  Executing 5 rapid component interactions simultaneously...');
            const startTime = Date.now();

            const results = await Promise.allSettled(
                rapidInteractions.map(async (interaction, index) => {
                    // Simulate component interaction processing
                    const { isInteractionInvalidated } = require('../utils/commandInvalidation.js');
                    const isInvalidated = await isInteractionInvalidated(interaction);

                    if (isInvalidated) {
                        throw new Error(`Rapid interaction ${index} incorrectly invalidated`);
                    }

                    // Simulate interaction acknowledgment
                    await interaction.deferUpdate();
                    return `Rapid interaction ${index} completed`;
                })
            );

            const duration = Date.now() - startTime;

            // Check results
            const failures = results.filter(result => result.status === 'rejected');
            if (failures.length > 0) {
                throw new Error(`${failures.length}/5 rapid interactions failed: ${failures.map(f => f.reason.message).join(', ')}`);
            }

            this.recordSuccess(`Rapid interactions (5 simultaneous)`, duration);

        } catch (error) {
            this.recordError('RAPID_INTERACTIONS', `Rapid interaction test failed: ${error.message}`, {
                stack: error.stack
            });
            allTestsPassed = false;
        }

        this.testResults.totalTests++;
        return allTestsPassed;
    }

    /**
     * Test error recovery scenarios
     */
    async testErrorRecovery() {
        this.currentTestPhase = 'ERROR_RECOVERY';
        console.log(`\n🧪 Testing error recovery scenarios...`);

        let allTestsPassed = true;

        try {
            // Test 1: Database timeout during invalidation check
            console.log('  Test 1: Database timeout during invalidation check...');

            // Create interaction that will trigger database timeout
            const timeoutInteraction = this.createMockInteraction(3, { customId: '17-timeout-test' });

            // Temporarily break database connection to simulate timeout
            const originalFind = require('mongodb').Collection.prototype.findOne;
            require('mongodb').Collection.prototype.findOne = function() {
                return new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Database timeout')), 2000);
                });
            };

            const { isInteractionInvalidated } = require('../utils/commandInvalidation.js');
            const startTime = Date.now();

            // This should not throw an error - should gracefully handle timeout
            const isInvalidated = await isInteractionInvalidated(timeoutInteraction);
            const duration = Date.now() - startTime;

            // Restore original function
            require('mongodb').Collection.prototype.findOne = originalFind;

            // Should allow interaction despite database error
            if (isInvalidated) {
                throw new Error('Interaction incorrectly blocked due to database timeout');
            }

            this.recordSuccess('Database timeout gracefully handled', duration);

            // Test 2: Double acknowledgment protection
            console.log('  Test 2: Double acknowledgment protection...');

            const doubleAckInteraction = this.createMockInteraction(3, { customId: '17-double-ack' });

            // First acknowledgment
            await doubleAckInteraction.deferUpdate();

            // Second acknowledgment should fail gracefully
            try {
                await doubleAckInteraction.deferUpdate();
                throw new Error('Double acknowledgment should have failed');
            } catch (error) {
                if (error.message.includes('already been acknowledged')) {
                    this.recordSuccess('Double acknowledgment correctly prevented');
                } else {
                    throw error;
                }
            }

        } catch (error) {
            this.recordError('ERROR_RECOVERY', `Error recovery test failed: ${error.message}`, {
                stack: error.stack
            });
            allTestsPassed = false;
        }

        this.testResults.totalTests += 2;
        return allTestsPassed;
    }

    /**
     * Run all comprehensive tests
     */
    async runAllTests() {
        console.log('🚀 Starting Comprehensive Discord Bot Interaction Timeout Test Suite');
        console.log('=' .repeat(80));

        const overallStartTime = Date.now();
        let allTestsPassed = true;

        try {
            // Initialize test environment
            await this.initialize();

            // Test 1: Slash Command Execution Tests
            console.log('\n📋 PHASE 1: SLASH COMMAND EXECUTION TESTS');
            console.log('-'.repeat(50));

            const slash17Passed = await this.testSlashCommand('17');
            const slashYouPassed = await this.testSlashCommand('you');

            if (!slash17Passed || !slashYouPassed) {
                allTestsPassed = false;
            }

            // Test 2: Component Interaction Tests
            console.log('\n📋 PHASE 2: COMPONENT INTERACTION TESTS');
            console.log('-'.repeat(50));

            // Define component tests for /17 command
            const command17ComponentTests = [
                { type: 'select', customId: '17-select', values: ['owner'], args: ['owner'] },
                { type: 'select', customId: '17-select', values: ['items'], args: ['items'] },
                { type: 'select', customId: '17-select', values: ['changelog'], args: ['changelog'] },
                { type: 'button', customId: 'items-back-to-items', args: [] },
                { type: 'button', customId: 'items-drop-channel-back', args: [] }
            ];

            // Define component tests for /you command
            const commandYouComponentTests = [
                { type: 'select', customId: 'you-select', values: ['profile'], args: ['profile'] },
                { type: 'select', customId: 'you-select', values: ['inventory'], args: ['inventory'] },
                { type: 'select', customId: 'you-select', values: ['daily'], args: ['daily'] },
                { type: 'button', customId: 'you-settings-toggle', args: [] }
            ];

            const component17Passed = await this.testComponentInteractions('17', command17ComponentTests);
            const componentYouPassed = await this.testComponentInteractions('you', commandYouComponentTests);

            if (!component17Passed || !componentYouPassed) {
                allTestsPassed = false;
            }

            // Test 3: Command Invalidation Tests
            console.log('\n📋 PHASE 3: COMMAND INVALIDATION TESTS');
            console.log('-'.repeat(50));

            const invalidationPassed = await this.testCommandInvalidation();
            if (!invalidationPassed) {
                allTestsPassed = false;
            }

            // Test 4: Rapid Interaction Tests
            console.log('\n📋 PHASE 4: RAPID INTERACTION TESTS');
            console.log('-'.repeat(50));

            const rapidPassed = await this.testRapidInteractions();
            if (!rapidPassed) {
                allTestsPassed = false;
            }

            // Test 5: Error Recovery Tests
            console.log('\n📋 PHASE 5: ERROR RECOVERY TESTS');
            console.log('-'.repeat(50));

            const errorRecoveryPassed = await this.testErrorRecovery();
            if (!errorRecoveryPassed) {
                allTestsPassed = false;
            }

        } catch (error) {
            console.error('❌ Critical test suite error:', error);
            this.recordError('TEST_SUITE_CRITICAL', `Test suite failed: ${error.message}`, {
                stack: error.stack
            });
            allTestsPassed = false;
        }

        // Generate comprehensive test report
        const overallDuration = Date.now() - overallStartTime;
        await this.generateTestReport(overallDuration, allTestsPassed);

        // Cleanup
        await this.cleanup();

        return allTestsPassed;
    }

    /**
     * Generate comprehensive test report
     */
    async generateTestReport(duration, overallSuccess) {
        console.log('\n' + '='.repeat(80));
        console.log('📊 COMPREHENSIVE TEST REPORT');
        console.log('='.repeat(80));

        const successRate = this.testResults.totalTests > 0
            ? ((this.testResults.passedTests / this.testResults.totalTests) * 100).toFixed(2)
            : 0;

        console.log(`\n📈 OVERALL RESULTS:`);
        console.log(`   Total Tests: ${this.testResults.totalTests}`);
        console.log(`   Passed: ${this.testResults.passedTests}`);
        console.log(`   Failed: ${this.testResults.failedTests}`);
        console.log(`   Success Rate: ${successRate}%`);
        console.log(`   Total Duration: ${duration}ms`);
        console.log(`   Overall Status: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);

        // Success criteria check
        const meetsSuccessCriteria = successRate >= 95;
        console.log(`\n🎯 SUCCESS CRITERIA (>95% success rate): ${meetsSuccessCriteria ? '✅ MET' : '❌ NOT MET'}`);

        // Timing analysis
        if (this.testResults.timingData.length > 0) {
            const avgTiming = this.testResults.timingData.reduce((sum, t) => sum + t.duration, 0) / this.testResults.timingData.length;
            const maxTiming = Math.max(...this.testResults.timingData.map(t => t.duration));
            const minTiming = Math.min(...this.testResults.timingData.map(t => t.duration));

            console.log(`\n⏱️  TIMING ANALYSIS:`);
            console.log(`   Average Response Time: ${avgTiming.toFixed(2)}ms`);
            console.log(`   Fastest Response: ${minTiming}ms`);
            console.log(`   Slowest Response: ${maxTiming}ms`);
            console.log(`   Discord 3s Limit Violations: ${this.testResults.timingData.filter(t => t.duration > 3000).length}`);
        }

        // Error analysis
        if (this.testResults.errors.length > 0) {
            console.log(`\n❌ ERROR ANALYSIS:`);
            const errorTypes = {};
            this.testResults.errors.forEach(error => {
                errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
            });

            Object.entries(errorTypes).forEach(([type, count]) => {
                console.log(`   ${type}: ${count} occurrences`);
            });

            console.log(`\n📋 DETAILED ERRORS:`);
            this.testResults.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. [${error.type}] ${error.message}`);
                if (error.context && Object.keys(error.context).length > 0) {
                    console.log(`      Context: ${JSON.stringify(error.context, null, 2)}`);
                }
            });
        }

        // Command invalidation test results
        if (this.testResults.invalidationTests.length > 0) {
            console.log(`\n🔒 COMMAND INVALIDATION TESTS:`);
            this.testResults.invalidationTests.forEach((test, index) => {
                console.log(`   Test ${index + 1}: ${test.passed ? '✅ PASSED' : '❌ FAILED'} - ${test.details}`);
            });
        }

        // Recommendations
        console.log(`\n💡 RECOMMENDATIONS:`);
        if (successRate < 95) {
            console.log(`   - Success rate (${successRate}%) is below 95% threshold`);
            console.log(`   - Review failed tests and implement fixes`);
        }

        if (this.testResults.timingData.some(t => t.duration > 2000)) {
            console.log(`   - Some interactions are taking >2s, consider optimization`);
        }

        if (this.testResults.errors.some(e => e.type.includes('INVALIDATION'))) {
            console.log(`   - Command invalidation system needs adjustment`);
        }

        if (this.testResults.errors.some(e => e.message.includes('double') || e.message.includes('already acknowledged'))) {
            console.log(`   - Double-processing or acknowledgment issues detected`);
        }

        console.log('\n' + '='.repeat(80));
    }

    /**
     * Cleanup test environment
     */
    async cleanup() {
        console.log('\n🧹 Cleaning up test environment...');

        try {
            // Clear any test data from invalidation system
            const { clearUserCommands } = require('../utils/commandInvalidation.js');
            const testUserId = process.env.OWNER || '97757532835033088';
            await clearUserCommands(testUserId);

            // Disconnect Discord client
            if (this.client) {
                this.client.destroy();
                console.log('✅ Discord client disconnected');
            }

            console.log('✅ Test environment cleanup completed');

        } catch (error) {
            console.error('❌ Error during cleanup:', error);
        }
    }
}

// Test runner
async function runComprehensiveTests() {
    const tester = new InteractionTimeoutTester();

    try {
        const success = await tester.runAllTests();

        if (success) {
            console.log('\n🎉 All tests completed successfully!');
            process.exit(0);
        } else {
            console.log('\n💥 Some tests failed. Review the report above.');
            process.exit(1);
        }

    } catch (error) {
        console.error('\n💥 Critical test failure:', error);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runComprehensiveTests();
}

module.exports = { InteractionTimeoutTester, runComprehensiveTests };
