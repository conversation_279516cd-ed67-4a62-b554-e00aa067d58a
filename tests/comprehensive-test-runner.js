#!/usr/bin/env node

/**
 * Comprehensive Test Runner
 * Automatically discovers and tests ALL interactions in the codebase
 */

const InteractionTester = require('./automated-interaction-tester.js');
const InteractionScanner = require('./interaction-scanner.js');
const { connect } = require('../mongo/client.js');
const fs = require('fs');
const path = require('path');

class ComprehensiveTestRunner {
    constructor() {
        this.scanner = new InteractionScanner();
        this.tester = null;
        this.discoveredInteractions = null;
    }

    /**
     * Run the complete test suite
     */
    async run() {
        console.log('🚀 Comprehensive Discord Bot Testing Suite');
        console.log('==========================================\n');

        try {
            // Step 1: Connect to database
            console.log('🔌 Connecting to database...');
            await connect();
            console.log('✅ Database connected\n');

            // Step 2: Scan codebase for interactions
            console.log('🔍 Phase 1: Discovering interactions...');
            await this.discoverInteractions();

            // Step 3: Initialize tester
            console.log('\n🤖 Phase 2: Initializing test environment...');
            this.tester = new InteractionTester();
            await this.tester.initialize();

            // Step 4: Run tests
            console.log('\n🧪 Phase 3: Running automated tests...');
            await this.runDiscoveredTests();

            // Step 5: Generate comprehensive report
            console.log('\n📊 Phase 4: Generating reports...');
            const report = await this.generateComprehensiveReport();

            // Step 6: Display results
            this.displayResults(report);

            return report;

        } catch (error) {
            console.error('\n💥 Test suite failed:', error);
            throw error;
        } finally {
            if (this.tester) {
                await this.tester.cleanup();
            }
        }
    }

    /**
     * Discover all interactions in the codebase
     */
    async discoverInteractions() {
        await this.scanner.scanCodebase();
        this.scanner.printResults();
        
        this.discoveredInteractions = this.scanner.generateTestData();
        
        const totalFound = Object.values(this.discoveredInteractions)
            .reduce((sum, arr) => sum + arr.length, 0);
        
        console.log(`\n✨ Total interactions discovered: ${totalFound}`);
    }

    /**
     * Run tests on all discovered interactions
     */
    async runDiscoveredTests() {
        const { buttons, selectMenus, modals, commands } = this.discoveredInteractions;

        // Test commands
        console.log('\n⚡ Testing Commands...');
        for (const command of commands) {
            await this.tester.testInteraction('command', command.id, command.testData);
        }

        // Test buttons
        console.log('\n🔘 Testing Buttons...');
        for (const button of buttons) {
            await this.tester.testInteraction('button', button.id, button.testData);
        }

        // Test select menus
        console.log('\n📋 Testing Select Menus...');
        for (const menu of selectMenus) {
            await this.tester.testInteraction('selectMenu', menu.id, menu.testData);
        }

        // Test modals
        console.log('\n📝 Testing Modals...');
        for (const modal of modals) {
            await this.tester.testInteraction('modal', modal.id, modal.testData);
        }
    }

    /**
     * Generate comprehensive test report
     */
    async generateComprehensiveReport() {
        const baseReport = this.tester.generateReport();
        
        // Add discovery information
        const comprehensiveReport = {
            ...baseReport,
            discovery: {
                totalDiscovered: Object.values(this.discoveredInteractions)
                    .reduce((sum, arr) => sum + arr.length, 0),
                breakdown: {
                    commands: this.discoveredInteractions.commands.length,
                    buttons: this.discoveredInteractions.buttons.length,
                    selectMenus: this.discoveredInteractions.selectMenus.length,
                    modals: this.discoveredInteractions.modals.length
                },
                discoveredInteractions: this.discoveredInteractions
            },
            coverage: this.calculateCoverage()
        };

        // Save comprehensive report
        const reportPath = path.join(__dirname, `comprehensive-report-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(comprehensiveReport, null, 2));
        
        console.log(`📄 Comprehensive report saved: ${reportPath}`);
        
        return comprehensiveReport;
    }

    /**
     * Calculate test coverage
     */
    calculateCoverage() {
        const discovered = Object.values(this.discoveredInteractions)
            .reduce((sum, arr) => sum + arr.length, 0);
        
        const tested = this.tester.testResults.summary.total;
        
        return {
            discovered: discovered,
            tested: tested,
            coveragePercentage: discovered > 0 ? 
                (tested / discovered * 100).toFixed(2) + '%' : '0%'
        };
    }

    /**
     * Display comprehensive results
     */
    displayResults(report) {
        console.log('\n' + '='.repeat(60));
        console.log('🎯 COMPREHENSIVE TEST RESULTS');
        console.log('='.repeat(60));
        
        console.log('\n📊 Discovery Summary:');
        console.log(`🔍 Total Interactions Found: ${report.discovery.totalDiscovered}`);
        console.log(`⚡ Commands: ${report.discovery.breakdown.commands}`);
        console.log(`🔘 Buttons: ${report.discovery.breakdown.buttons}`);
        console.log(`📋 Select Menus: ${report.discovery.breakdown.selectMenus}`);
        console.log(`📝 Modals: ${report.discovery.breakdown.modals}`);
        
        console.log('\n🧪 Testing Summary:');
        console.log(`📈 Coverage: ${report.coverage.coveragePercentage}`);
        console.log(`✅ Passed: ${report.summary.passed}`);
        console.log(`❌ Failed: ${report.summary.failed}`);
        console.log(`⏭️ Skipped: ${report.summary.skipped}`);
        console.log(`🎯 Success Rate: ${report.successRate}`);

        if (report.details.errors.length > 0) {
            console.log('\n🚨 CRITICAL ISSUES FOUND:');
            console.log('-'.repeat(40));
            report.details.errors.forEach((error, index) => {
                console.log(`${index + 1}. [${error.type.toUpperCase()}] "${error.customId}"`);
                console.log(`   Error: ${error.error}`);
                console.log('');
            });
        }

        // Recommendations
        console.log('\n💡 RECOMMENDATIONS:');
        console.log('-'.repeat(30));
        
        if (report.summary.failed > 0) {
            console.log('🔧 Fix the failed interactions listed above');
        }
        
        if (parseFloat(report.coverage.coveragePercentage) < 100) {
            console.log('📈 Some interactions may not have been discovered - check manually');
        }
        
        if (report.summary.failed === 0) {
            console.log('🎉 All tests passed! Your bot interactions are working correctly.');
        }
        
        console.log('\n📋 Next Steps:');
        console.log('1. Review any failed tests and fix the underlying issues');
        console.log('2. Run the test suite again after making fixes');
        console.log('3. Consider adding this to your CI/CD pipeline');
        console.log('4. Run tests before deploying new features');
    }
}

// Main execution
async function main() {
    const runner = new ComprehensiveTestRunner();
    
    try {
        const report = await runner.run();
        
        // Exit with appropriate code
        const exitCode = report.summary.failed > 0 ? 1 : 0;
        process.exit(exitCode);
        
    } catch (error) {
        console.error('\n💥 Test runner crashed:', error);
        process.exit(1);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n⏹️ Test interrupted by user');
    process.exit(1);
});

process.on('SIGTERM', () => {
    console.log('\n⏹️ Test terminated');
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('💥 Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = ComprehensiveTestRunner;
