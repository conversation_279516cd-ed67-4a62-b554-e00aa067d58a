require('dotenv').config();
const { REST, Routes } = require('discord.js');
const fs = require('node:fs');
const path = require('node:path');

const commands = [];
// Grab all the command folders from the commands directory you created earlier
const foldersPath = path.join(__dirname, 'commands');
const commandFolders = fs.readdirSync(foldersPath);

for (const folder of commandFolders) {
	// Grab all the command files from the commands directory you created earlier
	const commandsPath = path.join(foldersPath, folder);
	const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
	// Grab the SlashCommandBuilder#toJSON() output of each command's data for deployment
	for (const file of commandFiles) {
		const filePath = path.join(commandsPath, file);
		const command = require(filePath);
		if ('data' in command && 'execute' in command) {
			commands.push(command.data.toJSON());
		}
		// Silently skip utility files that don't have slash command properties
	}
}

// Construct and prepare an instance of the REST module
const rest = new REST().setToken(process.env.TOKEN);

// and deploy your commands!
(async () => {
	try {
		console.log(`Started refreshing ${commands.length} application commands.`);

		// First, clear all existing commands
		await rest.put(
			Routes.applicationGuildCommands(process.env.CLIENTID, process.env.GUILDIDTWO),
			{ body: [] }
		);
		console.log('Cleared all existing commands.');

		// Then deploy the new commands
		const data = await rest.put(
			// Flip the comment to deploy to all guilds. GUILDIDTWO just refreshes the commands in the testing server.
             Routes.applicationCommands(process.env.CLIENTID),
			//Routes.applicationGuildCommands(process.env.CLIENTID, process.env.GUILDIDTWO),
			{ body: commands },
		);

		console.log(`Successfully reloaded ${data.length} application commands.`);
	} catch (error) {
		// And of course, make sure you catch and log any errors!
		console.error(error);
	}
})();