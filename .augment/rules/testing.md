---
type: "always_apply"
---

When adding tests in this repository:

1. Load environment variables at the beginning of each test file:
```javascript
   require('dotenv').config();
```

2. Create a real `discord.js` client and log in using the token from `.env`:

```javascript
const { Client, GatewayIntentBits } = require('discord.js');
const client = new Client({ intents: [GatewayIntentBits.Guilds] });
await client.login(process.env.TOKEN);
```

3. Use `process.env.GUILDIDTWO` for the test guild and `process.env.OWNER` for the test user. Connect to MongoDB with the helper in `./mongo/client.js`.

4. Follow the examples in `docs/comprehensive-testing-guide.md` (see lines 34‑59 for the setup code) when building interaction objects and test flows

5. Place new tests in the `tests/` directory and ensure they run via `node test/comprehensive-test-runner.js` as shown in `tests/README.md`

These instructions ensure each test uses the existing `.env` values, logs into Discord, and matches the repository’s comprehensive testing approach.

6. All new code changes must be covered by an interaction test.

- Every slash command, button, select menu, and modal must have a corresponding mock interaction in `tests/comprehensive-test-runner.js` or a test that is discovered via `interaction-scanner.js`.
- If a test is intentionally skipped (e.g., debug-only interactions), it must be added to the skip list in `interaction-scanner.js`.
- Any pull request or change without test coverage will be flagged by Augment.

To verify:
```bash
node tests/comprehensive-test-runner.js
echo $?  # 0 = All tested and passed
```

This guarantees every interaction is testable, reproducible, and safe to deploy.
