---
type: "always_apply"
---

# 📚 Rule: Always Check Official Docs Before Using a Method

Before using any method, property, or class from `discord.js`, **always verify it exists** in the official documentation:

🔗 https://discord.js.org/docs/packages/discord.js/14.21.0

- Do **not** rely on memory, incomplete code, or outdated references.
- If the method doesn’t exist or is deprecated, find the correct alternative using the docs.
- Failure to check leads to runtime errors and broken features.

📌 Especially important when:
- Writing new commands
- Using `Interaction`, `Message`, `Guild`, etc.
- Creating embeds, buttons, or menus