---
type: "always_apply"
---

# 🧩 Rule: Creating New Core Features

When building **new features for the bot**, follow these mandatory steps:

1. **Interaction Setup**
   - Always hook new buttons and select menus into the global `interactionCreate` listener.
   - If omitted, the feature will not function at runtime.

2. **Feature Menu Registration**
   - Add the feature to `featuresmenu.cs`.
   - If the user is on the feature’s own page, do **not** display the feature in its own menu (prevent self-listing).

3. **Owner Visibility Logic**
   - Determine what only the owner can see or do by comparing similar files.
   - Use existing features as examples for splitting behavior between owner and public access.

4. **Feature Structure**
   - Every feature must follow the same format:
     - Title
     - Quote
     - Constants and parameters displayed with `TextDisplayBuilder`
   - Review how `create` and `edit` functions work in other features.
   - Analyze and match existing code patterns **exactly** — this ensures seamless integration and UI/UX consistency.

5. **Select Menu Behavior**
   - When using select menus to edit part of a configuration, reveal a **second select menu** specific to the selected task.
   - Example: When creating a new item, the first select menu chooses an icon category, and the second reveals recently uploaded icons for that category.
   - Ensure information from the first select menu is preserved.
   - Follow examples in existing implementations to maintain consistency.

6. **Update Bot Invite Permissions**
   - If the new feature requires additional permissions (e.g., Manage Roles, View Audit Log), update the bot’s invite link to include the necessary scopes.
   - This ensures users can use the new feature without encountering permission-related errors.
   - Review existing permissions and match only what's required.

7. **Color Usage**
   - Always import and use predefined accent colors from `colors.js`.
   - Do not hardcode hex values inline; this keeps visual styles consistent and centralizes theme updates.

8. **Drop Category Expansion**
   - When adding a new category of item drop location, **always register it with the drop notification system**.
   - Match behavior and structure from existing categories (Level Up, Starfall, Text/Voice EXP).
   - This ensures drops are visible and consistent across all tracking systems.

📌 Do not guess. Look at existing features and replicate structure, logic, and formatting 1:1.
