/**
 * Command Usage Tracking System (Enterprise-Grade Performance Optimized)
 * Tracks command usage statistics with comprehensive caching and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, batch processing, and performance analytics
 */

const { optimizedFindOne, optimizedInsertOne, optimizedFindOneAndUpdate, optimizedBulkWrite } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const commandUsageMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    commandsTracked: 0,
    incrementsProcessed: 0,
    batchOperations: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU cache for maximum performance
const commandUsageCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for command usage data

// Register cache for global cleanup (MANDATORY)
registerCache(commandUsageCache);

/**
 * Get cached command usage data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} commandName - Command name
 * @returns {Promise<Object>} Command usage data
 */
async function getCachedCommandUsage(commandName) {
    const startTime = Date.now();
    const cacheKey = `usage_${commandName}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = commandUsageCache.get(cacheKey);
        if (cached) {
            commandUsageMetrics.cacheHits++;
            if (commandUsageMetrics.verboseLogging) {
                console.log(`[commandUsage] ⚡ Cache hit for ${commandName} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        commandUsageMetrics.cacheMisses++;
        commandUsageMetrics.databaseQueries++;

        const stats = await optimizedFindOne("stats", { type: "command_usage", command: commandName });
        const usageData = stats || { type: "command_usage", command: commandName, count: 0 };

        // Cache the result
        commandUsageCache.set(cacheKey, usageData);

        // Performance tracking
        const duration = Date.now() - startTime;
        commandUsageMetrics.averageQueryTime =
            (commandUsageMetrics.averageQueryTime * (commandUsageMetrics.databaseQueries - 1) + duration) /
            commandUsageMetrics.databaseQueries;

        if (commandUsageMetrics.verboseLogging || duration > 50) {
            console.log(`[commandUsage] ✅ Usage data fetched for ${commandName}: ${duration}ms - cached for future access`);
        }

        return usageData;
    } catch (error) {
        console.error(`[commandUsage] ❌ Error getting cached usage for ${commandName}:`, error);
        return { type: "command_usage", command: commandName, count: 0 };
    }
}

/**
 * Increments the usage count for a given command and returns the updated count (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced error handling with performance monitoring and intelligent caching
 * @param {string} commandName - The name of the command to track.
 * @returns {Promise<number>} - The updated usage count for the command.
 */
async function incrementCommandUsage(commandName) {
    const startTime = Date.now();

    try {
        commandUsageMetrics.databaseQueries++;
        commandUsageMetrics.incrementsProcessed++;
        commandUsageMetrics.commandsTracked++;

        // Check if document exists first to preserve existing data
        let stats = await optimizedFindOne("stats", { type: "command_usage", command: commandName });

        if (!stats) {
            // Create new document if it doesn't exist
            await optimizedInsertOne("stats", {
                type: "command_usage",
                command: commandName,
                count: 1
            });

            // Update cache
            const cacheKey = `usage_${commandName}`;
            const newData = { type: "command_usage", command: commandName, count: 1 };
            commandUsageCache.set(cacheKey, newData);

            // Performance tracking
            const duration = Date.now() - startTime;
            commandUsageMetrics.averageQueryTime =
                (commandUsageMetrics.averageQueryTime * (commandUsageMetrics.databaseQueries - 1) + duration) /
                commandUsageMetrics.databaseQueries;

            if (commandUsageMetrics.verboseLogging || duration > 100) {
                console.log(`[commandUsage] ✅ Created new usage tracking for ${commandName}: 1 (${duration}ms)`);
            }

            return 1;
        } else {
            // OPTIMIZATION: Use findOneAndUpdate for existing documents
            const result = await optimizedFindOneAndUpdate("stats",
                { type: "command_usage", command: commandName },
                { $inc: { count: 1 } },
                {
                    returnDocument: 'after',
                    projection: { count: 1 }
                }
            );

            const newCount = result.value?.count || (stats.count + 1);

            // Update cache
            const cacheKey = `usage_${commandName}`;
            const newData = { ...stats, count: newCount };
            commandUsageCache.set(cacheKey, newData);

            // Performance tracking
            const duration = Date.now() - startTime;
            commandUsageMetrics.averageQueryTime =
                (commandUsageMetrics.averageQueryTime * (commandUsageMetrics.databaseQueries - 1) + duration) /
                commandUsageMetrics.databaseQueries;

            if (commandUsageMetrics.verboseLogging || duration > 100) {
                console.log(`[commandUsage] ✅ Incremented usage for ${commandName}: ${newCount} (${duration}ms)`);
            }

            return newCount;
        }
    } catch (error) {
        console.error(`[commandUsage] ❌ Error incrementing usage for ${commandName}:`, error);

        // Return cached value if available
        const cached = await getCachedCommandUsage(commandName);
        return cached ? cached.count : 0;
    }
}

/**
 * Fetches the current usage count for a given command (without incrementing) (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} commandName - The name of the command to fetch.
 * @returns {Promise<number>} - The current usage count for the command.
 */
async function getCommandUsage(commandName) {
    try {
        const usageData = await getCachedCommandUsage(commandName);
        return usageData.count;
    } catch (error) {
        console.error(`[commandUsage] ❌ Error getting usage for ${commandName}:`, error);
        return 0;
    }
}

/**
 * Batch increment multiple command usages (Enterprise-Grade Optimized)
 * OPTIMIZED: Batch processing for multiple command tracking with performance monitoring
 * @param {Array<string>} commandNames - Array of command names to increment
 * @returns {Promise<Object>} - Object with command names as keys and new counts as values
 */
async function batchIncrementCommandUsage(commandNames) {
    const startTime = Date.now();

    try {
        commandUsageMetrics.batchOperations++;

        if (!commandNames || commandNames.length === 0) {
            return {};
        }

        // OPTIMIZED: Parallel processing for existing command lookups
        const existingStatsResults = await Promise.allSettled(
            commandNames.map(commandName =>
                optimizedFindOne("stats", { type: "command_usage", command: commandName })
            )
        );

        const bulkOps = [];
        const results = {};

        for (let i = 0; i < commandNames.length; i++) {
            const commandName = commandNames[i];
            const existingResult = existingStatsResults[i];
            const existingStats = existingResult.status === 'fulfilled' ? existingResult.value : null;

            if (!existingStats) {
                // Insert new document
                bulkOps.push({
                    insertOne: {
                        document: {
                            type: "command_usage",
                            command: commandName,
                            count: 1
                        }
                    }
                });
                results[commandName] = 1;

                // Update cache
                const cacheKey = `usage_${commandName}`;
                commandUsageCache.set(cacheKey, { type: "command_usage", command: commandName, count: 1 });
            } else {
                // Update existing document
                bulkOps.push({
                    updateOne: {
                        filter: { type: "command_usage", command: commandName },
                        update: { $inc: { count: 1 } }
                    }
                });
                results[commandName] = existingStats.count + 1;

                // Update cache
                const cacheKey = `usage_${commandName}`;
                commandUsageCache.set(cacheKey, { ...existingStats, count: existingStats.count + 1 });
            }
        }

        // Execute batch operations
        if (bulkOps.length > 0) {
            await optimizedBulkWrite("stats", bulkOps);
            commandUsageMetrics.databaseQueries += bulkOps.length;
        }

        commandUsageMetrics.incrementsProcessed += commandNames.length;
        commandUsageMetrics.commandsTracked += commandNames.length;

        // Performance tracking
        const duration = Date.now() - startTime;
        commandUsageMetrics.averageQueryTime =
            (commandUsageMetrics.averageQueryTime * (commandUsageMetrics.databaseQueries - bulkOps.length) + duration) /
            commandUsageMetrics.databaseQueries;

        if (commandUsageMetrics.verboseLogging || duration > 200) {
            console.log(`[commandUsage] ✅ Batch incremented ${commandNames.length} commands in ${duration}ms`);
        }

        return results;

    } catch (error) {
        console.error(`[commandUsage] ❌ Error in batch increment:`, error);
        return {};
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCommandUsageStats() {
    const cacheHitRate = commandUsageMetrics.cacheHits + commandUsageMetrics.cacheMisses > 0 ?
        (commandUsageMetrics.cacheHits / (commandUsageMetrics.cacheHits + commandUsageMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: commandUsageMetrics.cacheHits,
            cacheMisses: commandUsageMetrics.cacheMisses,
            databaseQueries: commandUsageMetrics.databaseQueries,
            averageQueryTime: `${commandUsageMetrics.averageQueryTime.toFixed(2)}ms`,
            commandsTracked: commandUsageMetrics.commandsTracked,
            incrementsProcessed: commandUsageMetrics.incrementsProcessed,
            batchOperations: commandUsageMetrics.batchOperations,
            lastOptimization: new Date(commandUsageMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            commandUsage: commandUsageCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            commandUsage: commandUsageCache.getStats().memoryUsage,
            total: commandUsageCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    commandUsageMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCommandUsageStats();
    console.log(`[commandUsage] 📊 Performance Report:`);
    console.log(`[commandUsage]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[commandUsage]   Database Queries: ${stats.performance.databaseQueries}`);
    console.log(`[commandUsage]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[commandUsage]   Commands Tracked: ${stats.performance.commandsTracked}`);
    console.log(`[commandUsage]   Batch Operations: ${stats.performance.batchOperations}`);
    console.log(`[commandUsage]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[commandUsage]   System Health: ${stats.systemHealth.status}`);

    return stats;
}

/**
 * Clear command usage cache (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearCommandUsageCache() {
    commandUsageCache.clear();
    console.log('[commandUsage] 🗑️ Cleared command usage cache');
}

/**
 * Invalidate specific command cache
 * @param {string} commandName - Command name to invalidate
 */
function invalidateCommandCache(commandName) {
    const cacheKey = `usage_${commandName}`;
    commandUsageCache.delete(cacheKey);

    if (commandUsageMetrics.verboseLogging) {
        console.log(`[commandUsage] 🗑️ Invalidated cache for ${commandName}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, commandUsageMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    incrementCommandUsage,
    getCommandUsage,

    // Enhanced optimization functions
    getCachedCommandUsage,
    batchIncrementCommandUsage,
    getCommandUsageStats,
    performanceCleanupAndOptimization,
    clearCommandUsageCache,
    invalidateCommandCache,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...commandUsageMetrics })
};