/**
 * Owner Join Notification Management Module (Enterprise-Grade Performance Optimized)
 * <PERSON><PERSON> join notification configuration and sending functionality
 * OPTIMIZED: LRU caching, performance monitoring, and enhanced error handling
 */

const { ContainerBuilder, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, AuditLogEvent } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const joinNotificationMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    configurationsFetched: 0,
    configurationsUpdated: 0,
    notificationsSent: 0,
    containersBuilt: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU cache for configuration data
const joinNotificationConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for configuration data

// Register cache for global cleanup (MANDATORY)
registerCache(joinNotificationConfigCache);

// --- JOIN NOTIFICATION FEATURE HELPERS ---
const JOIN_NOTIFICATION_DB_KEY = 'global';
const DEFAULT_JOIN_MESSAGE = 'Thanks for adding me to **{serverName}**! 17 is a unique multi-purpose bot with a one-of-a-kind feature set. Use (click) the </17:1380491293465116703> command to browse and setup the various features there are to configure.';
const BOT_NAME = '17';
const BOT_LINK = 'https://itwascarryingusquietlytowardswhatwillbenotwhatwas.com/';

/**
 * Get join notification config from DB (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @returns {Promise<Object>} Join notification configuration
 */
async function getJoinNotificationConfig() {
    const startTime = Date.now();
    const cacheKey = `config_${JOIN_NOTIFICATION_DB_KEY}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = joinNotificationConfigCache.get(cacheKey);
        if (cached) {
            joinNotificationMetrics.cacheHits++;
            if (joinNotificationMetrics.verboseLogging) {
                console.log(`[owner-join-notification] ⚡ Config cache hit (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        joinNotificationMetrics.cacheMisses++;
        joinNotificationMetrics.databaseQueries++;
        joinNotificationMetrics.configurationsFetched++;

        let doc = await optimizedFindOne('join_notifications', { key: JOIN_NOTIFICATION_DB_KEY });
        if (!doc) {
            doc = { key: JOIN_NOTIFICATION_DB_KEY, enabled: true, message: DEFAULT_JOIN_MESSAGE };
            await optimizedInsertOne('join_notifications', doc);
            joinNotificationMetrics.databaseQueries++;
        }

        // Cache the result
        joinNotificationConfigCache.set(cacheKey, doc);

        // Performance tracking
        const duration = Date.now() - startTime;
        joinNotificationMetrics.averageQueryTime =
            (joinNotificationMetrics.averageQueryTime * (joinNotificationMetrics.databaseQueries - 1) + duration) /
            joinNotificationMetrics.databaseQueries;

        if (joinNotificationMetrics.verboseLogging || duration > 50) {
            console.log(`[owner-join-notification] ✅ Config fetched in ${duration}ms - cached for future access`);
        }

        return doc;
    } catch (error) {
        console.error('[owner-join-notification] ❌ Error getting join notification config:', error);
        // Return default config as fallback
        return { key: JOIN_NOTIFICATION_DB_KEY, enabled: true, message: DEFAULT_JOIN_MESSAGE };
    }
}

/**
 * Set join notification config in DB (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced error handling with performance monitoring and cache invalidation
 * @param {Object} config - Configuration object with enabled and message properties
 * @returns {Promise<boolean>} Success status
 */
async function setJoinNotificationConfig({ enabled, message }) {
    const startTime = Date.now();

    try {
        joinNotificationMetrics.databaseQueries++;
        joinNotificationMetrics.configurationsUpdated++;

        await optimizedUpdateOne('join_notifications',
            { key: JOIN_NOTIFICATION_DB_KEY },
            { $set: { enabled, message } },
            { upsert: true }
        );

        // Invalidate cache
        invalidateJoinNotificationConfigCache();

        // Performance tracking
        const duration = Date.now() - startTime;
        joinNotificationMetrics.averageQueryTime =
            (joinNotificationMetrics.averageQueryTime * (joinNotificationMetrics.databaseQueries - 1) + duration) /
            joinNotificationMetrics.databaseQueries;

        if (joinNotificationMetrics.verboseLogging || duration > 100) {
            console.log(`[owner-join-notification] ✅ Config updated in ${duration}ms`);
        }

        return true;
    } catch (error) {
        console.error('[owner-join-notification] ❌ Error setting join notification config:', error);
        return false;
    }
}

/**
 * Send join notification to the appropriate channel/user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced error handling with performance monitoring and cached configuration
 * @param {Object} guild - Discord guild object
 * @returns {Promise<boolean>} Success status
 */
async function sendJoinNotification(guild) {
    const startTime = Date.now();

    try {
        joinNotificationMetrics.notificationsSent++;

        // OPTIMIZED: Get cached config from DB
        const config = await getJoinNotificationConfig();
        if (!config.enabled) {
            if (joinNotificationMetrics.verboseLogging) {
                console.log('[owner-join-notification] Join notifications disabled, skipping');
            }
            return false;
        }

        // Build the container with the actual guild name
        const container = buildJoinNotificationPreview({
            message: config.message,
            enabled: config.enabled,
            botUser: guild.client.user,
            guildName: guild.name
        });

        // Try to find the inviter first
        const auditLogs = await guild.fetchAuditLogs({ type: AuditLogEvent.BotAdd, limit: 1 }).catch(() => null);
        const inviter = auditLogs?.entries.first()?.executor;

        if (inviter) {
            // Try to DM the inviter
            try {
                const dmChannel = await inviter.createDM();
                await dmChannel.send({
                    flags: MessageFlags.IsComponentsV2,
                    components: [container]
                });

                // Performance tracking
                const duration = Date.now() - startTime;
                if (joinNotificationMetrics.verboseLogging || duration > 200) {
                    console.log(`[owner-join-notification] ✅ Join notification sent via DM to ${inviter.tag} in ${duration}ms`);
                }

                return true;
            } catch (e) {
                if (joinNotificationMetrics.verboseLogging) {
                    console.log('[owner-join-notification] Could not DM inviter, trying channel instead');
                }
            }
        }

        // If we couldn't DM the inviter, try to find the best channel
        const channels = guild.channels.cache
            .filter(c => c.type === 0 && c.permissionsFor(guild.members.me).has('SendMessages'))
            .sort((a, b) => {
                // Prioritize by name keywords
                function getPriority(name) {
                    const n = name.toLowerCase();
                    if (n.includes('bot')) return 1;
                    if (n.includes('spam')) return 2;
                    if (n.includes('welcome')) return 3;
                    if (n.includes('general')) return 4;
                    if (n.includes('announcements')) return 5;
                    if (n.includes('chat')) return 6;
                    return 7;
                }
                const pa = getPriority(a.name);
                const pb = getPriority(b.name);
                if (pa !== pb) return pa - pb;
                // Then sort by position
                return a.position - b.position;
            });

        const channel = channels.first();
        if (channel) {
            await channel.send({
                flags: MessageFlags.IsComponentsV2,
                components: [container]
            });

            // Performance tracking
            const duration = Date.now() - startTime;
            if (joinNotificationMetrics.verboseLogging || duration > 200) {
                console.log(`[owner-join-notification] ✅ Join notification sent to #${channel.name} in ${duration}ms`);
            }

            return true;
        }

        if (joinNotificationMetrics.verboseLogging) {
            console.log('[owner-join-notification] No suitable channel found for join notification');
        }

        return false;
    } catch (e) {
        console.error('[owner-join-notification] ❌ Error sending join notification:', e);
        return false;
    }
}

/**
 * Build the join notification preview container (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced performance monitoring for container building
 * @param {Object} params - Parameters for building preview
 * @returns {ContainerBuilder} Join notification preview container
 */
function buildJoinNotificationPreview({ message, botUser, guildName = null }) {
    const startTime = Date.now();

    try {
        joinNotificationMetrics.containersBuilt++;

        // Replace placeholders for preview
        let processedMessage = message;
        if (guildName) {
            processedMessage = message.replace(/{serverName}/g, guildName);
        }

        const heading = new TextDisplayBuilder().setContent(`# [${BOT_NAME}](${BOT_LINK})`);
        const quote = new TextDisplayBuilder().setContent(`> ${processedMessage}`);
        const section = new SectionBuilder()
            .setThumbnailAccessory({ media: { url: botUser.displayAvatarURL({ forceStatic: false }) } })
            .addTextDisplayComponents(heading, quote);
        const container = new ContainerBuilder()
            .addSectionComponents(section)
            .setAccentColor(OPERATION_COLORS.ENTITY);

        // Performance tracking
        const duration = Date.now() - startTime;
        if (joinNotificationMetrics.verboseLogging || duration > 50) {
            console.log(`[owner-join-notification] ✅ Preview container built in ${duration}ms`);
        }

        return container;
    } catch (error) {
        console.error('[owner-join-notification] ❌ Error building preview container:', error);
        throw error;
    }
}

/**
 * Build the join notification setup container (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced performance monitoring for container building
 * @param {Object} params - Parameters for building setup container
 * @returns {ContainerBuilder} Join notification setup container
 */
function buildJoinNotificationSetupContainer({ enabled }) {
    const startTime = Date.now();

    try {
        joinNotificationMetrics.containersBuilt++;

        // Section with back button (like buildServersContainer)
        const heading = new TextDisplayBuilder().setContent('# join notification');
        const backButton = new ButtonBuilder()
            .setCustomId('owner-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(heading)
            .setButtonAccessory(backButton);
        // Static description of the feature
        const quote = new TextDisplayBuilder().setContent('> hi hello its me');
        const status = new TextDisplayBuilder().setContent(`**send message:** ${enabled ? 'enabled' : 'disabled'}`);
        // Separator
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
        // Select menu for join notification config (inside container)
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('owner-joinnotification')
            .setPlaceholder('join notification')
            .addOptions([
                {
                    label: enabled ? 'disable' : 'enable',
                    value: 'toggle',
                    description: enabled ? 'Currently enabled' : 'Currently disabled',
                    default: false
                },
                {
                    label: 'join message',
                    value: 'edit',
                    description: 'Edit the join notification message',
                    default: false
                }
            ]);
        const selectRow = new ActionRowBuilder().addComponents(selectMenu);
        // Build container
        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(quote, status)
            .addSeparatorComponents(separator)
            .addActionRowComponents(selectRow)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        // Performance tracking
        const duration = Date.now() - startTime;
        if (joinNotificationMetrics.verboseLogging || duration > 50) {
            console.log(`[owner-join-notification] ✅ Setup container built in ${duration}ms`);
        }

        return container;
    } catch (error) {
        console.error('[owner-join-notification] ❌ Error building setup container:', error);
        throw error;
    }
}

/**
 * Cache invalidation functions (Enterprise-Grade Cache Management)
 * OPTIMIZED: Intelligent cache invalidation for data consistency
 */
function invalidateJoinNotificationConfigCache() {
    const cacheKey = `config_${JOIN_NOTIFICATION_DB_KEY}`;
    joinNotificationConfigCache.delete(cacheKey);

    if (joinNotificationMetrics.verboseLogging) {
        console.log('[owner-join-notification] 🗑️ Invalidated join notification config cache');
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getJoinNotificationStats() {
    const cacheHitRate = joinNotificationMetrics.cacheHits + joinNotificationMetrics.cacheMisses > 0 ?
        (joinNotificationMetrics.cacheHits / (joinNotificationMetrics.cacheHits + joinNotificationMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: joinNotificationMetrics.cacheHits,
            cacheMisses: joinNotificationMetrics.cacheMisses,
            databaseQueries: joinNotificationMetrics.databaseQueries,
            averageQueryTime: `${joinNotificationMetrics.averageQueryTime.toFixed(2)}ms`,
            configurationsFetched: joinNotificationMetrics.configurationsFetched,
            configurationsUpdated: joinNotificationMetrics.configurationsUpdated,
            notificationsSent: joinNotificationMetrics.notificationsSent,
            containersBuilt: joinNotificationMetrics.containersBuilt,
            lastOptimization: new Date(joinNotificationMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            joinNotificationConfig: joinNotificationConfigCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            joinNotificationConfig: joinNotificationConfigCache.getStats().memoryUsage,
            total: joinNotificationConfigCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    joinNotificationMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getJoinNotificationStats();
    console.log(`[owner-join-notification] 📊 Performance Report:`);
    console.log(`[owner-join-notification]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[owner-join-notification]   Database Queries: ${stats.performance.databaseQueries}`);
    console.log(`[owner-join-notification]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[owner-join-notification]   Configurations Fetched: ${stats.performance.configurationsFetched}`);
    console.log(`[owner-join-notification]   Configurations Updated: ${stats.performance.configurationsUpdated}`);
    console.log(`[owner-join-notification]   Notifications Sent: ${stats.performance.notificationsSent}`);
    console.log(`[owner-join-notification]   Containers Built: ${stats.performance.containersBuilt}`);
    console.log(`[owner-join-notification]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[owner-join-notification]   System Health: ${stats.systemHealth.status}`);

    return stats;
}

/**
 * Clear join notification cache (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearJoinNotificationCache() {
    joinNotificationConfigCache.clear();
    console.log('[owner-join-notification] 🗑️ Cleared join notification cache');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, joinNotificationMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getJoinNotificationConfig,
    setJoinNotificationConfig,
    sendJoinNotification,
    buildJoinNotificationPreview,
    buildJoinNotificationSetupContainer,

    // Enhanced optimization functions
    getJoinNotificationStats,
    performanceCleanupAndOptimization,
    clearJoinNotificationCache,
    invalidateJoinNotificationConfigCache,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...joinNotificationMetrics })
};
