const { Slash<PERSON>ommandBuilder, Con<PERSON>er<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ThumbnailBuilder, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, MessageFlags, StringSelectMenuBuilder } = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { optimizedFindOne, optimizedFind, optimizedUpdateOne, optimizedDeleteOne, optimizedAggregate, optimizedCountDocuments } = require("../../utils/database-optimizer.js");
const { getUserGuildRank, getUserGlobalRank } = require("../../utils/expRanking.js");
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');
const { incrementCommandUsage } = require("../../utils/commandUsage.js");
const { registerCommand } = require("../../utils/commandInvalidation.js");
const { getUserGlobalInventory, getUserInventory } = require("../../utils/itemDrops.js");
const { getCachedUserGlobalInventory } = require("../../utils/itemCache.js");
const { getUserProfile } = require("../../utils/userProfileCache.js");
const { formatDuration, getVoiceExpGuildRank, getTextExpGuildRank, getVoiceExpGlobalRank, getTextExpGlobalRank, calculateAvgPerDay } = require("../../utils/statsUtils.js");
const { OPERATION_COLORS, LEGACY_COLORS } = require('../../utils/colors.js');
const { getStarfallMenuDescription } = require('../../utils/starfall.js');

/**
 * You Command System (Enterprise-Grade Performance Optimized)
 * Handles all user profile and statistics with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const youMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    profilesProcessed: 0,
    inventoryLookupsProcessed: 0,
    expCalculationsProcessed: 0,
    rankingCalculationsProcessed: 0,
    statsCalculationsProcessed: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const userSettingsCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user settings
const guildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const memberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member data
const userInventoryCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user inventory data
const expDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for experience data
const rankingCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for ranking calculations
const statsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for statistics calculations

// Register caches for global cleanup
registerCache(userSettingsCache);
registerCache(guildConfigCache);
registerCache(memberDataCache);
registerCache(userInventoryCache);
registerCache(expDataCache);
registerCache(rankingCache);
registerCache(statsCache);

/**
 * Get cached user settings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User settings data
 */
async function getCachedUserSettings(userId) {
    const startTime = Date.now();

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = userSettingsCache.get(userId);
        if (cached) {
            youMetrics.cacheHits++;
            if (youMetrics.verboseLogging) {
                console.log(`[you] ⚡ User settings cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        youMetrics.cacheMisses++;
        youMetrics.databaseQueries++;

        const userData = await optimizedFindOne('users', { id: userId });
        const settings = userData || {};

        // Cache the result
        userSettingsCache.set(userId, settings);

        const duration = Date.now() - startTime;
        youMetrics.averageQueryTime =
            (youMetrics.averageQueryTime * (youMetrics.databaseQueries - 1) + duration) /
            youMetrics.databaseQueries;

        if (youMetrics.verboseLogging || duration > 100) {
            console.log(`[you] ✅ User settings fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        return settings;
    } catch (error) {
        console.error(`[you] ❌ Error getting user settings for ${userId}:`, error);
        return {};
    }
}

/**
 * Get cached guild configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild configuration data
 */
async function getCachedGuildConfig(guildId) {
    const startTime = Date.now();

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildConfigCache.get(guildId);
        if (cached) {
            youMetrics.cacheHits++;
            if (youMetrics.verboseLogging) {
                console.log(`[you] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        youMetrics.cacheMisses++;
        youMetrics.databaseQueries++;

        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const config = guildData || {};

        // Cache the result
        guildConfigCache.set(guildId, config);

        const duration = Date.now() - startTime;
        youMetrics.averageQueryTime =
            (youMetrics.averageQueryTime * (youMetrics.databaseQueries - 1) + duration) /
            youMetrics.databaseQueries;

        if (youMetrics.verboseLogging || duration > 100) {
            console.log(`[you] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return config;
    } catch (error) {
        console.error(`[you] ❌ Error getting guild config for ${guildId}:`, error);
        return {};
    }
}

/**
 * Get cached member data
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Member data
 */
async function getCachedMemberData(userId, guildId) {
    const cacheKey = `${userId}_${guildId}`;

    // OPTIMIZED: Check LRU cache first (automatic TTL and size management)
    const cached = memberDataCache.get(cacheKey);
    if (cached) {
        return cached;
    }

    try {
        const memberData = await optimizedFindOne("member", {
            guildId: guildId,
            userId: userId
        });
        const data = memberData || {};

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        memberDataCache.set(cacheKey, data);

        return data;
    } catch (error) {
        console.error(`[you] ❌ Error getting member data for ${userId} in ${guildId}:`, error);
        return {};
    }
}

/**
 * Get cached user inventory data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for inventory lookups
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional, for guild-specific inventory)
 * @returns {Promise<Array>} User inventory items
 */
async function getCachedUserInventory(userId, guildId = null) {
    const startTime = Date.now();
    const cacheKey = guildId ? `inventory_${userId}_${guildId}` : `inventory_${userId}_global`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = userInventoryCache.get(cacheKey);
        if (cached) {
            youMetrics.cacheHits++;
            if (youMetrics.verboseLogging) {
                console.log(`[you] ⚡ User inventory cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        youMetrics.cacheMisses++;
        youMetrics.databaseQueries++;
        youMetrics.inventoryLookupsProcessed++;

        // Build query
        const query = { userId: userId };
        if (guildId) {
            query.guildId = guildId;
        }

        // Get user inventory
        const inventory = await optimizedFind("user_inventory", query);

        // Cache the result
        userInventoryCache.set(cacheKey, inventory);

        const duration = Date.now() - startTime;
        youMetrics.averageQueryTime =
            (youMetrics.averageQueryTime * (youMetrics.databaseQueries - 1) + duration) /
            youMetrics.databaseQueries;

        if (youMetrics.verboseLogging || duration > 100) {
            console.log(`[you] ✅ User inventory fetched for ${userId}: ${inventory.length} items in ${duration}ms - cached for future access`);
        }

        return inventory;
    } catch (error) {
        console.error(`[you] ❌ Error getting user inventory for ${userId}:`, error);
        return [];
    }
}

/**
 * Get cached experience data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for experience calculations
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional, for guild-specific exp)
 * @returns {Promise<Object>} Experience data
 */
async function getCachedExpData(userId, guildId = null) {
    const startTime = Date.now();
    const cacheKey = guildId ? `exp_${userId}_${guildId}` : `exp_${userId}_global`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = expDataCache.get(cacheKey);
        if (cached) {
            youMetrics.cacheHits++;
            if (youMetrics.verboseLogging) {
                console.log(`[you] ⚡ Experience data cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        youMetrics.cacheMisses++;
        youMetrics.databaseQueries++;
        youMetrics.expCalculationsProcessed++;

        // Get member data for experience calculations
        let expData = {};
        if (guildId) {
            const memberData = await getCachedMemberData(userId, guildId);
            expData = memberData.exp || {};
        } else {
            const userData = await getCachedUserSettings(userId);
            expData = userData.exp || {};
        }

        // Cache the result
        expDataCache.set(cacheKey, expData);

        const duration = Date.now() - startTime;
        youMetrics.averageQueryTime =
            (youMetrics.averageQueryTime * (youMetrics.databaseQueries - 1) + duration) /
            youMetrics.databaseQueries;

        if (youMetrics.verboseLogging || duration > 50) {
            console.log(`[you] ✅ Experience data fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        return expData;
    } catch (error) {
        console.error(`[you] ❌ Error getting experience data for ${userId}:`, error);
        return {};
    }
}

/**
 * Invalidate user-related caches
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional)
 */
function invalidateUserCaches(userId, guildId = null) {
    userSettingsCache.delete(userId);
    expDataCache.delete(`exp_${userId}_global`);

    if (guildId) {
        const memberCacheKey = `${userId}_${guildId}`;
        memberDataCache.delete(memberCacheKey);
        expDataCache.delete(`exp_${userId}_${guildId}`);
        userInventoryCache.delete(`inventory_${userId}_${guildId}`);
    }

    // Clear global inventory cache
    userInventoryCache.delete(`inventory_${userId}_global`);

    if (youMetrics.verboseLogging) {
        console.log(`[you] 🗑️ Invalidated caches for user ${userId}`);
    }
}

/**
 * Get comprehensive you system performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive you system performance data
 */
function getYouSystemStats() {
    const cacheHitRate = youMetrics.cacheHits + youMetrics.cacheMisses > 0 ?
        (youMetrics.cacheHits / (youMetrics.cacheHits + youMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: youMetrics.cacheHits,
            cacheMisses: youMetrics.cacheMisses,
            databaseQueries: youMetrics.databaseQueries,
            averageQueryTime: `${youMetrics.averageQueryTime.toFixed(2)}ms`,
            profilesProcessed: youMetrics.profilesProcessed,
            inventoryLookupsProcessed: youMetrics.inventoryLookupsProcessed,
            expCalculationsProcessed: youMetrics.expCalculationsProcessed,
            rankingCalculationsProcessed: youMetrics.rankingCalculationsProcessed,
            statsCalculationsProcessed: youMetrics.statsCalculationsProcessed,
            lastOptimization: new Date(youMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            userSettings: userSettingsCache.getStats(),
            guildConfig: guildConfigCache.getStats(),
            memberData: memberDataCache.getStats(),
            userInventory: userInventoryCache.getStats(),
            expData: expDataCache.getStats(),
            ranking: rankingCache.getStats(),
            stats: statsCache.getStats()
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    youMetrics.lastOptimization = Date.now();

    const stats = getYouSystemStats();
    if (youMetrics.verboseLogging) {
        console.log(`[you] 📊 Performance Report:`);
        console.log(`[you]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[you]   Profiles Processed: ${stats.performance.profilesProcessed}`);
        console.log(`[you]   Inventory Lookups: ${stats.performance.inventoryLookupsProcessed}`);
        console.log(`[you]   Exp Calculations: ${stats.performance.expCalculationsProcessed}`);
        console.log(`[you]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[you]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, youMetrics.performanceReportInterval);

// Build simplified top-level hub menu (1:1 with featuresMenu.js - hide current selection, show back option)
async function buildYouHubMenu(username, userId, currentSelection = null, showBack = false) {
    const options = [];

    // Only add "you are username" option if it's not currently selected AND not showing back button
    if (currentSelection !== 'you' && !showBack) {
        options.push({
            label: `you are ${username}`,
            value: 'you',
            description: 'levels, exp, and inventory',
            emoji: '👤'
        });
    }

    // Only add starfall option if it's not currently selected
    if (currentSelection !== 'daily') {
        // Get dynamic starfall description
        const starfallDescription = await getStarfallMenuDescription(userId);

        options.push({
            label: 'starfall',
            value: 'daily',
            description: starfallDescription,
            emoji: '⭐'
        });
    }

    // Only add settings option if it's not currently selected
    if (currentSelection !== 'settings') {
        options.push({
            label: 'settings',
            value: 'settings',
            description: 'notification and display settings',
            emoji: '⚙️'
        });
    }

    // Add back option if requested (like featuresMenu.js show17 parameter)
    if (showBack) {
        options.push({
            label: 'you',
            value: 'you',
            description: 'back to main',
            emoji: '👤'
        });
    }

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('you-hub-select')
        .setPlaceholder(currentSelection === 'you' ? `you are ${username}` :
                       currentSelection === 'daily' ? 'starfall' :
                       currentSelection === 'settings' ? 'settings' :
                       `you are ${username}`)
        .addOptions(options);

    return new ActionRowBuilder().addComponents(selectMenu);
}

// Build simplified feature menu (hide current selection like featuresMenu.js)
function buildYouFeatureMenu(currentFeature = 'inventory') {
    const options = [];

    // Only add inventory option if it's not currently selected
    if (currentFeature !== 'inventory') {
        options.push({
            label: 'inventory',
            description: 'items and collectibles',
            value: 'inventory'
        });
    }

    // Only add text exp stats option if it's not currently selected
    if (currentFeature !== 'text-stats') {
        options.push({
            label: 'text exp stats',
            description: 'detailed text exp statistics',
            value: 'text-stats'
        });
    }

    // Only add voice exp stats option if it's not currently selected
    if (currentFeature !== 'voice-stats') {
        options.push({
            label: 'voice exp stats',
            description: 'detailed voice exp statistics',
            value: 'voice-stats'
        });
    }

    // Clean up placeholder text (remove dashes)
    let placeholderText = currentFeature || 'choose feature';
    if (placeholderText === 'text-stats') {
        placeholderText = 'text stats';
    } else if (placeholderText === 'voice-stats') {
        placeholderText = 'voice stats';
    }

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('you-feature-select')
        .setPlaceholder(placeholderText)
        .addOptions(options);

    return new ActionRowBuilder().addComponents(selectMenu);
}



module.exports = {
    data: new SlashCommandBuilder()
        .setName('you')
        .setDescription('i see u'),
    async execute(interaction) {
        // Register this command to invalidate previous ones
        await registerCommand(interaction.user.id, 'you', interaction.id, interaction.client);

        // Track command usage
        await incrementCommandUsage('you');

        const user = interaction.user;
        const member = interaction.member;

        // Show main "you" page (initial command)
        await this.showMainPage(interaction, user, member, true);
    },

    // Show simplified main "you" page - OPTIMIZED VERSION
    async showMainPage(interaction, user, member, isInitialCommand = false) {
        const startTime = Date.now();

        try {
            // OPTIMIZATION 1: Parallel database queries instead of sequential
            const [guildExpData, globalExpData] = await Promise.all([
                this.getOptimizedGuildExpData(user.id, interaction.guild.id),
                this.getOptimizedGlobalExpData(user.id)
            ]);

            // Extract guild data
            const { guildExp, guildLevel, guildNextLevelExp, guildRole, guildRoleIcon } = guildExpData;

            // Extract global data
            const { globalExp, globalLevel, globalNextLevelExp, globalLevelName, globalLevelIcon, prestigeLevel } = globalExpData;

            // OPTIMIZATION: Performance logging
            const loadTime = Date.now() - startTime;
            if (loadTime > 1000) {
                console.log(`[you] PERFORMANCE: Main page loaded in ${loadTime}ms (SLOW)`);
            } else {
                console.log(`[you] PERFORMANCE: Main page loaded in ${loadTime}ms`);
            }

            // Build UI components
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'you');

        // Build thumbnail section with title and quote
        let titleText = '';
        if (guildRole) {
            titleText = `## ${guildRoleIcon} ${guildRole} | ${globalLevelIcon} ${globalLevelName}`;
        } else {
            titleText = `## ${globalLevelIcon} ${globalLevelName}`;
        }

        const quoteText = `you in ${interaction.guild.name} and globally`;

        const profileImage = new ThumbnailBuilder({
            media: { url: user.displayAvatarURL({ forceStatic: false }) }
        });

        const titleTextDisplay = new TextDisplayBuilder().setContent(titleText);
        const quoteTextDisplay = new TextDisplayBuilder().setContent(`> ${quoteText}`);

        const titleSection = new SectionBuilder()
            .setThumbnailAccessory(profileImage)
            .addTextDisplayComponents(titleTextDisplay, quoteTextDisplay);

        // Level display with proper X/MAX format (accounting for prestige)
        // Import MAX_LEVELS from exp.js to get the correct guild level limit
        const { MAX_LEVELS } = require('./exp.js');
        const guildMaxLevel = MAX_LEVELS; // Guild levels are capped at MAX_LEVELS (excluding Level 0)

        // Get dynamic global max level from database and prestige info
        const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
        const { getPrestigeDisplayText } = require('../../utils/prestigeUI.js');
        const globalLevels = await getCachedGlobalLevels();
        const globalMaxLevel = globalLevels.length > 0 ? Math.max(...globalLevels.map(l => l.level)) : 10;

        // Get prestige display text
        const prestigeText = getPrestigeDisplayText(prestigeLevel || 0);

        // Build level content using modern format
        let levelContent = '';

        if (guildRole) {
            levelContent += `**level:** ${guildLevel}/${guildMaxLevel} | ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;
            levelContent += `**progress:** ${guildExp}/${guildNextLevelExp || guildExp} | ${globalExp}/${globalNextLevelExp || globalExp}\n`;
            levelContent += `**exp:** ${guildExp.toLocaleString()} | ${globalExp.toLocaleString()}\n`;
        } else {
            levelContent += `**level:** ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;
            levelContent += `**progress:** ${globalExp}/${globalNextLevelExp || globalExp}\n`;
            levelContent += `**exp:** ${globalExp.toLocaleString()}\n`;
        }

        // Get starfall data for stars display
        const { getStarfallData } = require('../../utils/starfall.js');
        const starfallData = await getStarfallData(user.id);
        levelContent += `**stars:** ${starfallData.stars.toLocaleString()}`;

        const levelDisplay = new TextDisplayBuilder().setContent(levelContent);

        // Get current level rewards for display
        const { getCurrentLevelRewards } = require('../../utils/globalLevels.js');
        const currentRewards = await getCurrentLevelRewards(user.id);
        let rewardsContent = null;

        if (currentRewards && (currentRewards.items?.length > 0 || currentRewards.xpBooster || currentRewards.dropBooster || currentRewards.stars)) {
            let rewardsText = '## level rewards\n';

            if (currentRewards.items && currentRewards.items.length > 0) {
                rewardsText += `**items:** ${currentRewards.items.length} item${currentRewards.items.length !== 1 ? 's' : ''}\n`;
            }
            if (currentRewards.xpBooster) {
                rewardsText += `**xp booster:** ${currentRewards.xpBooster}x\n`;
            }
            if (currentRewards.dropBooster) {
                rewardsText += `**drop booster:** ${currentRewards.dropBooster}x\n`;
            }
            if (currentRewards.stars) {
                rewardsText += `**stars:** ${currentRewards.stars}\n`;
            }

            // Remove trailing newline
            rewardsText = rewardsText.trim();
            rewardsContent = new TextDisplayBuilder().setContent(rewardsText);
        }

        // Large separator
        const largeSeparator = new SeparatorBuilder()
            .setSpacing(SeparatorSpacingSize.Large)
            .setDivider(true);

        // Feature menu (preselect inventory)
        const featureMenu = buildYouFeatureMenu('inventory');

        // Build full inventory content with select menus
        const inventoryContent = await this.buildInventoryContent({ user, guild: interaction.guild }, user);

        // Build container (without hub menu) - handle inventory content array
        const container = new ContainerBuilder()
            .addSectionComponents(titleSection)
            .addTextDisplayComponents(levelDisplay);

        // Add level rewards if they exist
        if (rewardsContent) {
            container.addTextDisplayComponents(rewardsContent);
        }

        container.addSeparatorComponents(largeSeparator)
            .addActionRowComponents(featureMenu);

        // Add full inventory content (handle array format)
        if (Array.isArray(inventoryContent) && inventoryContent.length > 0) {
            container.addTextDisplayComponents(inventoryContent[0]);

            // Add select menus if present
            if (inventoryContent[1]) {
                container.addActionRowComponents(inventoryContent[1]);
            }
            if (inventoryContent[2]) {
                container.addActionRowComponents(inventoryContent[2]);
            }
        } else if (inventoryContent) {
            // Fallback for non-array content
            container.addTextDisplayComponents(inventoryContent);
        }

        container.setAccentColor(OPERATION_COLORS.ENTITY);

        // Check for item notifications if notification center is enabled
        const components = [hubMenu, container];

        // OPTIMIZED: Check user's notification center setting using cached function
        let notificationCenterEnabled = true; // Default to enabled
        try {
            const userData = await getCachedUserSettings(user.id);
            notificationCenterEnabled = userData?.notificationCenterEnabled !== false; // Default to true unless explicitly disabled
        } catch (error) {
            console.error('[you] Error fetching user notification settings:', error);
        }

        if (notificationCenterEnabled) {
            const notificationResult = await this.buildItemNotificationDisplay(user.id, interaction.guild.id, interaction.client);
            if (notificationResult) {
                components.push(notificationResult.container, notificationResult.buttonRow);
            }
        }

        // Use reply() for initial command, editReply() for select menu interactions
        if (isInitialCommand) {
            await interaction.reply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: components
            });
        } else {
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: components
            });
        }

        } catch (error) {
            console.error('[you] Error in showMainPage:', error);

            // Rebuild main page with status message instead of separate error container
            const hubMenu = await buildYouHubMenu(user.username, user.id, 'main');
            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('## you\n\n*Loading your profile...*'))
                .setAccentColor(OPERATION_COLORS.ENTITY);

            // Add status message to container
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Something went wrong loading your profile. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });
        }
    },

    // Handle select menu interactions
    async select(interaction) {
        const selectedValue = interaction.values[0];
        const user = interaction.user;
        const member = interaction.member;

        await interaction.deferUpdate();

        // Handle hub menu selections
        if (interaction.customId === 'you-hub-select') {
            if (selectedValue === 'you') {
                // Back to main page (global view)
                await this.showMainPage(interaction, user, member);
            } else if (selectedValue === 'server') {
                // Show server-specific view
                await this.showServerPage(interaction, user, member);
            } else if (selectedValue === 'daily') {
                await this.showDaily(interaction, user);
            } else if (selectedValue === 'settings') {
                await this.showSettings(interaction, user);
            }
        }
        // Handle feature menu selections (edit same container like EXP)
        else if (interaction.customId === 'you-feature-select') {
            if (selectedValue === 'main') {
                // Back to main page with stats and inventory
                await this.showMainPage(interaction, user, member);
            } else if (selectedValue === 'voice-stats') {
                await this.showFeatureView(interaction, user, member, 'voice-stats');
            } else if (selectedValue === 'text-stats') {
                await this.showFeatureView(interaction, user, member, 'text-stats');
            } else if (selectedValue === 'inventory') {
                // Show inventory view (auto-selection will handle initial state)
                await this.showInventoryView(interaction, user, member);
            }
        }
        // Handle settings menu selections
        else if (interaction.customId === 'you-settings-select') {
            await this.handleSettingsSelect(interaction);
        }
        // Handle inventory type selection
        else if (interaction.customId === 'you-inventory-type-select') {
            const selectedType = selectedValue;

            // Store the selected type in state (using cache like items system)
            this.storeInventoryState(user.id, interaction.guild.id, {
                selectedType: selectedType
            });

            // Rebuild inventory with the selected type
            await this.showInventoryView(interaction, user, member);
        }
        // Handle inventory item selection
        else if (interaction.customId === 'you-inventory-item-select') {
            await this.handleInventoryItemSelect(interaction, user, member);
        }
        // Handle leaderboard type selection (disabled)
        // else if (interaction.customId === 'you-leaderboard-type-select') {
        //     await this.showLeaderboard(interaction, user, selectedValue);
        // }
        // Handle leaderboard item selection (disabled)
        // else if (interaction.customId === 'you-leaderboard-item-select') {
        //     await this.showLeaderboard(interaction, user, 'items', selectedValue);
        // }
    },

    // Show server-specific "you" page
    async showServerPage(interaction, user, member) {
        // Use the original guild-specific logic
        let exp = 0;
        let currentLevel = 0;
        let nextLevelExp = 100;
        let levelRole = null;
        let levelEmoji = '🌱';
        let profile = null;

        try {
            // Get cached user profile (combines EXP, inventory, notifications)
            const { getUserProfile } = require('../../utils/userProfileCache.js');
            profile = await getUserProfile(user.id, interaction.guild.id, interaction.client);

            // Extract EXP data from cached profile
            exp = profile.exp.total;
            currentLevel = profile.exp.currentLevel;
            nextLevelExp = profile.exp.nextLevelExp;
            levelRole = profile.exp.levelRole;
            levelEmoji = profile.exp.levelEmoji;
        } catch (error) {
            console.error('[you] Error fetching server profile:', error);
        }

        // Build server-specific UI
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'server');

        const profileImage = new ThumbnailBuilder({
            media: { url: user.displayAvatarURL({ forceStatic: false }) }
        });

        const levelDisplay = new TextDisplayBuilder().setContent(
            `## ${interaction.guild.name} Level ${currentLevel} ${levelEmoji}${levelRole ? ` ${levelRole}` : ''}`.trim()
        );

        const progressText = nextLevelExp === null
            ? `**server exp:** ${exp.toLocaleString()} (max level reached!)`
            : `**server exp:** ${exp.toLocaleString()} / ${nextLevelExp.toLocaleString()}`;

        const progressDisplay = new TextDisplayBuilder().setContent(progressText);

        const levelSection = new SectionBuilder()
            .setThumbnailAccessory(profileImage)
            .addTextDisplayComponents(levelDisplay, progressDisplay);

        // Server stats
        const statsDisplay = new TextDisplayBuilder().setContent(
            `## server stats\n` +
            `**server exp:** ${exp.toLocaleString()}\n` +
            `**server level:** ${currentLevel}\n` +
            `**server role:** ${levelRole || 'none'}`
        );

        // Server inventory
        let inventoryContent = '## server inventory\n*No items yet - gain EXP to find items!*';
        if (profile && profile.inventory.hasItems) {
            inventoryContent = profile.inventory.emoteDisplay.replace('## inventory', '## server inventory');
        }
        const inventoryDisplay = new TextDisplayBuilder().setContent(inventoryContent);

        // Build container
        const container = new ContainerBuilder()
            .addSectionComponents(levelSection)
            .addTextDisplayComponents(statsDisplay, inventoryDisplay)
            .setAccentColor(OPERATION_COLORS.NEUTRAL);

        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [hubMenu, container]
        });
    },

    // Show inventory view with new layout (matches main page design)
    async showInventoryView(interaction, user, member) {
        const startTime = Date.now();

        try {
            // OPTIMIZATION: Reuse optimized methods instead of duplicating logic
            const [guildExpData, globalExpData] = await Promise.all([
                this.getOptimizedGuildExpData(user.id, interaction.guild.id),
                this.getOptimizedGlobalExpData(user.id)
            ]);

            const { guildExp, guildLevel, guildNextLevelExp, guildRole, guildRoleIcon } = guildExpData;
            const { globalExp, globalLevel, globalNextLevelExp, globalLevelName, globalLevelIcon, prestigeLevel } = globalExpData;

            // OPTIMIZATION: Performance logging
            const loadTime = Date.now() - startTime;
            if (loadTime > 500) {
                console.log(`[you] PERFORMANCE: Inventory view loaded in ${loadTime}ms (SLOW)`);
            }

            // Build thumbnail section with title and quote (same as main page)
            let titleText = '';
            if (guildRole) {
                titleText = `## ${guildRoleIcon} ${guildRole} | ${globalLevelIcon} ${globalLevelName}`;
            } else {
                titleText = `## ${globalLevelIcon} ${globalLevelName}`;
            }

            const quoteText = `you in ${interaction.guild.name} and globally`;

            const profileImage = new ThumbnailBuilder({
                media: { url: user.displayAvatarURL({ forceStatic: false }) }
            });

            const titleTextDisplay = new TextDisplayBuilder().setContent(titleText);
            const quoteTextDisplay = new TextDisplayBuilder().setContent(`> ${quoteText}`);

            const titleSection = new SectionBuilder()
                .setThumbnailAccessory(profileImage)
                .addTextDisplayComponents(titleTextDisplay, quoteTextDisplay);

            // Level display with proper X/MAX format (same as main page)
            const { MAX_LEVELS } = require('./exp.js');
            const guildMaxLevel = MAX_LEVELS;

            // Get dynamic global max level from database and prestige info
            const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
            const { getPrestigeDisplayText } = require('../../utils/prestigeUI.js');
            const globalLevels = await getCachedGlobalLevels();
            const globalMaxLevel = globalLevels.length > 0 ? Math.max(...globalLevels.map(l => l.level)) : 10;

            // Get prestige display text
            const prestigeText = getPrestigeDisplayText(prestigeLevel || 0);

            // Build level content using modern format
            let levelContent = '';

            if (guildRole) {
                levelContent += `**level:** ${guildLevel}/${guildMaxLevel} | ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;
                levelContent += `**progress:** ${guildExp}/${guildNextLevelExp || guildExp} | ${globalExp}/${globalNextLevelExp || globalExp}\n`;
                levelContent += `**exp:** ${guildExp.toLocaleString()} | ${globalExp.toLocaleString()}\n`;
            } else {
                levelContent += `**level:** ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;
                levelContent += `**progress:** ${globalExp}/${globalNextLevelExp || globalExp}\n`;
                levelContent += `**exp:** ${globalExp.toLocaleString()}\n`;
            }

            // Get starfall data for stars display
            const { getStarfallData } = require('../../utils/starfall.js');
            const starfallData = await getStarfallData(user.id);
            levelContent += `**stars:** ${starfallData.stars.toLocaleString()}`;

            const levelDisplay = new TextDisplayBuilder().setContent(levelContent);

            // Get current level rewards for display
            const { getCurrentLevelRewards } = require('../../utils/globalLevels.js');
            const currentRewards = await getCurrentLevelRewards(user.id);
            let rewardsContent = null;

            if (currentRewards && (currentRewards.items?.length > 0 || currentRewards.xpBooster || currentRewards.dropBooster || currentRewards.stars)) {
                let rewardsText = '## level rewards\n';

                if (currentRewards.items && currentRewards.items.length > 0) {
                    rewardsText += `**items:** ${currentRewards.items.length} item${currentRewards.items.length !== 1 ? 's' : ''}\n`;
                }
                if (currentRewards.xpBooster) {
                    rewardsText += `**xp booster:** ${currentRewards.xpBooster}x\n`;
                }
                if (currentRewards.dropBooster) {
                    rewardsText += `**drop booster:** ${currentRewards.dropBooster}x\n`;
                }
                if (currentRewards.stars) {
                    rewardsText += `**stars:** ${currentRewards.stars}\n`;
                }

                // Remove trailing newline
                rewardsText = rewardsText.trim();
                rewardsContent = new TextDisplayBuilder().setContent(rewardsText);
            }

            // Build inventory content with select menus
            const inventoryContent = await this.buildInventoryContent(interaction, user);

            // Build menus
            const hubMenu = await buildYouHubMenu(user.username, user.id, 'you');
            const featureMenu = buildYouFeatureMenu('inventory');

            // Build container with new layout (same as main page)
            const largeSeparator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

            const container = new ContainerBuilder()
                .addSectionComponents(titleSection)
                .addTextDisplayComponents(levelDisplay);

            // Add level rewards if they exist
            if (rewardsContent) {
                container.addTextDisplayComponents(rewardsContent);
            }

            container.addSeparatorComponents(largeSeparator)
                .addActionRowComponents(featureMenu);

            // Add inventory content (handle array format)
            if (Array.isArray(inventoryContent) && inventoryContent.length > 0) {
                container.addTextDisplayComponents(inventoryContent[0]);

                // Add select menus if present
                if (inventoryContent[1]) {
                    container.addActionRowComponents(inventoryContent[1]);
                }
                if (inventoryContent[2]) {
                    container.addActionRowComponents(inventoryContent[2]);
                }
            } else if (inventoryContent) {
                // Fallback for non-array content
                container.addTextDisplayComponents(inventoryContent);
            }

            container.setAccentColor(OPERATION_COLORS.ENTITY);

            // Check for item notifications if notification center is enabled
            const components = [hubMenu, container];

            // Check user's notification center setting
            // OPTIMIZED: Check user's notification center setting using cached function
            let notificationCenterEnabled = true; // Default to enabled
            try {
                const userData = await getCachedUserSettings(user.id);
                notificationCenterEnabled = userData?.notificationCenterEnabled !== false; // Default to true unless explicitly disabled
            } catch (error) {
                console.error('[you] Error fetching user notification settings:', error);
            }

            if (notificationCenterEnabled) {
                const notificationResult = await this.buildItemNotificationDisplay(user.id, interaction.guild.id, interaction.client);
                if (notificationResult) {
                    components.push(notificationResult.container, notificationResult.buttonRow);
                }
            }

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: components
            });

        } catch (error) {
            console.error('[you] Error in showInventoryView:', error);

            // Rebuild inventory view with status message instead of separate error container
            const hubMenu = await buildYouHubMenu(user.username, user.id, 'inventory');
            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('## inventory\n\n*Loading your inventory...*'))
                .setAccentColor(OPERATION_COLORS.ENTITY);

            // Add status message to container
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Something went wrong loading your inventory. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });
        }
    },

    // Show feature view (like EXP subcomponents - edits same container) - OPTIMIZED
    async showFeatureView(interaction, user, member, feature) {
        const startTime = Date.now();

        // OPTIMIZATION: Reuse optimized methods instead of duplicating logic
        const [guildExpData, globalExpData] = await Promise.all([
            this.getOptimizedGuildExpData(user.id, interaction.guild.id),
            this.getOptimizedGlobalExpData(user.id)
        ]);

        const { guildExp, guildLevel, guildNextLevelExp, guildRole, guildRoleIcon } = guildExpData;
        const { globalExp, globalLevel, globalNextLevelExp, globalLevelName, globalLevelIcon, prestigeLevel } = globalExpData;
        // OPTIMIZATION: Performance logging
        const loadTime = Date.now() - startTime;
        if (loadTime > 500) {
            console.log(`[you] PERFORMANCE: Feature view loaded in ${loadTime}ms (SLOW)`);
        }

        // Build thumbnail section with title and quote (same as main page)
        let titleText = '';
        if (guildRole) {
            titleText = `## ${guildRoleIcon} ${guildRole} | ${globalLevelIcon} ${globalLevelName}`;
        } else {
            titleText = `## ${globalLevelIcon} ${globalLevelName}`;
        }

        const quoteText = `you in ${interaction.guild.name} and globally`;

        const profileImage = new ThumbnailBuilder({
            media: { url: user.displayAvatarURL({ forceStatic: false }) }
        });

        const titleTextDisplay = new TextDisplayBuilder().setContent(titleText);
        const quoteTextDisplay = new TextDisplayBuilder().setContent(`> ${quoteText}`);

        const titleSection = new SectionBuilder()
            .setThumbnailAccessory(profileImage)
            .addTextDisplayComponents(titleTextDisplay, quoteTextDisplay);

        // Level display with proper X/MAX format (same as main page)
        const { MAX_LEVELS } = require('./exp.js');
        const guildMaxLevel = MAX_LEVELS;

        // Get dynamic global max level from database and prestige info
        const { getCachedGlobalLevels } = require('../../utils/globalLevels.js');
        const { getPrestigeDisplayText } = require('../../utils/prestigeUI.js');
        const globalLevels = await getCachedGlobalLevels();
        const globalMaxLevel = globalLevels.length > 0 ? Math.max(...globalLevels.map(l => l.level)) : 10;

        // Get prestige display text
        const prestigeText = getPrestigeDisplayText(prestigeLevel || 0);

        // Build level content using modern format
        let levelContent = '';

        if (guildRole) {
            levelContent += `**level:** ${guildLevel}/${guildMaxLevel} | ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;
            levelContent += `**progress:** ${guildExp}/${guildNextLevelExp || guildExp} | ${globalExp}/${globalNextLevelExp || globalExp}\n`;
            levelContent += `**exp:** ${guildExp.toLocaleString()} | ${globalExp.toLocaleString()}\n`;
        } else {
            levelContent += `**level:** ${prestigeText}${globalLevel}/${globalMaxLevel}\n`;
            levelContent += `**progress:** ${globalExp}/${globalNextLevelExp || globalExp}\n`;
            levelContent += `**exp:** ${globalExp.toLocaleString()}\n`;
        }

        // Get starfall data for stars display
        const { getStarfallData } = require('../../utils/starfall.js');
        const starfallData = await getStarfallData(user.id);
        levelContent += `**stars:** ${starfallData.stars.toLocaleString()}`;

        const levelDisplay = new TextDisplayBuilder().setContent(levelContent);

        // Get current level rewards for display
        const { getCurrentLevelRewards } = require('../../utils/globalLevels.js');
        const currentRewards = await getCurrentLevelRewards(user.id);
        let rewardsContent = null;

        if (currentRewards && (currentRewards.items?.length > 0 || currentRewards.xpBooster || currentRewards.dropBooster || currentRewards.stars)) {
            let rewardsText = '## level rewards\n';

            if (currentRewards.items && currentRewards.items.length > 0) {
                rewardsText += `**items:** ${currentRewards.items.length} item${currentRewards.items.length !== 1 ? 's' : ''}\n`;
            }
            if (currentRewards.xpBooster) {
                rewardsText += `**xp booster:** ${currentRewards.xpBooster}x\n`;
            }
            if (currentRewards.dropBooster) {
                rewardsText += `**drop booster:** ${currentRewards.dropBooster}x\n`;
            }
            if (currentRewards.stars) {
                rewardsText += `**stars:** ${currentRewards.stars}\n`;
            }

            // Remove trailing newline
            rewardsText = rewardsText.trim();
            rewardsContent = new TextDisplayBuilder().setContent(rewardsText);
        }

        // Build feature-specific content
        let featureContent;
        if (feature === 'voice-stats') {
            featureContent = await this.buildVoiceStatsContent(interaction, user);
        } else if (feature === 'text-stats') {
            featureContent = await this.buildTextStatsContent(interaction, user);
        } else if (feature === 'inventory') {
            // OPTIMIZATION: Use full inventory content when specifically requested
            featureContent = await this.buildInventoryContent(interaction, user);
        }

        // Build menus (pass current feature to show selected option)
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'you');
        const featureMenu = buildYouFeatureMenu(feature);

        // Build container with new layout (same as main page)
        const largeSeparator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

        // Build container (without hub menu) - handle feature content array
        const container = new ContainerBuilder()
            .addSectionComponents(titleSection)
            .addTextDisplayComponents(levelDisplay);

        // Add level rewards if they exist
        if (rewardsContent) {
            container.addTextDisplayComponents(rewardsContent);
        }

        container.addSeparatorComponents(largeSeparator)
            .addActionRowComponents(featureMenu);

        // Add feature content (handle array format like inventory)
        if (Array.isArray(featureContent) && featureContent.length > 0) {
            container.addTextDisplayComponents(featureContent[0]);

            // Add select menus if present
            if (featureContent[1]) {
                container.addActionRowComponents(featureContent[1]);
            }
            if (featureContent[2]) {
                container.addActionRowComponents(featureContent[2]);
            }
        } else if (featureContent) {
            // Fallback for non-array content
            container.addTextDisplayComponents(featureContent);
        }

        container.setAccentColor(OPERATION_COLORS.ENTITY);

        // Check for item notifications if viewing inventory and notification center is enabled
        const components = [hubMenu, container];

        if (feature === 'inventory') {
            // Check user's notification center setting
            // OPTIMIZED: Check user's notification center setting using cached function
            let notificationCenterEnabled = true; // Default to enabled
            try {
                const userData = await getCachedUserSettings(user.id);
                notificationCenterEnabled = userData?.notificationCenterEnabled !== false; // Default to true unless explicitly disabled
            } catch (error) {
                console.error('[you] Error fetching user notification settings:', error);
            }

            if (notificationCenterEnabled) {
                const notificationResult = await this.buildItemNotificationDisplay(user.id, interaction.guild.id, interaction.client);
                if (notificationResult) {
                    components.push(notificationResult.container, notificationResult.buttonRow);
                }
            }
        }

        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: components
        });
    },

    // Enhanced leaderboard with type selection
    async showLeaderboard(interaction, user, leaderboardType = null, selectedItem = null) {
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'leaderboard');

        // If no type selected, show type selection menu
        if (!leaderboardType) {
            const typeSelect = new StringSelectMenuBuilder()
                .setCustomId('you-leaderboard-type-select')
                .setPlaceholder('select leaderboard type');

            typeSelect.addOptions([
                {
                    label: 'EXP Rankings',
                    description: 'view experience point leaderboards',
                    value: 'exp',
                    emoji: '⭐'
                },
                {
                    label: 'Item Records',
                    description: 'view item parameter leaderboards',
                    value: 'items',
                    emoji: '🏆'
                }
            ]);

            // Wrap select menu in ActionRowBuilder
            const typeSelectRow = new ActionRowBuilder().addComponents(typeSelect);

            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('## leaderboard\nselect a leaderboard type to view rankings'))
                .addActionRowComponents(typeSelectRow)
                .setAccentColor(OPERATION_COLORS.ENTITY);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });
            return;
        }

        // Handle EXP leaderboard (existing functionality)
        if (leaderboardType === 'exp') {
            const { getGuildRankings, getGlobalRankings } = require('../../utils/expRanking.js');

            // Get both guild and global data
            const [guildRank, globalRank, guildTop, globalTop] = await Promise.all([
                getUserGuildRank(interaction.guild.id, user.id),
                getUserGlobalRank(user.id),
                getGuildRankings(interaction.guild.id, 50), // Get more to find surrounding players
                getGlobalRankings(50) // Get more to find surrounding players
            ]);

            // Build guild leaderboard display
            const guildDisplay = await this.buildLeaderboardSection(
                '🏠',
                interaction.guild.name,
                guildTop,
                guildRank,
                user,
                interaction.client,
                false // not global
            );

            // Build global leaderboard display
            const globalDisplay = await this.buildLeaderboardSection(
                '🌍',
                'global',
                globalTop,
                globalRank,
                user,
                interaction.client,
                true // is global
            );

            const container = new ContainerBuilder()
                .addTextDisplayComponents(guildDisplay, globalDisplay)
                .setAccentColor(OPERATION_COLORS.ENTITY);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });
            return;
        }

        // Handle Items leaderboard
        if (leaderboardType === 'items') {
            await this.showItemLeaderboard(interaction, user, selectedItem);
            return;
        }
    },

    // Show item leaderboard with item selection
    async showItemLeaderboard(interaction, user, selectedItem = null) {
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'leaderboard');

        // If no item selected, show item selection menu
        if (!selectedItem) {
            try {
                // Get user's global inventory to show all items they've found
                const userItems = await getCachedUserGlobalInventory(user.id);

                if (userItems.length === 0) {
                    const container = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('## item leaderboard\n__you haven\'t found any items yet__\n\ntry chatting or joining voice channels to find items.'))
                        .setAccentColor(OPERATION_COLORS.ENTITY);

                    await interaction.editReply({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [hubMenu, container]
                    });
                    return;
                }

                // Get unique items the user has found
                const uniqueItems = [];
                const seenItems = new Set();

                for (const item of userItems) {
                    const itemKey = `${item.itemName}-${item.itemType}`;
                    if (!seenItems.has(itemKey)) {
                        seenItems.add(itemKey);
                        uniqueItems.push({
                            name: item.itemName,
                            type: item.itemType,
                            emote: item.itemEmote || '📦',
                            value: itemKey
                        });
                    }
                }

                // Ensure we have items to show
                if (uniqueItems.length === 0) {
                    const container = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('## item leaderboard\n__you haven\'t found any items with parameters yet__\n\ntry chatting or joining voice channels to find items.'))
                        .setAccentColor(OPERATION_COLORS.ENTITY);

                    await interaction.editReply({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [hubMenu, container]
                    });
                    return;
                }

                // Build item selection menu
                const itemSelect = new StringSelectMenuBuilder()
                    .setCustomId('you-leaderboard-item-select')
                    .setPlaceholder('select an item you\'ve found');

                // Add up to 25 items (Discord limit)
                uniqueItems.slice(0, 25).forEach(item => {
                    itemSelect.addOptions({
                        label: item.name,
                        description: `view ${item.name} parameter rankings`,
                        value: item.value,
                        emoji: item.emote
                    });
                });

                // Wrap select menu in ActionRowBuilder
                const selectRow = new ActionRowBuilder().addComponents(itemSelect);

                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent(`## item leaderboard\nselect an item to view parameter rankings\n\n**items found:** ${uniqueItems.length}`))
                    .addActionRowComponents(selectRow)
                    .setAccentColor(OPERATION_COLORS.ENTITY);

                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [hubMenu, container]
                });
                return;

            } catch (error) {
                console.error('[you] Error loading user items for leaderboard:', error);
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('## item leaderboard\nerror loading your items. please try again.'))
                    .setAccentColor(OPERATION_COLORS.ENTITY);

                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [hubMenu, container]
                });
                return;
            }
        }

        // Show rankings for selected item
        await this.showItemParameterRankings(interaction, user, selectedItem);
    },

    // Show parameter rankings for a specific item
    async showItemParameterRankings(interaction, user, itemKey) {
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'leaderboard');
        const [itemName, itemType] = itemKey.split('-');

        try {
            // Get user's best parameters for this item
            const userItems = await optimizedFind('user_inventory', {
                userId: user.id,
                guildId: interaction.guild.id,
                itemName: itemName,
                itemType: itemType
            });

            if (userItems.length === 0) {
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent(`## ${itemName} rankings\n__no items found__`))
                    .setAccentColor(OPERATION_COLORS.ENTITY);

                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [hubMenu, container]
                });
                return;
            }

            // Get user's best parameters and rankings
            const { getUserParameterRank } = require('../../utils/itemRecords.js');
            const parameterRankings = {};

            // Find user's best values for each parameter
            for (const item of userItems) {
                if (item.catchData) {
                    for (const [paramName, paramValue] of Object.entries(item.catchData)) {
                        if (!parameterRankings[paramName] || paramValue > parameterRankings[paramName].value) {
                            parameterRankings[paramName] = {
                                value: paramValue,
                                displayValue: paramValue
                            };
                        }
                    }
                }
            }

            // Get rankings for each parameter
            let content = `## ${itemName} rankings\n`;

            if (Object.keys(parameterRankings).length === 0) {
                content += 'no parameter data available for this item.';
            } else {
                for (const [paramName, paramData] of Object.entries(parameterRankings)) {
                    const guildRank = await getUserParameterRank('guild', interaction.guild.id, user.id, paramName, itemType);
                    const globalRank = await getUserParameterRank('global', null, user.id, paramName, itemType);

                    let rankText = '';
                    if (guildRank.rank === 1 && globalRank.rank === 1) {
                        rankText = ' (🏆 NEW RECORD!)';
                    } else if (guildRank.rank === 1) {
                        rankText = ` (🏆 server record, ${globalRank.rank}${this.getOrdinalSuffix(globalRank.rank)}/${globalRank.total} global)`;
                    } else if (globalRank.rank === 1) {
                        rankText = ` (${guildRank.rank}${this.getOrdinalSuffix(guildRank.rank)}/${guildRank.total} server, 🏆 global record)`;
                    } else {
                        rankText = ` (${guildRank.rank}${this.getOrdinalSuffix(guildRank.rank)}/${guildRank.total} server, ${globalRank.rank}${this.getOrdinalSuffix(globalRank.rank)}/${globalRank.total} global)`;
                    }

                    content += `**${paramName}:** ${paramData.displayValue}${rankText}\n`;
                }
            }

            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent(content))
                .setAccentColor(OPERATION_COLORS.ENTITY);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });

        } catch (error) {
            console.error('[you] Error showing item parameter rankings:', error);
            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent(`## ${itemName} rankings\nerror loading rankings. please try again.`))
                .setAccentColor(OPERATION_COLORS.ENTITY);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });
        }
    },

    // Helper function to get ordinal suffix (1st, 2nd, 3rd, 4th, etc.)
    getOrdinalSuffix(num) {
        const j = num % 10;
        const k = num % 100;
        if (j === 1 && k !== 11) return 'st';
        if (j === 2 && k !== 12) return 'nd';
        if (j === 3 && k !== 13) return 'rd';
        return 'th';
    },

    // Build leaderboard section with top 3 + surrounding players
    async buildLeaderboardSection(emoji, title, rankings, userRank, user, client, isGlobal = false) {
        let content = `**${emoji} ${title}**\n`;

        if (!userRank.rank) {
            content += `rank: unranked\nexp: 0\n\n`;
            return new TextDisplayBuilder().setContent(content);
        }

        content += `rank: #${userRank.rank} / ${userRank.total}\n`;
        content += `exp: ${(isGlobal ? userRank.exp : userRank.exp).toLocaleString()}${isGlobal ? ' *(normalized)*' : ''}\n\n`;

        // Show top 3 players
        const top3 = rankings.slice(0, 3);
        if (top3.length > 0) {
            content += `**top players:**\n`;
            for (let i = 0; i < top3.length; i++) {
                const player = top3[i];
                const expValue = isGlobal ? player.exp.normalized : player.exp.total;

                try {
                    const playerUser = await client.users.fetch(player.userId).catch(() => null);
                    const playerName = playerUser ? playerUser.username : 'Unknown User';
                    const medal = i === 0 ? '🥇' : i === 1 ? '🥈' : '🥉';
                    content += `${medal} **${playerName}** - ${expValue.toLocaleString()} exp\n`;
                } catch (error) {
                    content += `${i === 0 ? '🥇' : i === 1 ? '🥈' : '🥉'} Unknown User - ${expValue.toLocaleString()} exp\n`;
                }
            }
            content += '\n';
        }

        // Show surrounding players if user is not in top 3
        if (userRank.rank > 3) {
            content += `**around you:**\n`;

            // Find user's position in rankings array
            const userIndex = rankings.findIndex(player => player.userId === user.id);

            if (userIndex !== -1) {
                // Show player above (if exists)
                if (userIndex > 0) {
                    const above = rankings[userIndex - 1];
                    const expValue = isGlobal ? above.exp.normalized : above.exp.total;
                    try {
                        const aboveUser = await client.users.fetch(above.userId).catch(() => null);
                        const aboveName = aboveUser ? aboveUser.username : 'Unknown User';
                        content += `${above.rank}. **${aboveName}** - ${expValue.toLocaleString()} exp\n`;
                    } catch (error) {
                        content += `${above.rank}. Unknown User - ${expValue.toLocaleString()} exp\n`;
                    }
                }

                // Show current user
                const currentExpValue = isGlobal ? userRank.exp : userRank.exp;
                content += `**${userRank.rank}. ${user.username}** - ${currentExpValue.toLocaleString()} exp *(you)*\n`;

                // Show player below (if exists)
                if (userIndex < rankings.length - 1) {
                    const below = rankings[userIndex + 1];
                    const expValue = isGlobal ? below.exp.normalized : below.exp.total;
                    try {
                        const belowUser = await client.users.fetch(below.userId).catch(() => null);
                        const belowName = belowUser ? belowUser.username : 'Unknown User';
                        content += `${below.rank}. **${belowName}** - ${expValue.toLocaleString()} exp\n`;
                    } catch (error) {
                        content += `${below.rank}. Unknown User - ${expValue.toLocaleString()} exp\n`;
                    }
                }
            }
        }

        content += '\n';
        return new TextDisplayBuilder().setContent(content);
    },

    async showDaily(interaction, user) {
        const { buildStarfallContainer } = require('../../utils/starfall.js');

        const hubMenu = await buildYouHubMenu(user.username, user.id, 'daily', true);
        const starfallContainer = await buildStarfallContainer(user.id);

        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [hubMenu, starfallContainer]
        });
    },

    async showSettings(interaction, user) {
        const hubMenu = await buildYouHubMenu(user.username, user.id, 'settings', true);

        // Get user's current settings
        let userDMEnabled = false;
        let globalLevelDMEnabled = false;
        // OPTIMIZED: Check user's notification settings using cached function
        let notificationCenterEnabled = true; // Default to enabled
        try {
            const userData = await getCachedUserSettings(user.id);
            userDMEnabled = userData?.itemDMNotificationsEnabled === true;
            globalLevelDMEnabled = userData?.globalLevelDMNotificationsEnabled === true;
            notificationCenterEnabled = userData?.notificationCenterEnabled !== false; // Default to true unless explicitly disabled
        } catch (error) {
            console.error('[you] Error fetching user settings:', error);
        }

        // Build settings select menu
        const settingsSelect = new StringSelectMenuBuilder()
            .setCustomId('you-settings-select')
            .setPlaceholder('choose a setting to configure')
            .addOptions(
                {
                    label: userDMEnabled ? 'disable item dm notifications' : 'enable item dm notifications',
                    value: 'toggle-item-dm',
                    description: userDMEnabled ? 'Currently enabled' : 'Currently disabled',
                    emoji: '📬'
                },
                {
                    label: globalLevelDMEnabled ? 'disable global level dm notifications' : 'enable global level dm notifications',
                    value: 'toggle-global-level-dm',
                    description: globalLevelDMEnabled ? 'Currently enabled' : 'Currently disabled',
                    emoji: '🌟'
                },
                {
                    label: notificationCenterEnabled ? 'disable notification center' : 'enable notification center',
                    value: 'toggle-notification-center',
                    description: notificationCenterEnabled ? 'Currently enabled' : 'Currently disabled',
                    emoji: '🔔'
                }
            );

        const settingsMenu = new ActionRowBuilder().addComponents(settingsSelect);

        const settingsDisplay = new TextDisplayBuilder().setContent(
            `## settings\n\n` +
            `**item dm notifications:** ${userDMEnabled ? 'enabled' : 'disabled'}\n` +
            `**global level dm notifications:** ${globalLevelDMEnabled ? 'enabled' : 'disabled'}\n` +
            `**notification center:** ${notificationCenterEnabled ? 'enabled' : 'disabled'}`
        );

        const container = new ContainerBuilder()
            .addTextDisplayComponents(settingsDisplay)
            .addActionRowComponents(settingsMenu)
            .setAccentColor(OPERATION_COLORS.ENTITY);

        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [hubMenu, container]
        });
    },

    async handleSettingsSelect(interaction) {
        const selectedValue = interaction.values[0];
        const user = interaction.user;

        try {
            // OPTIMIZED: Get user data using cached function
            const userData = await getCachedUserSettings(user.id);

            if (selectedValue === 'toggle-item-dm') {
                // Toggle user's item DM notification setting
                const currentState = userData?.itemDMNotificationsEnabled === true;
                const newState = !currentState;

                await optimizedUpdateOne('users',
                    { id: user.id },
                    { $set: { itemDMNotificationsEnabled: newState } },
                    { upsert: true }
                );

                // OPTIMIZED: Invalidate user cache after update
                invalidateUserCaches(user.id);

                // Show updated settings page
                await this.showSettings(interaction, user);
            } else if (selectedValue === 'toggle-global-level-dm') {
                // Toggle user's global level DM notifications setting
                const currentState = userData?.globalLevelDMNotificationsEnabled === true;
                const newState = !currentState;

                await optimizedUpdateOne('users',
                    { id: user.id },
                    { $set: { globalLevelDMNotificationsEnabled: newState } },
                    { upsert: true }
                );

                // OPTIMIZED: Invalidate user cache after update
                invalidateUserCaches(user.id);

                // Show updated settings page
                await this.showSettings(interaction, user);
            } else if (selectedValue === 'toggle-notification-center') {
                // Toggle user's notification center setting
                const currentState = userData?.notificationCenterEnabled !== false; // Default to true
                const newState = !currentState;

                await optimizedUpdateOne('users',
                    { id: user.id },
                    { $set: { notificationCenterEnabled: newState } },
                    { upsert: true }
                );

                // OPTIMIZED: Invalidate user cache after update
                invalidateUserCaches(user.id);

                // Show updated settings page
                await this.showSettings(interaction, user);
            } else {
                // Unknown setting, just show settings page
                await this.showSettings(interaction, user);
            }
        } catch (error) {
            console.error('[you] Error updating settings:', error);

            // Rebuild settings view with status message instead of ephemeral reply
            const hubMenu = await buildYouHubMenu(user.username, user.id, 'settings');
            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('## settings\n\n*Loading your settings...*'))
                .setAccentColor(OPERATION_COLORS.ENTITY);

            // Add status message to container
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Error updating settings. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [hubMenu, container]
            });
        }
    },

    // Build voice stats content
    async buildVoiceStatsContent(interaction, user) {
        try {
            // OPTIMIZED: Get member voice stats using cached function
            const memberData = await getCachedMemberData(user.id, interaction.guild.id);

            const voiceStats = memberData?.exp?.voice || {};
            const totalExp = memberData?.exp?.total || 0;

            // Get rankings
            const [guildRank, globalRank] = await Promise.all([
                getVoiceExpGuildRank(interaction.guild.id, user.id),
                getVoiceExpGlobalRank(user.id)
            ]);

            // Format stats
            const voiceExp = voiceStats.total || 0;
            const timeSpent = formatDuration(voiceStats.timeSpent || 0);
            const longestSession = formatDuration(voiceStats.longestSession || 0);
            const sessionCount = voiceStats.sessionCount || 0;
            const currentStreak = voiceStats.currentStreak || 0;

            const guildRankText = guildRank.rank ? `#${guildRank.rank} / ${guildRank.total}` : 'unranked';
            const globalRankText = globalRank.rank ? `#${globalRank.rank} / ${globalRank.total}` : 'unranked';

            return new TextDisplayBuilder().setContent(
                `## voice exp stats\n\n` +
                `**total voice exp:** ${voiceExp.toLocaleString()} / ${totalExp.toLocaleString()}\n` +
                `**time spent:** ${timeSpent}\n` +
                `**longest session:** ${longestSession}\n` +
                `**session count:** ${sessionCount.toLocaleString()}\n` +
                `**current streak:** ${currentStreak} day${currentStreak !== 1 ? 's' : ''}\n` +
                `**guild rank:** ${guildRankText}\n` +
                `**global rank:** ${globalRankText}`
            );
        } catch (error) {
            console.error('[you] Error building voice stats:', error);
            return new TextDisplayBuilder().setContent(
                `## voice exp stats\n\n` +
                `❌ Error loading voice stats. Please try again later.`
            );
        }
    },

    // Build text stats content
    async buildTextStatsContent(interaction, user) {
        try {
            // OPTIMIZED: Get member text stats using cached function
            const memberData = await getCachedMemberData(user.id, interaction.guild.id);

            const textStats = memberData?.exp?.text || {};
            const totalExp = memberData?.exp?.total || 0;

            // Get rankings
            const [guildRank, globalRank] = await Promise.all([
                getTextExpGuildRank(interaction.guild.id, user.id),
                getTextExpGlobalRank(user.id)
            ]);

            // Calculate average per day
            const avgPerDay = calculateAvgPerDay(textStats.messagesCounted, textStats.lastActiveDay);

            // Format stats
            const textExp = textStats.total || 0;
            const messagesSent = textStats.messagesSent || 0;
            const messagesCounted = textStats.messagesCounted || 0;
            const currentStreak = textStats.currentStreak || 0;

            const guildRankText = guildRank.rank ? `#${guildRank.rank} / ${guildRank.total}` : 'unranked';
            const globalRankText = globalRank.rank ? `#${globalRank.rank} / ${globalRank.total}` : 'unranked';

            return new TextDisplayBuilder().setContent(
                `## text exp stats\n\n` +
                `**total text exp:** ${textExp.toLocaleString()} / ${totalExp.toLocaleString()}\n` +
                `**messages sent:** ${messagesSent.toLocaleString()}\n` +
                `**messages counted:** ${messagesCounted.toLocaleString()}\n` +
                `**avg per day:** ${avgPerDay}\n` +
                `**current streak:** ${currentStreak} day${currentStreak !== 1 ? 's' : ''}\n` +
                `**guild rank:** ${guildRankText}\n` +
                `**global rank:** ${globalRankText}`
            );
        } catch (error) {
            console.error('[you] Error building text stats:', error);
            return new TextDisplayBuilder().setContent(
                `## text exp stats\n\n` +
                `❌ Error loading text stats. Please try again later.`
            );
        }
    },

    // Utility function to format emojis in rows with specified limit per row
    formatEmojisInRows(emojis, itemsPerRow = 10, separator = '') {
        if (emojis.length === 0) return '';

        const rows = [];
        for (let i = 0; i < emojis.length; i += itemsPerRow) {
            const row = emojis.slice(i, i + itemsPerRow).join(separator);
            rows.push(row);
        }

        return rows.join('\n');
    },

    // Helper function to convert numbers to Unicode subscripts
    numberToSubscript(num) {
        const subscriptMap = {
            '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
            '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉'
        };
        return num.toString().split('').map(digit => subscriptMap[digit] || digit).join('');
    },

    // Inventory selection state management (using cache like items system)
    inventoryStateCache: new Map(),

    storeInventoryState(userId, guildId, state) {
        const cacheKey = `${userId}-${guildId}`;
        // Update cache immediately for instant UI updates
        this.inventoryStateCache.set(cacheKey, { ...state, updatedAt: new Date() });

        // Update database asynchronously (don't await to improve performance)
        setImmediate(async () => {
            try {
                await optimizedUpdateOne('inventory_selection_state',
                    { userId, guildId },
                    { $set: { ...state, updatedAt: new Date() } },
                    { upsert: true }
                );
            } catch (error) {
                console.error('Error storing inventory state:', error);
            }
        });
    },

    getInventoryState(userId, guildId) {
        const cacheKey = `${userId}-${guildId}`;

        // Check cache first
        if (this.inventoryStateCache.has(cacheKey)) {
            return this.inventoryStateCache.get(cacheKey);
        }

        // Return default state if not in cache
        return { selectedType: null };
    },

    clearInventoryState(userId, guildId) {
        const cacheKey = `${userId}-${guildId}`;
        this.inventoryStateCache.delete(cacheKey);

        // Clear from database asynchronously
        setImmediate(async () => {
            try {
                await optimizedDeleteOne('inventory_selection_state', { userId, guildId });
            } catch (error) {
                console.error('Error clearing inventory state:', error);
            }
        });
    },

    // Cache for inventory previews (10 minute cache - very aggressive)
    inventoryPreviewCache: new Map(),

    // Build global inventory preview for main page (heavily cached for performance)
    async buildGlobalInventoryPreview(userId) {
        const cacheKey = `${userId}_global`;
        const now = Date.now();

        // Check cache first (10 minute expiry - very aggressive)
        if (this.inventoryPreviewCache.has(cacheKey)) {
            const cached = this.inventoryPreviewCache.get(cacheKey);
            if (now - cached.timestamp < 600000) { // 10 minutes
                return cached.data;
            }
        }

        try {
            const inventory = await getCachedUserGlobalInventory(userId);

            let result;
            if (inventory.length === 0) {
                result = `## global inventory\n*No items yet - gain EXP to find items!*`;
            } else {
                // Fast processing - just count emotes without complex sorting
                const itemCounts = new Map();
                inventory.forEach(item => {
                    const emote = item.itemEmote || '📦';
                    itemCounts.set(emote, (itemCounts.get(emote) || 0) + 1);
                });

                // Build display efficiently
                const emoteStrings = [];
                for (const [emote, count] of itemCounts) {
                    const subscriptCount = this.numberToSubscript(count);
                    emoteStrings.push(`${emote}${subscriptCount}`);
                }

                // Limit to first 10 items for preview
                const limitedEmotes = emoteStrings.slice(0, 10);
                result = `## global inventory\n${limitedEmotes.join(' ')}`;
            }

            // Cache the result
            this.inventoryPreviewCache.set(cacheKey, {
                data: result,
                timestamp: now
            });

            return result;
        } catch (error) {
            console.error('[you] Error building global inventory preview:', error);
            return `## global inventory\n*Error loading inventory*`;
        }
    },

    // Build inventory preview for main page (heavily cached for performance)
    async buildInventoryPreview(userId, guildId) {
        const cacheKey = userId;
        const now = Date.now();

        // Check cache first (10 minute expiry - very aggressive)
        if (this.inventoryPreviewCache.has(cacheKey)) {
            const cached = this.inventoryPreviewCache.get(cacheKey);
            if (now - cached.timestamp < 600000) { // 10 minutes
                return cached.data;
            }
        }

        try {
            const inventory = await getCachedUserGlobalInventory(userId);

            let result;
            if (inventory.length === 0) {
                result = `## inventory\n*No items yet - gain EXP to find items!*`;
            } else {
                // Fast processing - just count emotes without complex sorting
                const itemCounts = new Map();
                inventory.forEach(item => {
                    const emote = item.itemEmote || '📦';
                    itemCounts.set(emote, (itemCounts.get(emote) || 0) + 1);
                });

                // Build display efficiently
                const emoteStrings = [];
                for (const [emote, count] of itemCounts) {
                    const subscriptCount = this.numberToSubscript(count);
                    emoteStrings.push(`${emote}${subscriptCount}`);
                }

                // Limit to first 10 items for preview
                const limitedEmotes = emoteStrings.slice(0, 10);
                result = `## inventory\n${limitedEmotes.join(' ')}`;
            }

            // Cache the result
            this.inventoryPreviewCache.set(cacheKey, {
                data: result,
                timestamp: now
            });

            return result;

        } catch (error) {
            console.error('Error building inventory preview:', error);
            return `## inventory\n*Error loading inventory*`;
        }
    },

    // Helper function to get guilds a user is in
    async getUserGuilds(userId) {
        try {
            // Get all guilds from the client and filter by user membership
            const { client } = require('../../index.js');
            const userGuilds = [];

            for (const guild of client.guilds.cache.values()) {
                try {
                    const member = await guild.members.fetch(userId).catch(() => null);
                    if (member) {
                        userGuilds.push({ id: guild.id, name: guild.name });
                    }
                } catch (error) {
                    // User not in this guild, continue
                }
            }

            return userGuilds;
        } catch (error) {
            console.error('Error getting user guilds:', error);
            return [];
        }
    },

    // Cache for accessible items (30 minute cache - very aggressive)
    accessibleItemsCache: new Map(),

    // Helper function to get accessible items count per drop location for a user (heavily cached)
    async getAccessibleItemsPerLocation(userId) {
        const cacheKey = userId;
        const now = Date.now();

        // Check cache first (30 minute expiry - very aggressive)
        if (this.accessibleItemsCache.has(cacheKey)) {
            const cached = this.accessibleItemsCache.get(cacheKey);
            if (now - cached.timestamp < 1800000) { // 30 minutes
                return cached.data;
            }
        }

        try {
            const { mongoClient } = require('../../mongo/client.js');
            const { getDroppableItems } = require('../../utils/itemDrops.js');

            // Get all guilds the user is in
            const userGuilds = await this.getUserGuilds(userId);
            const guildIds = userGuilds.map(guild => guild.id);

            const locationCounts = {};
            const { DROP_LOCATIONS } = require('./items.js');

            // For each drop location, calculate accessible items
            for (const [locationKey, locationData] of Object.entries(DROP_LOCATIONS)) {
                let totalAccessible = 0;

                // Get global items for this location
                const globalItems = await getDroppableItems(locationKey, null);
                totalAccessible += globalItems.length;

                // Get guild-specific items for each guild the user is in
                for (const guildId of guildIds) {
                    const guildItems = await getDroppableItems(locationKey, guildId);
                    // Only count guild-specific items (not global ones)
                    const guildSpecificItems = guildItems.filter(item => item.guildId === guildId);
                    totalAccessible += guildSpecificItems.length;
                }

                locationCounts[locationKey] = totalAccessible;
            }

            // Cache the result
            this.accessibleItemsCache.set(cacheKey, {
                data: locationCounts,
                timestamp: now
            });

            return locationCounts;
        } catch (error) {
            console.error('Error getting accessible items per location:', error);
            return {};
        }
    },

    // OPTIMIZATION METHODS - New optimized database access methods

    /**
     * Get guild EXP data with optimized single query
     * @param {string} userId - User ID
     * @param {string} guildId - Guild ID
     * @returns {Object} Guild EXP data
     */
    async getOptimizedGuildExpData(userId, guildId) {
        try {
            const { mongoClient } = require('../../mongo/client.js');

            // OPTIMIZATION: Single aggregation query instead of separate queries
            const pipeline = [
                {
                    $facet: {
                        memberData: [
                            { $match: { userId: userId, guildId: guildId } },
                            { $project: { exp: 1 } },
                            { $limit: 1 }
                        ],
                        guildData: [
                            { $match: { id: guildId } },
                            { $project: { exp: 1 } },
                            { $limit: 1 }
                        ]
                    }
                }
            ];

            const result = await optimizedAggregate('member', pipeline);
            const [aggregateResult] = result;

            const memberData = aggregateResult.memberData[0];

            // OPTIMIZED: Get guild data using cached function
            const guildData = await getCachedGuildConfig(guildId);

            let guildExp = 0, guildLevel = 0, guildNextLevelExp = null;
            let guildRole = null, guildRoleIcon = '';

            if (memberData?.exp?.total !== undefined && guildData?.exp?.levels) {
                guildExp = memberData.exp.total;

                // Calculate level from EXP (cached)
                const { getCachedLevelCalculation } = require('../../utils/expCache.js');
                const levels = guildData.exp.levels;
                const levelCalc = getCachedLevelCalculation(guildExp, levels);

                guildLevel = levelCalc.currentLevel;
                guildNextLevelExp = levelCalc.nextLevelExp;

                if (levelCalc.levelIndex >= 0) {
                    const currentLevelData = levels[levelCalc.levelIndex];
                    guildRole = currentLevelData.roleId ? `<@&${currentLevelData.roleId}>` : null;
                    guildRoleIcon = currentLevelData.levelIcon || '🌱';
                }
            }

            return { guildExp, guildLevel, guildNextLevelExp, guildRole, guildRoleIcon };

        } catch (error) {
            console.error('[you] Error in getOptimizedGuildExpData:', error);
            return { guildExp: 0, guildLevel: 0, guildNextLevelExp: null, guildRole: null, guildRoleIcon: '' };
        }
    },

    /**
     * Get global EXP data with optimized caching
     * @param {string} userId - User ID
     * @returns {Object} Global EXP data
     */
    async getOptimizedGlobalExpData(userId) {
        try {
            // OPTIMIZATION: Use cached functions that are already optimized
            const { getCachedGlobalUser, getCachedGlobalLevels, calculateGlobalLevel } = require('../../utils/globalLevels.js');

            // These are already cached and optimized
            const [globalUserData, globalLevels] = await Promise.all([
                getCachedGlobalUser(userId),
                getCachedGlobalLevels()
            ]);

            const globalExp = globalUserData.globalExp;
            const levelCalc = calculateGlobalLevel(globalExp, globalUserData.prestigeLevel, globalLevels);
            const globalLevel = levelCalc.currentLevel;
            const globalNextLevelExp = levelCalc.nextLevelExp;

            let globalLevelName = 'Beginner';
            let globalLevelIcon = '🌱';

            if (levelCalc.levelIndex >= 0 && globalLevels[levelCalc.levelIndex]) {
                const levelData = globalLevels[levelCalc.levelIndex];
                globalLevelName = levelData.name;
                globalLevelIcon = levelData.levelIcon || '🌱';
            } else {
                // User is at level 0 - look for Level 0 definition
                const level0 = globalLevels.find(level => level.level === 0);
                if (level0) {
                    globalLevelName = level0.name;
                    globalLevelIcon = level0.levelIcon || '🌱';
                }
            }

            return { globalExp, globalLevel, globalNextLevelExp, globalLevelName, globalLevelIcon, prestigeLevel: globalUserData.prestigeLevel || 0 };

        } catch (error) {
            console.error('[you] Error in getOptimizedGlobalExpData:', error);
            return { globalExp: 0, globalLevel: 0, globalNextLevelExp: null, globalLevelName: 'Beginner', globalLevelIcon: '🌱', prestigeLevel: 0 };
        }
    },

    /**
     * Build optimized inventory content with minimal processing
     * @param {string} userId - User ID
     * @returns {Array} Inventory content components
     */
    async buildOptimizedInventoryContent(userId) {
        try {
            // OPTIMIZATION: Use cached inventory function
            const { getCachedUserGlobalInventory } = require('../../utils/itemCache.js');
            const inventory = await getCachedUserGlobalInventory(userId);

            if (inventory.length === 0) {
                const emptyContent = new TextDisplayBuilder().setContent(
                    `## inventory\n\n*Your inventory is empty!*\n\n` +
                    `**How to get items:**\n` +
                    `\\- Gain EXP from text messages\n` +
                    `\\- Gain EXP from voice activity\n` +
                    `\\- Items drop randomly based on rarity`
                );
                return [emptyContent];
            }

            // OPTIMIZATION: Skip expensive sorting and processing for main page
            // Just show basic inventory info
            const content = new TextDisplayBuilder().setContent(
                `## inventory\n\n**total items:** ${inventory.length} (across all servers)\n\n` +
                `*Use the inventory feature menu to view detailed items*`
            );

            return [content];

        } catch (error) {
            console.error('[you] Error in buildOptimizedInventoryContent:', error);
            const errorContent = new TextDisplayBuilder().setContent('## inventory\n*Error loading inventory*');
            return [errorContent];
        }
    },

    // Build inventory content (optimized for speed)
    async buildInventoryContent(interaction, user) {
        try {
            const { RARITIES, DROP_LOCATIONS } = require('./items.js');
            const inventory = await getCachedUserGlobalInventory(user.id);

            // Item notifications are now shown on the main page, not in inventory

            if (inventory.length === 0) {
                const emptyContent =
                    `## inventory\n\n` +
                    `*Your inventory is empty!*\n\n` +
                    `**How to get items:**\n` +
                    `\\- Gain EXP from text messages\n` +
                    `\\- Gain EXP from voice activity\n` +
                    `\\- Items drop randomly based on rarity`;

                const emptyInventoryContent = new TextDisplayBuilder().setContent(emptyContent);
                return [emptyInventoryContent];
            }

            // Skip expensive sorting - inventory is already sorted from cache
            const sortedInventory = inventory;

            // Group items by drop location
            const itemsByLocation = {};
            sortedInventory.forEach(item => {
                const location = item.droppedFrom || 'UNKNOWN';
                if (!itemsByLocation[location]) {
                    itemsByLocation[location] = [];
                }
                itemsByLocation[location].push(item);
            });

            // Skip expensive accessible items calculation - just show what user has
            const accessibleCounts = {};

            // Simple fallback: estimate based on what user has
            Object.keys(itemsByLocation).forEach(locationKey => {
                const locationItems = itemsByLocation[locationKey] || [];
                if (locationItems.length > 0) {
                    const uniqueItemTypes = new Set(locationItems.map(item => `${item.itemName}_${item.itemType}`));
                    // Show owned count as both owned and total (no confusing 3/0)
                    accessibleCounts[locationKey] = uniqueItemTypes.size;
                }
            });

            let content = `## inventory\n\n**total items:** ${inventory.length} (across all servers)\n\n`;

            // Display items by location with proper counts
            for (const [locationKey, locationData] of Object.entries(DROP_LOCATIONS)) {
                const locationItems = itemsByLocation[locationKey] || [];
                const accessibleCount = accessibleCounts[locationKey] || 0;

                // Only show locations that have items or accessible items
                if (locationItems.length > 0 || accessibleCount > 0) {
                    const locationName = locationData.displayName || locationData.name;
                    const uniqueItemTypes = new Set(locationItems.map(item => `${item.itemName}_${item.itemType}`));

                    content += `**${locationName}** (${uniqueItemTypes.size}/${accessibleCount}): `;

                    if (locationItems.length > 0) {
                        // Simple emote counting
                        const itemCounts = new Map();
                        locationItems.forEach(item => {
                            const emote = item.itemEmote || '📦';
                            itemCounts.set(emote, (itemCounts.get(emote) || 0) + 1);
                        });

                        const emoteStrings = [];
                        for (const [emote, count] of itemCounts) {
                            const subscriptCount = this.numberToSubscript(count);
                            emoteStrings.push(`${emote}${subscriptCount}`);
                        }

                        content += emoteStrings.join(' ');
                    } else {
                        content += '*none*';
                    }

                    content += '\n';
                }
            }

            content += '\n';

            // Build select menus for functionality
            const { StringSelectMenuBuilder, ActionRowBuilder } = require('discord.js');
            const inventoryContent = new TextDisplayBuilder().setContent(content);
            const result = [inventoryContent];

            // Get current inventory selection state
            const inventoryState = this.getInventoryState(user.id, interaction.guild.id);

            // Add type select menu
            const availableTypes = new Set();
            sortedInventory.forEach(item => {
                if (item.droppedFrom) {
                    availableTypes.add(item.droppedFrom);
                }
            });

            // Auto-select first available type if none is selected
            if (availableTypes.size > 0 && !inventoryState.selectedType) {
                const firstType = Array.from(availableTypes)[0];
                inventoryState.selectedType = firstType;
                this.storeInventoryState(user.id, interaction.guild.id, inventoryState);
            }

            if (availableTypes.size > 0) {
                const typeSelect = new StringSelectMenuBuilder()
                    .setCustomId('you-inventory-type-select')
                    .setPlaceholder(inventoryState.selectedType ?
                        DROP_LOCATIONS[inventoryState.selectedType]?.displayName || 'select type' :
                        'select type');

                for (const [locationKey, locationData] of Object.entries(DROP_LOCATIONS)) {
                    if (availableTypes.has(locationKey)) {
                        typeSelect.addOptions({
                            label: locationData.displayName || locationData.name,
                            value: locationKey,
                            description: locationData.description,
                            emoji: locationData.emote,
                            default: locationKey === inventoryState.selectedType
                        });
                    }
                }

                const typeSelectRow = new ActionRowBuilder().addComponents(typeSelect);
                result.push(typeSelectRow);

                // Add item select menu if type is selected
                if (inventoryState.selectedType) {
                    const filteredItems = sortedInventory.filter(item => item.droppedFrom === inventoryState.selectedType);

                    if (filteredItems.length > 0) {
                        const itemSelect = new StringSelectMenuBuilder()
                            .setCustomId('you-inventory-item-select')
                            .setPlaceholder('select an item to view details');

                        const selectMenuItems = filteredItems.slice(0, 25);
                        const options = [];

                        selectMenuItems.forEach((item) => {
                            const rarity = RARITIES[item.itemRarity];
                            const rarityName = rarity?.name || item.itemRarity;

                            let description = `${rarityName}`;
                            if (item.catchData && Object.keys(item.catchData).length > 0) {
                                const params = Object.entries(item.catchData)
                                    .slice(0, 2)
                                    .map(([key, value]) => `${key}: ${value}`)
                                    .join(', ');
                                description += ` - ${params}`;
                            }

                            const option = {
                                label: item.itemName,
                                value: `${item._id}`,
                                description: description.slice(0, 100),
                                emoji: item.itemEmote || '📦'
                            };

                            // Mark as default if this is the selected item
                            if (inventoryState.selectedItem && item._id.toString() === inventoryState.selectedItem) {
                                option.default = true;
                            }

                            options.push(option);
                        });

                        itemSelect.setOptions(options);

                        const itemSelectRow = new ActionRowBuilder().addComponents(itemSelect);
                        result.push(itemSelectRow);
                    }
                }
            }

            return result;

        } catch (error) {
            console.error('Error building inventory content:', error);
            const errorContent = new TextDisplayBuilder().setContent(
                `## inventory\n\n*Error loading inventory*\n\nPlease try again later.`
            );
            return [errorContent];
        }
    },

    // Build inventory content with a selected item as default in select menu (global inventory)
    async buildInventoryContentWithSelection(interaction, user, selectedItemId) {
        try {
            const { RARITIES, DROP_LOCATIONS } = require('./items.js');
            const inventory = await getCachedUserGlobalInventory(user.id);

            if (inventory.length === 0) {
                const inventoryContent = new TextDisplayBuilder().setContent(
                    `## inventory\n\n*Your inventory is empty!*\n\n` +
                    `*No items yet - gain EXP to find items!*`
                );
                return [inventoryContent];
            }

            // Group items by rarity for better display
            const itemsByRarity = {};
            inventory.forEach(item => {
                if (!itemsByRarity[item.itemRarity]) {
                    itemsByRarity[item.itemRarity] = [];
                }
                itemsByRarity[item.itemRarity].push(item);
            });

            let content = `## inventory\n\n**total items:** ${inventory.length} (across all servers)\n\n`;

            // Build item select menu for detailed view with selected item as default
            const { StringSelectMenuBuilder, ActionRowBuilder } = require('discord.js');
            const itemSelect = new StringSelectMenuBuilder()
                .setCustomId('you-inventory-item-select')
                .setPlaceholder('select an item to view details');

            // Sort all items by rarity and add to select menu (limit to 25 for Discord)
            const rarityOrder = ['UNKNOWN', 'GALACTIC', 'MYTHICAL', 'RARE', 'UNCOMMON', 'COMMON'];
            const sortedInventory = inventory.sort((a, b) => {
                const aIndex = rarityOrder.indexOf(a.itemRarity);
                const bIndex = rarityOrder.indexOf(b.itemRarity);
                return aIndex - bIndex; // Lower index = rarer
            });

            // Add items to select menu (up to 25) with selected item as default
            const selectMenuItems = sortedInventory.slice(0, 25);
            let selectedItemFound = false;

            selectMenuItems.forEach((item) => {
                const rarity = RARITIES[item.itemRarity];
                const rarityName = rarity?.name || item.itemRarity;

                // Build description with parameters
                let description = `${rarityName}`;
                if (item.catchData && Object.keys(item.catchData).length > 0) {
                    const params = Object.entries(item.catchData)
                        .slice(0, 2) // Show first 2 parameters to fit in description
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(', ');
                    description += ` - ${params}`;
                }

                const option = {
                    label: item.itemName,
                    value: `${item._id}`, // Use MongoDB _id as unique identifier
                    description: description.slice(0, 100), // Discord limit
                    emoji: item.itemEmote || '📦'
                };

                // Set as default if this is the selected item
                if (item._id.toString() === selectedItemId) {
                    option.default = true;
                    selectedItemFound = true;
                }

                itemSelect.addOptions(option);
            });

            // If selected item wasn't found in the first 25, don't set any default
            const itemSelectRow = new ActionRowBuilder().addComponents(itemSelect);

            // Display items by rarity (highest to lowest)
            for (const rarityKey of rarityOrder) {
                const items = itemsByRarity[rarityKey];
                if (!items || items.length === 0) continue;

                const rarity = RARITIES[rarityKey];
                const rarityEmote = rarity?.emote || '⚪';

                // Show emoji first, then rarity name
                content += `${rarityEmote} **${rarity?.name || rarityKey}**\n`;

                // Show all item emojis with 10 per row limit and spaces between items
                const itemEmotes = items.map(item => item.itemEmote || '📦');
                const formattedEmotes = this.formatEmojisInRows(itemEmotes, 10, ' ');
                content += `${formattedEmotes}\n\n`;
            }

            // Return array format directly without container conversion
            const inventoryContent = new TextDisplayBuilder().setContent(content);
            const result = [inventoryContent];

            // Just return the content for now - this function needs to be rewritten
            return result;

            // Add item select menu if type is selected
            if (inventoryState.selectedType) {
                const filteredItems = sortedInventory.filter(item => item.droppedFrom === inventoryState.selectedType);

                if (filteredItems.length > 0) {
                    const itemSelect = new StringSelectMenuBuilder()
                        .setCustomId('you-inventory-item-select')
                        .setPlaceholder('select an item to view details');

                    const selectMenuItems = filteredItems.slice(0, 25);
                    selectMenuItems.forEach((item, index) => {
                        const rarity = RARITIES[item.itemRarity];
                        const rarityName = rarity?.name || item.itemRarity;

                        let description = `${rarityName}`;
                        if (item.catchData && Object.keys(item.catchData).length > 0) {
                            const params = Object.entries(item.catchData)
                                .slice(0, 2)
                                .map(([key, value]) => `${key}: ${value}`)
                                .join(', ');
                            description += ` - ${params}`;
                        }

                        itemSelect.addOptions({
                            label: item.itemName,
                            value: `${item._id}`,
                            description: description.slice(0, 100),
                            emoji: item.itemEmote || '📦'
                        });
                    });

                    const itemSelectRow = new ActionRowBuilder().addComponents(itemSelect);
                    result.push(itemSelectRow);
                }
            }

            return result;

        } catch (error) {
            console.error('Error building inventory content with selection:', error);
            const errorContent = new TextDisplayBuilder().setContent(
                `## inventory\n\n*Error loading inventory*\n\nPlease try again later.`
            );
            return errorContent;
        }
    },

    // Build item detail display for inventory item selection (without dismiss button)
    async buildItemDetailDisplay(itemId, userId, guildId, client) {
        try {
            const { buildDynamicFoundItemContainer } = require('./items.js');
            const inventory = await getCachedUserGlobalInventory(userId);

            // Find the specific item by ID
            const selectedItem = inventory.find(item => item._id.toString() === itemId);
            if (!selectedItem) {
                return null;
            }

            // Calculate leaderboard results on-demand since they're not stored on inventory items
            let enhancedLeaderboardResults = {
                guildRecords: [],
                globalRecords: [],
                guildRanks: {},
                globalRanks: {}
            };

            // If item has parameters, calculate current rankings
            if (selectedItem.catchData && Object.keys(selectedItem.catchData).length > 0) {
                try {
                    const { getUserParameterRank } = require('../../utils/itemRecords.js');

                    // Calculate rankings for each parameter of THIS SPECIFIC ITEM
                    for (const [paramName, paramValue] of Object.entries(selectedItem.catchData)) {
                        // Skip condition parameter for rankings (usually not numeric)
                        if (paramName === 'condition') continue;

                        // Get rankings for this specific item's parameter value
                        const [guildRank, globalRank] = await Promise.all([
                            this.getSpecificItemParameterRank('guild', guildId, paramName, selectedItem.itemType, paramValue),
                            this.getSpecificItemParameterRank('global', null, paramName, selectedItem.itemType, paramValue)
                        ]);

                        if (guildRank.rank > 0) {
                            enhancedLeaderboardResults.guildRanks[paramName] = guildRank;
                        }
                        if (globalRank.rank > 0) {
                            enhancedLeaderboardResults.globalRanks[paramName] = globalRank;
                        }
                    }

                    // Calculate discovery ranking (what order this item was found in)
                    const discoveryRank = await this.calculateDiscoveryRank(selectedItem, guildId);
                    if (discoveryRank.guildRank > 0) {
                        enhancedLeaderboardResults.guildRanks['_item_discovery'] = {
                            rank: discoveryRank.guildRank,
                            total: discoveryRank.guildTotal,
                            discoveryRank: discoveryRank.guildRank
                        };
                    }
                    if (discoveryRank.globalRank > 0) {
                        enhancedLeaderboardResults.globalRanks['_item_discovery'] = {
                            rank: discoveryRank.globalRank,
                            total: discoveryRank.globalTotal,
                            discoveryRank: discoveryRank.globalRank
                        };
                    }

                    console.log(`[you] Calculated rankings for ${selectedItem.itemName}:`, enhancedLeaderboardResults);
                } catch (error) {
                    console.error('Error calculating rankings:', error);
                }
            }

            if (selectedItem.catchData && Object.keys(selectedItem.catchData).length > 0) {
                // Get all items of the same type for this user to check personal bests
                const userItemsOfType = inventory.filter(item =>
                    item.itemName === selectedItem.itemName &&
                    item.itemType === selectedItem.itemType
                );

                // Check each parameter to see if this is the user's personal best
                const personalBests = {};
                for (const [paramName, paramValue] of Object.entries(selectedItem.catchData)) {
                    // Skip condition parameter for personal best checking (since it's usually the same for all items)
                    if (paramName === 'condition') {
                        personalBests[paramName] = false; // Don't show personal best for condition
                        continue;
                    }

                    // Find the best value this user has for this parameter
                    let isPersonalBest = true;

                    for (const userItem of userItemsOfType) {
                        if (userItem.catchData && userItem.catchData[paramName] !== undefined) {
                            const otherValue = userItem.catchData[paramName];

                            // Convert both values to numbers for comparison if they're strings
                            const currentNum = typeof paramValue === 'string' ? parseFloat(paramValue) : paramValue;
                            const otherNum = typeof otherValue === 'string' ? parseFloat(otherValue) : otherValue;

                            if (otherNum > currentNum) {
                                isPersonalBest = false;
                                break;
                            }
                        }
                    }

                    personalBests[paramName] = isPersonalBest;
                }

                // Add personal best indicators to the leaderboard results
                enhancedLeaderboardResults.personalBests = personalBests;
            }

            // Get the original item data to get the guildId
            const originalItem = await optimizedFindOne('custom_items', { id: selectedItem.itemId });

            // Create item data structure for the container
            const itemData = {
                name: selectedItem.itemName,
                type: selectedItem.itemType,
                rarity: selectedItem.itemRarity,
                emote: selectedItem.itemEmote,
                description: selectedItem.itemDescription,
                guildId: originalItem?.guildId // Add the guildId from original item
            };

            // Add context information including where the item was found
            const { DROP_LOCATIONS } = require('./items.js');
            const locationInfo = DROP_LOCATIONS[selectedItem.droppedFrom];
            const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : selectedItem.droppedFrom;

            // Get guild name for the server where this item was found
            let guildName = 'Unknown Server';
            try {
                const guild = await client.guilds.fetch(selectedItem.guildId).catch(() => null);
                if (guild) {
                    guildName = guild.name;
                }
            } catch (error) {
                console.error('Error fetching guild for inventory item:', error);
            }

            // Add timestamp, userId, guildId, and found context for personal record checking and live totals
            const context = {
                timestamp: selectedItem.droppedAt,
                userId: userId,
                guildId: guildId,
                // Add found by information for inventory display (no user since it's their own inventory)
                server: guildName,
                location: locationText
            };

            // Build the item container using the DYNAMIC version with enhanced leaderboard results and live totals
            const itemContainerResult = await buildDynamicFoundItemContainer(itemData, selectedItem.catchData || {}, context, enhancedLeaderboardResults);

            // buildDynamicFoundItemContainer returns just the container (not an array like server version)
            return itemContainerResult;

        } catch (error) {
            console.error('Error building item detail display:', error);
            return null;
        }
    },

    // Build item notification display for inventory view (optimized)
    async buildItemNotificationDisplay(userId, guildId, client) {
        try {
            const { getUserItemNotifications } = require('../../utils/itemDrops.js');
            const { RARITIES } = require('./items.js');

            // Use optimized notification query with limit (max 1 for display)
            const notifications = await getUserItemNotifications(userId, guildId, 1);

            if (notifications.length === 0) {
                return null; // No notifications to display
            }

            // Get the first notification to display
            const notification = notifications[0];
            const items = notification.items;

            // Build item display
            const itemsText = items.map(item => {
                const rarity = RARITIES[item.itemRarity];
                const rarityText = rarity ? rarity.name : 'Unknown';
                return `${item.itemEmote} **${item.itemName}** (${rarityText})`;
            }).join('\n');

            // Use the DM version of the found item container (no context text needed)
            const { buildFoundItemContainer } = require('./items.js');

            // Get the first item to display (for now, just show one item per notification)
            const firstItem = items[0];

            // Get the original item data with projection for performance
            const originalItem = await optimizedFindOne('custom_items',
                { id: firstItem.itemId },
                { projection: { guildId: 1 } } // Only fetch guildId field
            );

            // Create item data structure
            const itemData = {
                name: firstItem.itemName,
                type: firstItem.itemType,
                rarity: firstItem.itemRarity,
                emote: firstItem.itemEmote,
                description: firstItem.itemDescription,
                guildId: originalItem?.guildId // Add the guildId from original item
            };

            // Add context information including where the item was found
            const { DROP_LOCATIONS } = require('./items.js');
            const locationInfo = DROP_LOCATIONS[notification.location];
            const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : notification.location;

            // Get guild name for the server where this item was found
            let guildName = 'Unknown Server';
            try {
                const guild = await client.guilds.fetch(firstItem.guildId || guildId).catch(() => null);
                if (guild) {
                    guildName = guild.name;
                }
            } catch (error) {
                console.error('Error fetching guild for notification item:', error);
            }

            // Add timestamp, userId, and found context for personal record checking
            const context = {
                timestamp: notification.createdAt,
                userId: userId,
                // Add found by information for notification display
                server: guildName,
                location: locationText
            };

            // Build the item container using the unified version with leaderboard results
            const { buildUnifiedItemDisplayContainer } = require('./items.js');
            const itemContainer = await buildUnifiedItemDisplayContainer(itemData, firstItem.catchData || {}, context, firstItem.leaderboardResults, {
                showLiveTotals: false,  // Use static totals for notifications
                showServerContext: true  // Show server context for notifications
            });

            const { ButtonBuilder, ButtonStyle, ActionRowBuilder } = require('discord.js');

            const dismissButton = new ButtonBuilder()
                .setCustomId(`you-dismiss-notification-${notification._id}`)
                .setLabel('dismiss')
                .setStyle(ButtonStyle.Secondary);

            const buttonRow = new ActionRowBuilder().addComponents(dismissButton);

            // Return both the item container and the button row separately
            return { container: itemContainer, buttonRow: buttonRow };

        } catch (error) {
            console.error('Error building item notification display:', error);
            return null;
        }
    },

    // Handle button interactions
    async buttons(interaction, args) {
        // Handle notification dismiss buttons
        if (interaction.customId.startsWith('you-dismiss-notification-')) {
            const notificationId = interaction.customId.replace('you-dismiss-notification-', '');

            try {
                const { dismissItemNotification } = require('../../utils/itemDrops.js');
                await dismissItemNotification(notificationId);

                // Rebuild the main page to show next notification or remove notification container
                const user = interaction.user;
                const member = interaction.member;

                // Manually rebuild the main page content for update
                await user.fetch();
                await member.fetch();

                // Get EXP data
                let exp = 0;
                let currentLevel = 0;
                let nextLevelExp = 100;
                let levelRole = null;
                let levelEmoji = '🌱';
                let levels = [];

                try {
                    // OPTIMIZED: Use parallel processing for guild and member data
                    const [guildDataResult, memberDataResult] = await Promise.allSettled([
                        getCachedGuildConfig(interaction.guild.id),
                        getCachedMemberData(user.id, interaction.guild.id)
                    ]);

                    // Handle guild data result
                    const guildData = guildDataResult.status === 'fulfilled' ? guildDataResult.value : null;
                    const memberData = memberDataResult.status === 'fulfilled' ? memberDataResult.value : null;

                    if (guildData?.exp?.enabled && memberData?.exp?.total !== undefined) {
                        exp = memberData.exp.total;

                        // Use cached level calculation for better performance
                        const { getCachedLevelCalculation } = require('../../utils/expCache.js');
                        levels = guildData.exp.levels ?? [];
                        const levelCalc = getCachedLevelCalculation(exp, levels);

                        currentLevel = levelCalc.currentLevel;
                        nextLevelExp = levelCalc.nextLevelExp;

                        if (levelCalc.levelIndex >= 0) {
                            const currentLevelData = levels[levelCalc.levelIndex];
                            levelRole = currentLevelData.roleId ? `<@&${currentLevelData.roleId}>` : null;
                            levelEmoji = currentLevelData.emoji || '🌱';
                        } else {
                            levelRole = null;
                            levelEmoji = '🌱';
                        }
                    }

                    // Track performance metrics
                    youMetrics.profilesProcessed++;
                    youMetrics.expCalculationsProcessed++;
                } catch (error) {
                    console.error('[you] ❌ Error fetching EXP data:', error);
                }

                // Build UI components using modern format
                const hubMenu = await buildYouHubMenu(user.username, user.id, 'you');

                // Build thumbnail section with title and quote (same as main page)
                const titleText = `## ${levelEmoji} Level ${currentLevel}${levelRole ? ` ${levelRole}` : ''}`;
                const quoteText = `you in ${interaction.guild.name} and globally`;

                const profileImage = new ThumbnailBuilder({
                    media: { url: user.displayAvatarURL({ forceStatic: false }) }
                });

                const titleTextDisplay = new TextDisplayBuilder().setContent(titleText);
                const quoteTextDisplay = new TextDisplayBuilder().setContent(`> ${quoteText}`);

                const titleSection = new SectionBuilder()
                    .setThumbnailAccessory(profileImage)
                    .addTextDisplayComponents(titleTextDisplay, quoteTextDisplay);

                // Build feature menu
                const featureMenu = buildYouFeatureMenu();
                // Build full inventory content instead of preview
                const inventoryContent = await this.buildInventoryContent(interaction, user);
                let inventoryDisplay;
                if (Array.isArray(inventoryContent) && inventoryContent.length > 0) {
                    inventoryDisplay = inventoryContent[0]; // Use the text display component
                } else {
                    inventoryDisplay = new TextDisplayBuilder().setContent('No inventory items found.');
                }

                const smallSeparator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false);

                // Check for item notifications and add to main page
                const notificationResult = await this.buildItemNotificationDisplay(user.id, interaction.guild.id, interaction.client);

                // Build level content using modern format
                let levelContent = `**level:** ${currentLevel}\n`;
                levelContent += `**progress:** ${exp}/${nextLevelExp || exp}\n`;
                levelContent += `**exp:** ${exp.toLocaleString()}\n`;

                // Get starfall data for stars display
                const starfallData = await getStarfallData(user.id);
                levelContent += `**stars:** ${starfallData.stars.toLocaleString()}`;

                const levelDisplay = new TextDisplayBuilder().setContent(levelContent);

                // Build container with modern design
                const container = new ContainerBuilder()
                    .addSectionComponents(titleSection)
                    .addTextDisplayComponents(levelDisplay)
                    .addSeparatorComponents(smallSeparator)
                    .addActionRowComponents(featureMenu)
                    .addSeparatorComponents(smallSeparator)
                    .addTextDisplayComponents(inventoryDisplay);

                // Add inventory select menus if present
                if (Array.isArray(inventoryContent) && inventoryContent.length > 1) {
                    if (inventoryContent[1]) {
                        container.addActionRowComponents(inventoryContent[1]);
                    }
                    if (inventoryContent[2]) {
                        container.addActionRowComponents(inventoryContent[2]);
                    }
                }

                container.setAccentColor(OPERATION_COLORS.ENTITY);

                // Build components array - notification goes at the end
                const components = [hubMenu, container];

                if (notificationResult) {
                    components.push(notificationResult.container, notificationResult.buttonRow);
                }

                // Use update instead of reply for button interactions
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: components
                });
                return;

            } catch (error) {
                console.error('Error dismissing notification:', error);

                // Rebuild current view with status message instead of ephemeral reply
                const user = interaction.user;
                const member = interaction.member;

                // Try to rebuild the main page with status message
                try {
                    const hubMenu = await buildYouHubMenu(user.username, user.id, 'main');
                    const container = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('## you\n\n*Loading your profile...*'))
                        .setAccentColor(OPERATION_COLORS.ENTITY);

                    // Add status message to container
                    const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Error dismissing notification. Please try again.');
                    container.addTextDisplayComponents(statusDisplay);

                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [hubMenu, container]
                    });
                } catch (updateError) {
                    console.error('Error updating with status message:', updateError);
                    // Fallback to ephemeral reply if update fails
                    await interaction.reply({
                        content: '❌ Error dismissing notification. Please try again.',
                        flags: MessageFlags.Ephemeral
                    });
                }
            }
        }
    },

    // Handle inventory item selection (add container like notification center)
    async handleInventoryItemSelect(interaction, user, member) {
        const selectedItemId = interaction.values[0];

        try {
            // Get the item detail container (exactly like notification center)
            const itemDetailContainer = await this.buildItemDetailDisplay(selectedItemId, user.id, interaction.guild.id, interaction.client);

            if (!itemDetailContainer) {
                await interaction.followUp({
                    content: '❌ Could not find the selected item.',
                    flags: MessageFlags.Ephemeral
                });
                return;
            }

            // Store the selected item in state to maintain selection
            const inventoryState = this.getInventoryState(user.id, interaction.guild.id);
            this.storeInventoryState(user.id, interaction.guild.id, {
                ...inventoryState,
                selectedItem: selectedItemId
            });

            // Rebuild the inventory content to update the select menu with selected item
            const inventoryContent = await this.buildInventoryContent(interaction, user);

            // Get current message components
            const currentMessage = interaction.message;
            const existingComponents = [...currentMessage.components];

            // Replace the main container (index 1) with updated inventory that shows selected item
            if (existingComponents.length > 1) {
                // Keep hub menu, replace main container with updated inventory
                const hubMenu = existingComponents[0];

                // Build updated main container with the selected item shown in select menu
                const { getUserGuildRank, getUserGlobalRank } = require("../../utils/expRanking.js");
                const [guildData, memberData] = await Promise.all([
                    optimizedFindOne("guilds", { id: interaction.guild.id }),
                    optimizedFindOne("member", { guildId: interaction.guild.id, userId: user.id })
                ]);

                let exp = 0;
                let level = 0;
                if (guildData?.exp?.enabled && memberData) {
                    exp = memberData.exp?.total || 0;
                    level = memberData.exp?.level || 0;
                }

                // Build level section
                let currentLevel = level;
                let nextLevelExp = null;
                let levelEmoji = '';
                let levelRole = '';

                if (guildData?.exp?.enabled && memberData) {
                    const levels = guildData.exp.levels ?? [];
                    const { getCachedLevelCalculation } = require('../../utils/expCache.js');
                    const levelCalc = getCachedLevelCalculation(exp, levels);

                    currentLevel = levelCalc.currentLevel;
                    nextLevelExp = levelCalc.nextLevelExp;

                    if (levelCalc.levelIndex >= 0) {
                        const currentLevelData = levels[levelCalc.levelIndex];
                        levelRole = currentLevelData.roleId ? `<@&${currentLevelData.roleId}>` : '';
                        levelEmoji = currentLevelData.emoji || '🌱';
                    } else {
                        levelRole = '';
                        levelEmoji = '🌱';
                    }
                }

                // Build thumbnail section with title and quote (modern format)
                const titleText = `## ${levelEmoji} Level ${currentLevel}${levelRole ? ` ${levelRole}` : ''}`;
                const quoteText = `you in ${interaction.guild.name} and globally`;

                const profileImage = new ThumbnailBuilder({
                    media: { url: user.displayAvatarURL({ forceStatic: false }) }
                });

                const titleTextDisplay = new TextDisplayBuilder().setContent(titleText);
                const quoteTextDisplay = new TextDisplayBuilder().setContent(`> ${quoteText}`);

                const titleSection = new SectionBuilder()
                    .setThumbnailAccessory(profileImage)
                    .addTextDisplayComponents(titleTextDisplay, quoteTextDisplay);

                // Build level content using modern format
                let levelContent = `**level:** ${currentLevel}\n`;
                levelContent += `**progress:** ${exp}/${nextLevelExp || exp}\n`;
                levelContent += `**exp:** ${exp.toLocaleString()}\n`;

                // Get starfall data for stars display
                const { getStarfallData } = require('../../utils/starfall.js');
                const starfallData = await getStarfallData(user.id);
                levelContent += `**stars:** ${starfallData.stars.toLocaleString()}`;

                const levelDisplay = new TextDisplayBuilder().setContent(levelContent);

                const featureMenu = buildYouFeatureMenu('inventory');
                const smallSeparator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false);

                // Build updated main container
                const mainContainer = new ContainerBuilder()
                    .addSectionComponents(titleSection)
                    .addTextDisplayComponents(levelDisplay)
                    .addSeparatorComponents(smallSeparator)
                    .addActionRowComponents(featureMenu)
                    .addSeparatorComponents(smallSeparator)
                    .addTextDisplayComponents(inventoryContent[0]);

                // Add select menus (now with selected item maintained)
                if (inventoryContent[1]) {
                    mainContainer.addActionRowComponents(inventoryContent[1]);
                }
                if (inventoryContent[2]) {
                    mainContainer.addActionRowComponents(inventoryContent[2]);
                }

                mainContainer.setAccentColor(OPERATION_COLORS.ENTITY);

                // Update with new main container + item detail container
                await interaction.editReply({
                    components: [hubMenu, mainContainer, itemDetailContainer]
                });
                return;
            }

            // Fallback: just add the item detail container
            existingComponents.push(itemDetailContainer);

            // Update the message with the additional container (interaction already deferred)
            await interaction.editReply({
                components: existingComponents
            });

        } catch (error) {
            console.error('Error handling inventory item selection:', error);

            // Rebuild inventory view with status message instead of ephemeral followUp
            try {
                const hubMenu = await buildYouHubMenu(user.username, user.id, 'inventory');
                const inventoryContent = await this.buildInventoryContent(interaction, user);
                const components = [hubMenu, ...inventoryContent];

                // Add status message to the last container
                const lastContainer = components[components.length - 1];
                if (lastContainer && typeof lastContainer.addTextDisplayComponents === 'function') {
                    const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Error loading item details.');
                    lastContainer.addTextDisplayComponents(statusDisplay);
                }

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: components
                });
            } catch (updateError) {
                console.error('Error updating with status message:', updateError);
                // Fallback to ephemeral followUp if update fails
                await interaction.followUp({
                    content: '❌ Error loading item details.',
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    // Calculate discovery ranking for an item (what order it was found in)
    async calculateDiscoveryRank(item, guildId) {
        try {
            // Count items of the same type found before this one in the guild
            const guildRank = await optimizedCountDocuments("user_inventory", {
                guildId: guildId,
                itemName: item.itemName,
                itemType: item.itemType,
                droppedAt: { $lt: new Date(item.droppedAt) }
            }) + 1;

            // Count total items of this type in the guild
            const guildTotal = await optimizedCountDocuments("user_inventory", {
                guildId: guildId,
                itemName: item.itemName,
                itemType: item.itemType
            });

            // Count items of the same type found before this one globally
            const globalRank = await optimizedCountDocuments("user_inventory", {
                itemName: item.itemName,
                itemType: item.itemType,
                droppedAt: { $lt: new Date(item.droppedAt) }
            }) + 1;

            // Count total items of this type globally
            const globalTotal = await optimizedCountDocuments("user_inventory", {
                itemName: item.itemName,
                itemType: item.itemType
            });

            return {
                guildRank,
                guildTotal,
                globalRank,
                globalTotal
            };

        } catch (error) {
            console.error('Error calculating discovery rank:', error);
            return { guildRank: 0, guildTotal: 0, globalRank: 0, globalTotal: 0 };
        }
    },

    // Get ranking for a specific item's parameter value (not user's best)
    async getSpecificItemParameterRank(scope, guildId, paramName, itemType, paramValue) {
        try {
            const { parseParameterValue } = require('../../utils/itemRecords.js');
            const numericValue = parseParameterValue(paramValue);

            if (numericValue === null) {
                return { rank: 0, total: 0, value: paramValue };
            }



            // Build query for this parameter type
            const query = {
                itemType: itemType,
                [`catchData.${paramName}`]: { $exists: true, $ne: null }
            };

            if (scope === 'guild') {
                query.guildId = guildId;
            }

            // Count items with better values than this specific item
            const betterCount = await optimizedCountDocuments("user_inventory", {
                ...query,
                [`catchData.${paramName}`]: { $exists: true, $ne: null }
            });

            // Get all items and calculate rank properly
            const allItems = await optimizedFind("user_inventory", query, {
                projection: { [`catchData.${paramName}`]: 1 }
            });

            // Parse all values and sort by numeric value (descending)
            const parsedItems = allItems
                .map(item => ({
                    value: item.catchData[paramName],
                    numericValue: parseParameterValue(item.catchData[paramName])
                }))
                .filter(item => item.numericValue !== null)
                .sort((a, b) => b.numericValue - a.numericValue);

            // Find the rank of this specific value
            const rank = parsedItems.findIndex(item => item.numericValue === numericValue) + 1;

            return {
                rank: rank || 0,
                total: parsedItems.length,
                value: paramValue
            };

        } catch (error) {
            console.error('Error getting specific item parameter rank:', error);
            return { rank: 0, total: 0, value: paramValue };
        }
    }

};

// Export the buildYouHubMenu function for use in other modules
module.exports.buildYouHubMenu = buildYouHubMenu;