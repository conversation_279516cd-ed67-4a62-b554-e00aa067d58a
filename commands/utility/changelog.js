const { StringSelectMenuBuilder, ActionRowBuilder, MessageFlags, TextDisplayBuilder, ContainerBuilder } = require('discord.js');
const { OPERATION_COLORS } = require('../../utils/colors.js');
const fs = require('fs');
const path = require('path');

const CHANGELOG_DIR = path.join(__dirname, '../../json');
const MAX_CHANGELOGS = 25;

function loadChangelogs() {
    const changelogFile = path.join(CHANGELOG_DIR, 'changelog.json');
    let changelogs = [];

    try {
        if (fs.existsSync(changelogFile)) {
            const data = JSON.parse(fs.readFileSync(changelogFile, 'utf8'));
            if (Array.isArray(data.changelogs)) {
                changelogs = data.changelogs;
            }
        } else {
            console.warn('[changelog] changelog.json not found in', CHANGELOG_DIR);
        }
    } catch (e) {
        console.error('[changelog] Error reading changelog.json:', e);
    }

    // Sort by timestamp descending (newest first)
    changelogs.sort((a, b) => Number(b.timestamp) - Number(a.timestamp));
    return changelogs.slice(0, MAX_CHANGELOGS);
}

function buildChangelogEmbed(changelog) {
    const date = changelog.timestamp ? `<t:${Math.floor(Number(changelog.timestamp)/1000)}:D>` : 'unknown';
    const changes = changelog.changes && changelog.changes.length
        ? changelog.changes.map(c => `\\- ${c}`).join('\n')
        : 'No changes listed.';
    return new TextDisplayBuilder().setContent(
`**version:** ${changelog.version}\n**date:** ${date}\n**change(s):**\n${changes}`
    );
}

function buildChangelogContainer(changelogs, selectedVersion) {
    const heading = new TextDisplayBuilder().setContent('# changelog');
    const description = new TextDisplayBuilder().setContent("> what's new?");
    const selected = changelogs.find(c => c.version === selectedVersion) || changelogs[0];
    const changelogDisplay = buildChangelogEmbed(selected);

    // Build select menu
    const options = changelogs.map(c => ({
        label: `version ${c.version}`,
        value: c.version,
        description: c.description ? c.description.slice(0, 80) : (c.changes[0]?.slice(0, 80) || ''),
        default: c.version === selected.version
    }));
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('changelog-select')
        .setPlaceholder(`version ${selected.version}`)
        .addOptions(options);
    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    // Container
    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, description, changelogDisplay)
        .addActionRowComponents(selectRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    loadChangelogs,
    buildChangelogContainer,
    getLatestVersion: () => {
        const changelogs = loadChangelogs();
        return changelogs[0]?.version || 'unknown';
    },
    getLatestChangelog: () => {
        const changelogs = loadChangelogs();
        return changelogs[0] || null;
    },
    select: async (interaction, args) => {
        const selectedVersion = interaction.values[0];
        const changelogs = loadChangelogs();
        const container = buildChangelogContainer(changelogs, selectedVersion);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
    },
}; 