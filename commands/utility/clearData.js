const {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Section<PERSON><PERSON><PERSON>, TextDisplayBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle,
    StringSelectMenuBuilder, MessageFlags
} = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { optimizedDeleteMany } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');

/**
 * Clear all guild data for fresh start
 * Owner-only command for version releases
 */

async function buildClearDataContainer(stats = null) {
    // Button section with heading and back button
    const heading = new TextDisplayBuilder().setContent('# clear data');
    const backButton = new ButtonBuilder()
        .setCustomId('owner-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);

    const buttonSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);

    // Description and status
    const description = new TextDisplayBuilder().setContent('> reset all guild data for fresh start');

    let statusContent = '';
    if (stats) {
        statusContent = `**cleared:**\n` +
            `\\- ${stats.guilds} guild configs\n` +
            `\\- ${stats.members} member records\n` +
            `\\- ${stats.openerThreads} opener threads\n` +
            `\\- ${stats.expMsgs} exp messages\n` +
            `\\- ${stats.items} custom items\n` +
            `\\- ${stats.inventory} inventory items\n` +
            `\\- ${stats.leaderboards} leaderboard records\n` +
            `\\- ${stats.notifications} notification configs\n` +
            `\\- ${stats.emojis || 0} application emojis\n` +
            `\\- ${stats.globalLevels} global levels\n` +
            `\\- ${stats.globalUsers} global user data\n` +
            `\\- ${stats.globalNotifications} global notifications\n` +
            `\\- ${stats.tempData} temp records\n\n` +
            `✅ **all data cleared successfully!**`;
    } else {
        statusContent = `**warning:** this will permanently delete:\n` +
            `\\- all guild configurations\n` +
            `\\- all member data (sticky, exp)\n` +
            `\\- all opener thread tracking\n` +
            `\\- all custom items & inventories\n` +
            `\\- all item leaderboards\n` +
            `\\- all notification settings\n` +
            `\\- all application emojis\n` +
            `\\- all global levels & user data\n` +
            `\\- all global level notifications\n` +
            `\\- all temporary data\n\n` +
            `⚠️ **this action cannot be undone!**`;
    }

    const statusDisplay = new TextDisplayBuilder().setContent(statusContent);

    // Action select menu
    const actionOptions = [
        {
            label: 'clear all data',
            value: 'clear-all',
            description: '⚠️ permanently delete all bot data'
        },
        {
            label: 'clear guild configs only',
            value: 'clear-guilds',
            description: 'keep member data, clear guild settings'
        },
        {
            label: 'clear member data only',
            value: 'clear-members',
            description: 'keep guild configs, clear user data'
        },
        {
            label: 'clear temp data only',
            value: 'clear-temp',
            description: 'clear temporary/cache data only'
        }
    ];

    const actionSelect = new StringSelectMenuBuilder()
        .setCustomId('clear-data-action')
        .setPlaceholder('select data to clear')
        .addOptions(actionOptions)
        .setDisabled(!!stats); // Disable after clearing

    const actionRow = new ActionRowBuilder().addComponents(actionSelect);

    const container = new ContainerBuilder()
        .addSectionComponents(buttonSection)
        .addTextDisplayComponents(description, statusDisplay)
        .addActionRowComponents(actionRow)
        .setAccentColor(stats ? OPERATION_COLORS.ADD : OPERATION_COLORS.DELETE);

    return container;
}

async function clearAllData(client = null) {
    const stats = {
        guilds: 0,
        members: 0,
        openerThreads: 0,
        expMsgs: 0,
        tempData: 0,
        items: 0,
        inventory: 0,
        leaderboards: 0,
        notifications: 0,
        emojis: 0,
        globalLevels: 0,
        globalUsers: 0,
        globalNotifications: 0
    };

    try {


        // Clear application emojis first (before clearing items database)
        if (client) {
            try {
                const existingEmojis = await client.application.emojis.fetch();
                for (const emoji of existingEmojis.values()) {
                    try {
                        await client.application.emojis.delete(emoji.id);
                        stats.emojis++;
                        console.log(`Deleted application emote ${emoji.name} (${emoji.id})`);
                    } catch (emojiError) {
                        console.error(`Failed to delete emote ${emoji.name}:`, emojiError);
                    }
                }
            } catch (error) {
                console.error('Error clearing application emojis:', error);
            }
        }

        // Clear guild configurations
        const guildsResult = await optimizedDeleteMany("guilds", {});
        stats.guilds = guildsResult.deletedCount;

        // Clear member data
        const membersResult = await optimizedDeleteMany("member", {});
        stats.members = membersResult.deletedCount;

        // Clear opener threads
        const openerResult = await optimizedDeleteMany("opener_threads", {});
        stats.openerThreads = openerResult.deletedCount;

        // Clear EXP message tracking
        const expMsgsResult = await optimizedDeleteMany("exp_main_msgs", {});
        stats.expMsgs = expMsgsResult.deletedCount;

        // Clear custom items
        const itemsResult = await optimizedDeleteMany("custom_items", {});
        stats.items = itemsResult.deletedCount;

        // Clear user inventory
        const inventoryResult = await optimizedDeleteMany("user_inventory", {});
        stats.inventory = inventoryResult.deletedCount;

        // Clear item leaderboards
        const leaderboardsResult = await optimizedDeleteMany("item_leaderboards", {});
        stats.leaderboards = leaderboardsResult.deletedCount;

        // Clear item notifications
        const notificationsResult = await optimizedDeleteMany("item_notifications", {});
        stats.notifications = notificationsResult.deletedCount;

        // Clear global levels data
        const globalLevelsResult = await optimizedDeleteMany("global_levels", {});
        stats.globalLevels = globalLevelsResult.deletedCount;

        // Clear global user data
        const globalUserResult = await optimizedDeleteMany("global_user_data", {});
        stats.globalUsers = globalUserResult.deletedCount;

        // Clear global level notifications
        const globalNotificationsResult = await optimizedDeleteMany("global_level_notifications", {});
        stats.globalNotifications = globalNotificationsResult.deletedCount;

        // Clear legacy pchannels collection (p feature remnants)
        const pchannelsResult = await optimizedDeleteMany("pchannels", {});
        stats.pchannels = pchannelsResult.deletedCount;

        // Invalidate global levels caches after clearing
        try {
            const { invalidateAllCaches } = require('../../utils/globalLevels.js');
            invalidateAllCaches();
            console.log('[clearData] Global levels caches invalidated');
        } catch (error) {
            console.error('[clearData] Error invalidating global levels caches:', error);
        }

        // Clear temporary data
        const tempCollections = [
            "exp_create_level_temp",
            "exp_level_channel_temp",
            "user_images_cache",
            "item_creation_state"
        ];

        for (const collectionName of tempCollections) {
            try {
                const tempResult = await optimizedDeleteMany(collectionName, {});
                stats.tempData += tempResult.deletedCount;
            } catch (e) {
                // Collection might not exist, that's fine
            }
        }

        console.log('[clearData] All data cleared:', stats);
        return stats;

    } catch (error) {
        console.error('[clearData] Error clearing data:', error);
        throw error;
    }
}

async function clearGuildData() {

    // Clear guild configurations
    const guildsResult = await optimizedDeleteMany("guilds", {});

    // Clear guild-specific items (keep bot-wide items)
    const itemsResult = await optimizedDeleteMany("custom_items", { guildId: { $ne: null } });

    // Clear guild-specific inventory items
    const inventoryResult = await optimizedDeleteMany("user_inventory", { guildId: { $ne: null } });

    // Clear guild-specific leaderboards
    const leaderboardsResult = await optimizedDeleteMany("item_leaderboards", { scope: "guild" });

    // Clear legacy pchannels collection (p feature remnants)
    const pchannelsResult = await optimizedDeleteMany("pchannels", {});

    return {
        guilds: guildsResult.deletedCount,
        members: 0,
        openerThreads: 0,
        expMsgs: 0,
        tempData: 0,
        items: itemsResult.deletedCount,
        inventory: inventoryResult.deletedCount,
        leaderboards: leaderboardsResult.deletedCount,
        notifications: 0,
        emojis: 0,
        pchannels: pchannelsResult.deletedCount
    };
}

async function clearMemberData() {

    // Clear member data
    const membersResult = await optimizedDeleteMany("member", {});

    // Clear user inventories
    const inventoryResult = await optimizedDeleteMany("user_inventory", {});

    // Clear item leaderboards (since they contain user records)
    const leaderboardsResult = await optimizedDeleteMany("item_leaderboards", {});

    return {
        guilds: 0,
        members: membersResult.deletedCount,
        openerThreads: 0,
        expMsgs: 0,
        tempData: 0,
        items: 0,
        inventory: inventoryResult.deletedCount,
        leaderboards: leaderboardsResult.deletedCount,
        notifications: 0,
        emojis: 0
    };
}

async function clearTempData() {
    let tempData = 0;
    const tempCollections = [
        "exp_main_msgs",
        "exp_create_level_temp",
        "exp_level_channel_temp",
        "user_images_cache",
        "item_creation_state"
    ];

    for (const collectionName of tempCollections) {
        try {
            const result = await optimizedDeleteMany(collectionName, {});
            tempData += result.deletedCount;
        } catch (e) {
            // Collection might not exist
        }
    }

    return {
        guilds: 0,
        members: 0,
        openerThreads: 0,
        expMsgs: 0,
        tempData,
        items: 0,
        inventory: 0,
        leaderboards: 0,
        notifications: 0,
        emojis: 0
    };
}

module.exports = {
    async execute(interaction) {
        // Restrict to bot owner only
        if (interaction.user.id !== process.env.OWNER) {
            await interaction.reply({ 
                content: '❌ This command is restricted to the bot owner only.', 
                ephemeral: true 
            });
            return;
        }
        
        const container = await buildClearDataContainer();
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
    },
    
    async select(interaction) {
        // Double-check owner permission
        if (interaction.user.id !== process.env.OWNER) {
            await interaction.reply({ 
                content: '❌ Access denied.', 
                ephemeral: true 
            });
            return;
        }
        
        const action = interaction.values[0];
        await interaction.deferUpdate();
        
        let stats;
        try {
            switch (action) {
                case 'clear-all':
                    stats = await clearAllData(interaction.client);
                    break;
                case 'clear-guilds':
                    stats = await clearGuildData();
                    break;
                case 'clear-members':
                    stats = await clearMemberData();
                    break;
                case 'clear-temp':
                    stats = await clearTempData();
                    break;
                default:
                    throw new Error('Unknown action');
            }
            
            const container = await buildClearDataContainer(stats);
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            
        } catch (error) {
            console.error('[clearData] Error:', error);
            await interaction.editReply({
                content: `❌ Error clearing data: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    }
};
