const { <PERSON><PERSON><PERSON>ommandBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, ContainerBuilder, TextDisplayBuilder, MessageFlags,ChannelSelectMenuBuilder, SectionBuilder, ThumbnailBuilder, SeparatorBuilder,SeparatorSpacingSize,TextInputStyle,ComponentType,StringSelectMenuBuilder,StringSelectMenuOptionBuilder } = require('discord.js');
const { optimizedFind, optimizedFindOne, optimizedUpdateOne, optimizedUpdateMany, optimizedAggregate } = require('../../utils/database-optimizer.js');
const { incrementCommandUsage } = require('../../utils/commandUsage');
const { registerCommand } = require('../../utils/commandInvalidation');
const { buildOwnerContainer } = require('./owner');
const { buildOpenerContainer } = require('./opener');
const { setThreadLastOpened, getLastOpenedThread, setThreadsLastOpened, getAllOpenedThreads, replaceTrackedThreads } = require('./opener_db');
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { buildChangelogContainer, loadChangelogs } = require('./changelog');
const logs = require('./logs');
const sticky = require('./sticky');
const dehoist = require('./dehoist');
const { buildSelectMenu } = require('./featuresMenu');
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');

/**
 * 17 Command System (Enterprise-Grade Performance Optimized)
 * Primary bot interface with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const seventeenMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    commandExecutions: 0,
    guildConfigFetches: 0,
    featureToggles: 0,
    uiBuilds: 0,
    parallelOperations: 0,
    partialFailures: 0,
    dmMessageUpdates: 0,
    permissionChecks: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations
const featureStateCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for feature states
const uiComponentCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for UI components
const permissionCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for permission checks
const dmMessageCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for DM message templates

// Register caches for global cleanup
registerCache(guildConfigCache);
registerCache(featureStateCache);
registerCache(uiComponentCache);
registerCache(permissionCache);
registerCache(dmMessageCache);

/**
 * Get cached guild configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild configuration data
 */
async function getCachedGuildConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_config_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildConfigCache.get(cacheKey);
        if (cached) {
            seventeenMetrics.cacheHits++;
            if (seventeenMetrics.verboseLogging) {
                console.log(`[17] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        seventeenMetrics.cacheMisses++;
        seventeenMetrics.databaseQueries++;
        seventeenMetrics.guildConfigFetches++;

        // Get guild data with default fallback
        let guildData = await optimizedFindOne("guilds", { id: guildId });
        if (!guildData) {
            guildData = {
                items: { enabled: true },
                exp: { enabled: true },
                logs: { enabled: true },
                sticky: { enabled: true },
                dehoist: { enabled: true },
                opener: { enabled: true }
            };
        }

        // Ensure all feature structures exist
        if (!guildData.items) guildData.items = { enabled: true };
        if (!guildData.exp) guildData.exp = { enabled: true };
        if (!guildData.logs) guildData.logs = { enabled: true };
        if (!guildData.sticky) guildData.sticky = { enabled: true };
        if (!guildData.dehoist) guildData.dehoist = { enabled: true };
        if (!guildData.opener) guildData.opener = { enabled: true };

        // Cache the result
        guildConfigCache.set(cacheKey, guildData);

        const duration = Date.now() - startTime;
        seventeenMetrics.averageQueryTime =
            (seventeenMetrics.averageQueryTime * (seventeenMetrics.databaseQueries - 1) + duration) /
            seventeenMetrics.databaseQueries;

        if (seventeenMetrics.verboseLogging || duration > 100) {
            console.log(`[17] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return guildData;
    } catch (error) {
        console.error(`[17] ❌ Error getting guild config for ${guildId}:`, error);
        return {
            items: { enabled: true },
            exp: { enabled: true },
            logs: { enabled: true },
            sticky: { enabled: true },
            dehoist: { enabled: true },
            opener: { enabled: true }
        };
    }
}

/**
 * Get cached feature state (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for specific feature states
 * @param {string} guildId - Guild ID
 * @param {string} feature - Feature name (items, exp, logs, etc.)
 * @returns {Promise<Object>} Feature state data
 */
async function getCachedFeatureState(guildId, feature) {
    const startTime = Date.now();
    const cacheKey = `feature_${feature}_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = featureStateCache.get(cacheKey);
        if (cached) {
            seventeenMetrics.cacheHits++;
            if (seventeenMetrics.verboseLogging) {
                console.log(`[17] ⚡ Feature state cache hit for ${feature} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        seventeenMetrics.cacheMisses++;

        // Get full guild config and extract feature
        const guildData = await getCachedGuildConfig(guildId);
        const featureData = guildData[feature] || { enabled: true };

        // Cache the specific feature state
        featureStateCache.set(cacheKey, featureData);

        const duration = Date.now() - startTime;
        if (seventeenMetrics.verboseLogging || duration > 50) {
            console.log(`[17] ✅ Feature state fetched for ${feature}: ${duration}ms - cached for future access`);
        }

        return featureData;
    } catch (error) {
        console.error(`[17] ❌ Error getting feature state for ${feature}:`, error);
        return { enabled: true };
    }
}

/**
 * Get cached permission check (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching for permission validation
 * @param {Object} member - Discord member object
 * @param {string} feature - Feature name
 * @returns {boolean} Permission result
 */
function getCachedPermissionCheck(member, feature) {
    const startTime = Date.now();
    const cacheKey = `permission_${member.id}_${feature}_${member.guild.id}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = permissionCache.get(cacheKey);
        if (cached !== undefined) {
            seventeenMetrics.cacheHits++;
            if (seventeenMetrics.verboseLogging) {
                console.log(`[17] ⚡ Permission cache hit for ${feature} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        seventeenMetrics.cacheMisses++;
        seventeenMetrics.permissionChecks++;

        // Check permission using global function
        const hasPermission = global.hasFeaturePermission ? global.hasFeaturePermission(member, feature) : true;

        // Cache the result (shorter TTL for permission checks)
        permissionCache.set(cacheKey, hasPermission);

        const duration = Date.now() - startTime;
        if (seventeenMetrics.verboseLogging || duration > 10) {
            console.log(`[17] ✅ Permission checked for ${feature}: ${duration}ms - cached for future access`);
        }

        return hasPermission;
    } catch (error) {
        console.error(`[17] ❌ Error checking permission for ${feature}:`, error);
        return true; // Default to true on error
    }
}

// Add this function to build the main container
async function buildMainContainer(interaction, numServers, numMembers, numCmdsUsedFormatted, numVersion) {
    // Handle cases where interaction might be undefined or malformed
    if (!interaction || !interaction.client || !interaction.client.user) {
        console.error('[buildMainContainer] Invalid interaction object provided');
        // Return a basic container without client-dependent data
        const heading = new TextDisplayBuilder().setContent('# 17');
        const subheading = new TextDisplayBuilder().setContent('> a bug ??');

        const container = new ContainerBuilder()
            .addTextDisplayComponents(heading, subheading)
            .setAccentColor(OPERATION_COLORS.ERROR);

        return container;
    }
    const heading = new TextDisplayBuilder().setContent('# 17');
    const subheading = new TextDisplayBuilder().setContent('> -# **a bot.**');

    const serversText = new TextDisplayBuilder().setContent(
`**servers:** ${numServers}
**members:** ${numMembers}
**cmds used:** ${numCmdsUsedFormatted}
**version:** ${numVersion}
`);

    const alienComponent = new ThumbnailBuilder({
        media: {
            url: "https://images-ext-1.discordapp.net/external/ApCEqAMXvchBZHtP4pQZubq7FLMAUiJ5kLLB3BNChE4/https/cdn.discordapp.com/avatars/331669060221796362/81721a31232170290dc574ad1f7c07f6.webp?format=webp&width=205&height=205"
        }
    });

    const separatorComponent = new SeparatorBuilder()
        .setSpacing(SeparatorSpacingSize.Large);

    const sectionOne = new SectionBuilder()
        .setThumbnailAccessory(alienComponent)
        .addTextDisplayComponents(heading, subheading, serversText);

    const changelogButton = new ButtonBuilder()
        .setCustomId('17-changelog')
        .setLabel('changelog')
        .setStyle(ButtonStyle.Primary)
        .setDisabled(false);

    const changelogDescription = new TextDisplayBuilder().setContent("what's changed?")

    const sectionTwo = new SectionBuilder()
        .addTextDisplayComponents(changelogDescription)
        .setButtonAccessory(changelogButton);

    const clientId = interaction.client.user.id;
    // Calculate specific permissions needed for all bot features
    const { PermissionFlagsBits } = require('discord.js');
    const permissions =
        PermissionFlagsBits.ViewChannel |           // View channels
        PermissionFlagsBits.SendMessages |          // Send messages
        PermissionFlagsBits.ReadMessageHistory |    // Read message history
        PermissionFlagsBits.ManageMessages |        // Manage messages (for logging)
        PermissionFlagsBits.EmbedLinks |            // Embed links
        PermissionFlagsBits.AttachFiles |           // Attach files
        PermissionFlagsBits.UseExternalEmojis |     // Use external emojis
        PermissionFlagsBits.AddReactions |          // Add reactions
        PermissionFlagsBits.ManageRoles |           // Create/delete/manage roles (EXP levels)
        PermissionFlagsBits.ViewGuildInsights |     // View guild insights
        PermissionFlagsBits.Connect |               // Connect to voice channels
        PermissionFlagsBits.ViewAuditLog |          // View audit log (for logging)
        PermissionFlagsBits.ManageNicknames |       // Manage nicknames (dehoist/sticky)
        PermissionFlagsBits.ManageThreads |         // Manage threads (opener feature)
        PermissionFlagsBits.CreatePublicThreads |   // Create public threads
        PermissionFlagsBits.CreatePrivateThreads |  // Create private threads
        PermissionFlagsBits.SendMessagesInThreads | // Send messages in threads
        PermissionFlagsBits.UseApplicationCommands; // Use application commands

    const inviteUrl = `https://discord.com/oauth2/authorize?client_id=${clientId}&scope=bot+applications.commands&permissions=${permissions}`;

    const inviteButton = new ButtonBuilder()
        .setLabel('invite')
        .setStyle(ButtonStyle.Link)
        .setURL(inviteUrl);

    const inviteDescription = new TextDisplayBuilder().setContent("take me w/ u")

    const sectionThree = new SectionBuilder()
        .addTextDisplayComponents(inviteDescription)
        .setButtonAccessory(inviteButton);

    const containerComponent = new ContainerBuilder()
        .addSectionComponents(sectionOne)
        .addSeparatorComponents(separatorComponent)
        .addSectionComponents(sectionTwo)
        .addSectionComponents(sectionThree)
        .setAccentColor(OPERATION_COLORS.ENTITY);
    return containerComponent;
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('17')
        .setDescription('hello from in here'),
    async execute(interaction) {
        const startTime = Date.now();

        try {
            // Defer the interaction early to prevent timeout
            await interaction.deferReply({
                flags: MessageFlags.Ephemeral
            });

            // OPTIMIZED: Enhanced parallel operations with cached guild config
            const [commandRegistration, commandUsageIncrement, statsData, guildConfigData] = await Promise.allSettled([
                registerCommand(interaction.user.id, '17', interaction.id, interaction.client),
                incrementCommandUsage('17'), // Track command usage - only increment once per /17 execution
                this.getOptimizedStatsData(interaction.client),
                getCachedGuildConfig(interaction.guild.id) // Pre-fetch guild config for UI building
            ]);

            // Track parallel operation
            seventeenMetrics.parallelOperations++;
            seventeenMetrics.commandExecutions++;

            // Handle results with graceful fallbacks
            const statsResult = statsData.status === 'fulfilled' ? statsData.value : { numServers: 0, numMembers: 0, numCmdsUsed: 0 };
            const guildConfig = guildConfigData.status === 'fulfilled' ? guildConfigData.value : null;

            // Track partial failures
            const failures = [commandRegistration, commandUsageIncrement, statsData, guildConfigData].filter(r => r.status === 'rejected');
            if (failures.length > 0) {
                seventeenMetrics.partialFailures++;
                if (seventeenMetrics.verboseLogging) {
                    console.log(`[17] ⚠️ ${failures.length} partial failures in parallel initialization`);
                }
            }

            const { numServers, numMembers, numCmdsUsed } = statsResult;
            const numCmdsUsedFormatted = numCmdsUsed.toLocaleString('en-US');
            const numVersion = "2.3";

            // OPTIMIZED: Enhanced container building with cached data
            seventeenMetrics.uiBuilds++;
            const [mainContainer, selectMenu] = await Promise.all([
                buildMainContainer(interaction, numServers, numMembers, numCmdsUsedFormatted, numVersion),
                Promise.resolve(buildSelectMenu(false, interaction.user.id)) // Wrap sync function for consistency
            ]);

            // OPTIMIZATION 3: Performance logging
            const loadTime = Date.now() - startTime;
            if (loadTime > 500) {
                console.log(`[17] PERFORMANCE: Command loaded in ${loadTime}ms (SLOW)`);
            } else {
                console.log(`[17] PERFORMANCE: Command loaded in ${loadTime}ms`);
            }

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, mainContainer]
            });

        } catch (error) {
            console.error('[17] Error in execute:', error);

            // Check if interaction is still valid before trying to respond
            if (!interaction.replied && !interaction.deferred) {
                try {
                    const errorContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('## error\nSomething went wrong loading the main interface. Please try again.'))
                        .setAccentColor(OPERATION_COLORS.DELETE);

                    await interaction.reply({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [errorContainer]
                    });
                } catch (replyError) {
                    console.error('[17] Failed to send error response:', replyError);
                }
            } else if (interaction.deferred) {
                try {
                    const errorContainer = new ContainerBuilder()
                        .addTextDisplayComponents(new TextDisplayBuilder().setContent('## error\nSomething went wrong loading the main interface. Please try again.'))
                        .setAccentColor(OPERATION_COLORS.DELETE);

                    await interaction.editReply({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [errorContainer]
                    });
                } catch (editError) {
                    console.error('[17] Failed to edit error response:', editError);
                }
            }
        }
    },

    // OPTIMIZATION METHODS - New optimized data fetching methods

    /**
     * Get optimized stats data with caching and parallel operations
     * @param {Client} client - Discord client
     * @returns {Object} Stats data
     */
    async getOptimizedStatsData(client) {
        try {
            // OPTIMIZATION: Get command usage data
            const commandUsageData = await this.getTotalCommandUsage(); // Get total of all commands - don't increment for viewing stats

            // Get guild/member data from cache (instant)
            const totalGuilds = client.guilds.cache.size;
            const totalMembers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

            return {
                numServers: totalGuilds,
                numMembers: totalMembers.toLocaleString('en-US'),
                numCmdsUsed: commandUsageData
            };

        } catch (error) {
            console.error('[17] Error in getOptimizedStatsData:', error);
            return {
                numServers: 0,
                numMembers: '0',
                numCmdsUsed: 0
            };
        }
    },



    /**
     * Get total command usage across all commands
     * @returns {number} Total command usage count
     */
    totalUsageCache: { data: null, timestamp: 0 },
    async getTotalCommandUsage() {
        const CACHE_TTL = 30 * 1000; // 30 seconds cache
        const now = Date.now();

        // Check cache first
        if (this.totalUsageCache.data && (now - this.totalUsageCache.timestamp) < CACHE_TTL) {
            return this.totalUsageCache.data;
        }

        try {
            // OPTIMIZED: Use optimizedAggregate for efficient server-side sum calculation (no data transfer)
            const result = await optimizedAggregate("stats", [
                { $match: { type: 'command_usage' } },
                { $group: { _id: null, totalUsage: { $sum: "$count" } } }
            ]);
            const totalUsage = result.length > 0 ? result[0].totalUsage : 0;

            // Cache the result
            this.totalUsageCache = {
                data: totalUsage,
                timestamp: now
            };

            return totalUsage;

        } catch (error) {
            console.error('[17] Error getting total command usage:', error);

            // Return cached data if available, otherwise fallback
            return this.totalUsageCache.data || 0;
        }
    },

    /**
     * @param { ButtonInteraction<"cached"> } interaction
     * @param { string[] } args
    */
    async buttons(interaction, args) {
        // Handle items notification back buttons (Enterprise-Grade Optimized)
        if (interaction.customId === 'items-drop-channel-back' || interaction.customId === 'items-notifications-back' || interaction.customId === 'items-back-to-items') {
            // Return to items interface
            const items = require('./items');
            const isOwner = false; // Guild context = guild options

            // OPTIMIZED: Use cached guild configuration
            let guildData = await getCachedGuildConfig(interaction.guild.id);
            if (!guildData) {
                guildData = { items: { enabled: true } };
            }
            if (!guildData.items) {
                guildData.items = { enabled: true };
            }

            // Track performance metrics
            seventeenMetrics.uiBuilds++;

            const containerResult = await items.buildItemsContainer({
                isOwner: isOwner,
                guildId: interaction.guild.id,
                page: 0,
                client: interaction.client,
                member: interaction.member,
                guildData: guildData,
                selectedNotificationOption: 'drop-channel' // Maintain the selected state when going back
            });

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
            const globalItemsButton = new ButtonBuilder()
                .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

            const containers = Array.isArray(containerResult)
                ? [selectMenu, ...containerResult, globalButtonRow]
                : [selectMenu, containerResult, globalButtonRow];

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: containers
            });
            return;
        }

        // Handle changelog button
        if (interaction.customId === '17-changelog') {
            const changelogs = loadChangelogs();
            if (!changelogs.length) {
                // Build main interface with status message instead of ephemeral reply
                const container = build17Container(interaction.user.id);
                const statusContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** No changelogs found.'))
                    .setAccentColor(OPERATION_COLORS.WARNING);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container, statusContainer]
                });
                return;
            }
            const container = buildChangelogContainer(changelogs, changelogs[0].version);
            await interaction.reply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }
        // const mongo = new MongoGuild({ id: interaction.guild.id });
        console.log("hi");

        // if args is changelog then do that
        // etc..
    },

    async modalSubmit(interaction) {

        if (interaction.customId === 'items-dm-message-template-modal' || interaction.customId === 'items-guild-dm-message-modal' || interaction.customId === 'items-guild-dm-template-modal') {
            // Only allow bot owner to edit DM message template
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild items container with status message instead of ephemeral reply
                const items = require('./items');

                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: { enabled: true } };
                if (!guildData.items) guildData.items = { enabled: true };

                const containerResult = await items.buildItemsContainer({
                    isOwner: false,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData,
                    statusMessage: '❌ Only the bot owner can edit DM message templates'
                });

                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: Array.isArray(containerResult) ? [...containerResult, selectMenu, globalButtonRow] : [containerResult, selectMenu, globalButtonRow]
                });
                return;
            }

            const value = interaction.fields.getTextInputValue('dm-message-template-input');

            // Update all guilds with the new DM message template (bot-wide setting)
            await optimizedUpdateMany("guilds",
                { 'items.enabled': { $ne: false } }, // Only update guilds that have items enabled
                { $set: { 'items.dmMessage': value } }
            );

            if (interaction.customId === 'items-guild-dm-message-modal' || interaction.customId === 'items-guild-dm-template-modal') {
                // Return to items interface (new integrated approach)
                const items = require('./items');
                const isOwner = false; // Guild context = guild options

                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: { enabled: true } };
                if (!guildData.items) guildData.items = { enabled: true };

                const containerResult = await items.buildItemsContainer({
                    isOwner: isOwner,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData
                });

                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: containers
                });
            } else {
                // Return to items interface (legacy)
                const items = require('./items');
                const isOwner = false; // Guild context = guild options

                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: { enabled: true } };
                if (!guildData.items) guildData.items = { enabled: true };

                const containerResult = await items.buildItemsContainer({
                    isOwner: isOwner,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData
                });

                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: containers
                });
            }
        }
    },
    async select(interaction, args) {
        console.log('Select handler triggered:', interaction.customId, interaction.values);

        // Handle items notification configuration (legacy)
        if (interaction.customId === 'items-notifications-config') {
            const selected = interaction.values[0];

            if (selected === 'drop-notifications') {
                // OPTIMIZED: Toggle drop notifications with cached data
                let guildData = await getCachedGuildConfig(interaction.guild.id);
                if (!guildData) guildData = { items: {} };
                if (!guildData.items) guildData.items = {};

                const currentState = guildData.items.dropNotificationsEnabled ?? true;
                const newState = !currentState;

                await optimizedUpdateOne("guilds",
                    { id: interaction.guild.id },
                    { $set: { 'items.dropNotificationsEnabled': newState } },
                    { upsert: true }
                );

                // OPTIMIZED: Invalidate cache after update
                invalidateGuildConfigCache(interaction.guild.id);

                // Track performance metrics
                seventeenMetrics.featureToggles++;

                // Rebuild items interface
                const items = require('./items');
                const isOwner = false; // Guild context = guild options
                guildData.items.dropNotificationsEnabled = newState;

                const containerResult = await items.buildItemsContainer({
                    isOwner: isOwner,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData,
                    selectedNotificationOption: 'drop-notifications' // Maintain the selected state
                });

                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: containers
                });
                return;

            } else if (selected === 'drop-channel') {
                // Show channel selection
                const { ContainerBuilder, SectionBuilder, TextDisplayBuilder } = require('../../utils/logContainers.js');
                const { ChannelSelectMenuBuilder } = require('discord.js');

                const backButton = new ButtonBuilder()
                    .setCustomId('items-drop-channel-back')
                    .setLabel('back')
                    .setStyle(ButtonStyle.Secondary);
                const backSection = new SectionBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('# drop channel'))
                    .setButtonAccessory(backButton);

                const quote = new TextDisplayBuilder().setContent('> select channel for item drop notifications');

                const channelSelect = new ChannelSelectMenuBuilder()
                    .setCustomId('items-drop-channel-select')
                    .setPlaceholder('select channel')
                    .setChannelTypes([0, 5]); // Text and announcement channels

                const channelRow = new ActionRowBuilder().addComponents(channelSelect);

                const container = new ContainerBuilder()
                    .addSectionComponents(backSection)
                    .addTextDisplayComponents(quote)
                    .addActionRowComponents(channelRow)
                    .setAccentColor(OPERATION_COLORS.NEUTRAL);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;

            } else if (selected === 'dm-notifications') {
                // Toggle DM notifications
                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: {} };
                if (!guildData.items) guildData.items = {};

                const currentState = guildData.items.dmNotificationsEnabled ?? true;
                const newState = !currentState;

                await optimizedUpdateOne("guilds",
                    { id: interaction.guild.id },
                    { $set: { 'items.dmNotificationsEnabled': newState } },
                    { upsert: true }
                );

                // Rebuild items interface
                const items = require('./items');
                const isOwner = false; // Guild context = guild options
                guildData.items.dmNotificationsEnabled = newState;

                const containerResult = await items.buildItemsContainer({
                    isOwner: isOwner,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData,
                    selectedNotificationOption: 'dm-notifications' // Maintain the selected state
                });

                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: containers
                });
                return;

            } else if (selected === 'dm-message-template') {
                // Only allow bot owner to edit DM message template
                if (interaction.user.id !== process.env.OWNER) {
                    // Show error message and rebuild container
                    const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                    const hasPermission = global.hasFeaturePermission(interaction.member, 'items');

                    const items = require('./items.js');
                    const containers = await items.buildUnifiedItemContainer(interaction.user.id, interaction.guild.id, false, {
                        statusMessage: '❌ Only the bot owner can edit DM message templates'
                    });

                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: containers
                    });
                    return;
                }

                // Show modal to edit the DM message template
                const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

                const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                const currentMsg = guildData?.items?.dmMessage || 'You found {items} in **{server}**, dropped from {location}:';

                const msgInput = new TextInputBuilder()
                    .setCustomId('dm-message-template-input')
                    .setLabel('Item Drop DM Message')
                    .setPlaceholder('Available variables: {items}, {server}, {location}')
                    .setValue(currentMsg)
                    .setRequired(true)
                    .setMinLength(1)
                    .setMaxLength(200)
                    .setStyle(TextInputStyle.Paragraph);

                const row = new ActionRowBuilder().addComponents(msgInput);
                const modal = new ModalBuilder()
                    .setCustomId('items-dm-message-template-modal')
                    .setTitle('Edit Item Drop DM Message')
                    .addComponents(row);

                await interaction.showModal(modal);
                return;
            }
        }

        // Handle guild notification configuration (new)
        if (interaction.customId === 'items-guild-notifications-config') {
            const selected = interaction.values[0];

            if (selected === 'toggle-drop-notifications') {
                // Toggle drop notifications
                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: {} };
                if (!guildData.items) guildData.items = {};

                const currentState = guildData.items.dropNotificationsEnabled ?? true;
                const newState = !currentState;

                await optimizedUpdateOne("guilds",
                    { id: interaction.guild.id },
                    { $set: { 'items.dropNotificationsEnabled': newState } },
                    { upsert: true }
                );

                // Rebuild notification config container
                const items = require('./items');
                const container = await items.buildGuildNotificationContainer(interaction);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;

            } else if (selected === 'set-drop-channel') {
                // Show channel selection
                const { ContainerBuilder, SectionBuilder, TextDisplayBuilder } = require('../../utils/logContainers.js');
                const { ChannelSelectMenuBuilder } = require('discord.js');

                const backButton = new ButtonBuilder()
                    .setCustomId('items-notifications-back')
                    .setLabel('back')
                    .setStyle(ButtonStyle.Secondary);
                const backSection = new SectionBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('# drop channel'))
                    .setButtonAccessory(backButton);

                const quote = new TextDisplayBuilder().setContent('> select channel for item drop notifications');

                const channelSelect = new ChannelSelectMenuBuilder()
                    .setCustomId('items-guild-drop-channel-select')
                    .setPlaceholder('select channel')
                    .setChannelTypes([0, 5]); // Text and announcement channels

                const channelRow = new ActionRowBuilder().addComponents(channelSelect);

                const container = new ContainerBuilder()
                    .addSectionComponents(backSection)
                    .addTextDisplayComponents(quote)
                    .addActionRowComponents(channelRow)
                    .setAccentColor(OPERATION_COLORS.NEUTRAL);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;

            } else if (selected === 'toggle-dm-notifications') {
                // Toggle DM notifications
                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) guildData = { items: {} };
                if (!guildData.items) guildData.items = {};

                const currentState = guildData.items.dmNotificationsEnabled ?? true;
                const newState = !currentState;

                await optimizedUpdateOne("guilds",
                    { id: interaction.guild.id },
                    { $set: { 'items.dmNotificationsEnabled': newState } },
                    { upsert: true }
                );

                // Rebuild notification config container
                const items = require('./items');
                const container = await items.buildGuildNotificationContainer(interaction);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;

            } else if (selected === 'edit-dm-message') {
                // Show modal to edit the DM message template
                const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');

                const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                const currentMsg = guildData?.items?.dmMessage || 'You found {items} in **{server}**, dropped from {location}:';

                const msgInput = new TextInputBuilder()
                    .setCustomId('dm-message-template-input')
                    .setLabel('Item Drop DM Message')
                    .setPlaceholder('Available variables: {items}, {server}, {location}')
                    .setValue(currentMsg)
                    .setRequired(true)
                    .setMinLength(1)
                    .setMaxLength(200)
                    .setStyle(TextInputStyle.Paragraph);

                const row = new ActionRowBuilder().addComponents(msgInput);
                const modal = new ModalBuilder()
                    .setCustomId('items-guild-dm-message-modal')
                    .setTitle('Edit Item Drop DM Message')
                    .addComponents(row);

                await interaction.showModal(modal);
                return;
            }
        }

        // Handle guild drop channel selection (new cascading approach)
        if (interaction.customId === 'items-notification-channel-select') {
            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
            if (!hasPermission) {
                // Build container with status message instead of ephemeral reply
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const statusContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** You need Kick Members permission to configure items.'))
                    .setAccentColor(OPERATION_COLORS.DANGER);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, statusContainer]
                });
                return;
            }

            const channelId = interaction.values[0];

            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { 'items.dropChannel': channelId } },
                { upsert: true }
            );

            // Return to items interface with updated channel (no separate page)
            const items = require('./items');
            const isOwner = false; // Guild context = guild options

            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { items: { enabled: true } };
            if (!guildData.items) guildData.items = { enabled: true };

            const containerResult = await items.buildItemsContainer({
                isOwner: isOwner,
                guildId: interaction.guild.id,
                page: 0,
                client: interaction.client,
                member: interaction.member,
                guildData: guildData
                // selectedNotificationOption is intentionally omitted to clear the cascading state
            });

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const hasPermissionForButton = global.hasFeaturePermission(interaction.member, 'items');
            const globalItemsButton = new ButtonBuilder()
                .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermissionForButton);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

            const containers = Array.isArray(containerResult)
                ? [selectMenu, ...containerResult, globalButtonRow]
                : [selectMenu, containerResult, globalButtonRow];

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: containers
            });
            return;
        }

        // Handle items drop channel selection
        if (interaction.customId === 'items-drop-channel-select') {
            const channelId = interaction.values[0];

            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { 'items.dropChannel': channelId } },
                { upsert: true }
            );

            // Return to items interface
            const items = require('./items');
            const isOwner = false; // Guild context = guild options

            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { items: { enabled: true } };
            if (!guildData.items) guildData.items = { enabled: true };

            const containerResult = await items.buildItemsContainer({
                isOwner: isOwner,
                guildId: interaction.guild.id,
                page: 0,
                client: interaction.client,
                member: interaction.member,
                guildData: guildData
            });

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const hasPermissionForGlobalButton = global.hasFeaturePermission(interaction.member, 'items');
            const globalItemsButton = new ButtonBuilder()
                .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermissionForGlobalButton);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

            const containers = Array.isArray(containerResult)
                ? [selectMenu, ...containerResult, globalButtonRow]
                : [selectMenu, containerResult, globalButtonRow];

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: containers
            });
            return;
        }

        try {
            const selectedValue = interaction.values[0];
            if (selectedValue === '1') { // 'owner' selected
                // Track navigation usage
                await incrementCommandUsage('17');

                // Restrict to OWNER only
                if (interaction.user.id !== process.env.OWNER) {
                    // Rebuild main 17 container with status message instead of ephemeral reply
                    const mainContainer = await buildMainContainer(interaction.client, interaction.user, interaction.guild);
                    const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                    mainContainer.addTextDisplayComponents(statusDisplay);
                    const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, mainContainer]
                    });
                    return;
                }
                const ownerContainer = await buildOwnerContainer(interaction.client, interaction.user);
                const selectMenu = buildSelectMenu(true, interaction.user.id, selectedValue); // Show 17 option
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, ownerContainer]
                });
            } else if (selectedValue === '2') { // 'opener' selected
                // Track navigation usage
                await incrementCommandUsage('17');

                console.log('[17.select] Opener selected, calling execute...');
                const opener = require('./opener');
                await opener.execute(interaction);
            } else if (selectedValue === 'logs') {
                // Track navigation usage
                await incrementCommandUsage('17');
                await logs.execute(interaction);
            } else if (selectedValue === 'sticky') {
                // Track navigation usage
                await incrementCommandUsage('17');
                await sticky.execute(interaction);
            } else if (selectedValue === 'dehoist') {
                // Track navigation usage
                await incrementCommandUsage('17');
                await dehoist.execute(interaction);
            } else if (selectedValue === 'exp') {
                // Track navigation usage
                await incrementCommandUsage('17');
                const exp = require('./exp');
                await exp.execute(interaction);
            } else if (selectedValue === 'items') {
                // Track navigation usage
                await incrementCommandUsage('17');
                const items = require('./items');
                // In guild context, always use guild interface (even for bot owner)
                const isOwner = false; // Guild context = guild options

                // Get guild data for items enabled state
                let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                if (!guildData) {
                    guildData = { items: { enabled: true } };
                }
                if (!guildData.items) {
                    guildData.items = { enabled: true };
                }

                const containerResult = await items.buildItemsContainer({
                    isOwner: isOwner,
                    guildId: interaction.guild.id,
                    page: 0,
                    client: interaction.client,
                    member: interaction.member,
                    guildData: guildData
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, selectedValue);

                // Add global enable/disable button (similar to EXP feature)
                const hasItemsPermission = global.hasFeaturePermission(interaction.member, 'items');
                const globalItemsButton = new ButtonBuilder()
                    .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                    .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasItemsPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

                // Handle single container or array of containers
                const containers = Array.isArray(containerResult)
                    ? [selectMenu, ...containerResult, globalButtonRow]
                    : [selectMenu, containerResult, globalButtonRow];

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: containers
                });
            } else if (selectedValue === '17') {
                // Track navigation usage
                await incrementCommandUsage('17');

                // Rebuild the main container
                const totalGuilds = interaction.client.guilds.cache.size;
                const totalMembers = interaction.client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

                const numServers = totalGuilds;
                const numMembers = totalMembers.toLocaleString('en-US');
                const numCmdsUsed = await this.getTotalCommandUsage(); // Get total, don't increment
                const numCmdsUsedFormatted = numCmdsUsed.toLocaleString('en-US');
                const numVersion = "2.3";
                const mainContainer = await buildMainContainer(interaction, numServers, numMembers, numCmdsUsedFormatted, numVersion);
                const selectMenu = buildSelectMenu(false, interaction.user.id, selectedValue); // Hide 17 option
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, mainContainer]
                });
            } else {
                // Rebuild main container with status message instead of ephemeral reply
                const mainContainer = await buildMainContainer(interaction.client, interaction.user, interaction.guild);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** unknown option selected.');
                mainContainer.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, mainContainer]
                });
            }
        } catch (err) {
            console.error('Select menu error:', err);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    // Try to rebuild main container with error status message
                    const mainContainer = await buildMainContainer(interaction.client, interaction.user, interaction.guild);
                    const statusDisplay = new TextDisplayBuilder().setContent('**status:** there was an error processing your selection.');
                    mainContainer.addTextDisplayComponents(statusDisplay);
                    const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, mainContainer]
                    });
                } catch (fallbackErr) {
                    console.error('Fallback error:', fallbackErr);
                    await interaction.reply({ content: 'there was an error processing your selection.', ephemeral: true });
                }
            }
        }
    },
    async threadSelect(interaction) {
        console.log('[threadSelect] handler called');
        try {
            console.log('[threadSelect] interaction.values:', interaction.values);
            const threadIds = interaction.values; // Array of selected thread IDs
            
            // Get auto-archive durations for selected threads and reset their timers
            const autoArchiveDurations = {};
            const now = Date.now();
            
            for (const threadId of threadIds) {
                try {
                    console.log('[threadSelect] Fetching thread:', threadId);
                    let thread = await interaction.client.channels.fetch(threadId);
                    console.log('[threadSelect] Thread fetch result:', {
                        exists: !!thread,
                        isThread: thread?.isThread(),
                        name: thread?.name,
                        archived: thread?.archived,
                        joinable: thread?.joinable,
                        manageable: thread?.manageable,
                        currentAutoArchive: thread?.autoArchiveDuration
                    });
                    
                    if (!thread || !thread.isThread()) {
                        console.log('[threadSelect] Thread not found or not a thread, skipping');
                        continue;
                    }
                    
                    // Store the current auto-archive duration
                    const currentDuration = thread.autoArchiveDuration;
                    autoArchiveDurations[threadId] = currentDuration;
                    
                    // Always try to modify the auto-archive duration to make the thread visible
                    try {
                        if (thread.manageable) {
                            console.log('[threadSelect] Modifying auto-archive duration for thread:', threadId, thread.name);
                            // First set to a different duration to force a change
                            await thread.setAutoArchiveDuration(currentDuration === 60 ? 1440 : 60);
                            // Then set back to the original duration
                            await thread.setAutoArchiveDuration(currentDuration);
                            console.log('[threadSelect] Successfully modified auto-archive duration for thread:', threadId, thread.name);
                        } else {
                            console.log('[threadSelect] Thread not manageable, cannot modify auto-archive duration:', threadId);
                        }
                    } catch (err) {
                        console.error('[threadSelect] Error modifying thread:', threadId, err);
                        continue;
                    }
                } catch (err) {
                    console.error('[threadSelect] Error fetching thread:', threadId, err);
                }
            }
            
            console.log('[threadSelect] replaceTrackedThreads start:', threadIds, autoArchiveDurations);
            await replaceTrackedThreads(threadIds, now, autoArchiveDurations);
            console.log('[threadSelect] replaceTrackedThreads done');
            
            // Always fetch the updated list from the DB after updating
            const openedThreads = await getAllOpenedThreads();
            console.log('[threadSelect] getAllOpenedThreads result:', openedThreads);
            const openerContainer = await buildOpenerContainer({ openedThreads });
            console.log('[threadSelect] buildOpenerContainer done');
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'opener');
            console.log('[threadSelect] buildSelectMenu done');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, openerContainer]
            });
            console.log('[threadSelect] interaction.update done');
        } catch (err) {
            console.error('[threadSelect] Error:', err);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    // Try to rebuild main container with error status message
                    const mainContainer = await buildMainContainer(interaction.client, interaction.user, interaction.guild);
                    const statusDisplay = new TextDisplayBuilder().setContent('**status:** There was an error processing your selection.');
                    mainContainer.addTextDisplayComponents(statusDisplay);
                    const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, mainContainer]
                    });
                } catch (fallbackErr) {
                    console.error('[threadSelect] Fallback error:', fallbackErr);
                    await interaction.reply({ content: 'There was an error processing your selection.', ephemeral: true });
                }
            }
        }
    },
    async channelSelect(interaction) {
        // Handle guild drop channel selection (new cascading approach)
        if (interaction.customId === 'items-notification-channel-select') {
            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'items');
            if (!hasPermission) {
                // Build container with status message instead of ephemeral reply
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
                const statusContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('**status:** You need Kick Members permission to configure items.'))
                    .setAccentColor(OPERATION_COLORS.DANGER);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, statusContainer]
                });
                return;
            }

            const channelId = interaction.values[0];

            // Get current guild data to preserve existing enabled state
            let currentGuildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const currentEnabled = currentGuildData?.items?.enabled ?? true; // Default to true if not set

            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                {
                    $set: {
                        'items.dropChannel': channelId,
                        'items.enabled': currentEnabled // Preserve existing enabled state
                    }
                },
                { upsert: true }
            );

            // Return to items interface with updated channel (no separate page)
            const items = require('./items');
            const isOwner = false; // Guild context = guild options

            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { items: { enabled: true } };
            if (!guildData.items) guildData.items = { enabled: true };

            const containerResult = await items.buildItemsContainer({
                isOwner: isOwner,
                guildId: interaction.guild.id,
                page: 0,
                client: interaction.client,
                member: interaction.member,
                guildData: guildData
                // selectedNotificationOption is intentionally omitted to clear the cascading state
            });

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'items');
            const hasPermissionForButton = global.hasFeaturePermission(interaction.member, 'items');
            const globalItemsButton = new ButtonBuilder()
                .setCustomId(guildData.items.enabled ? 'items-global-disable' : 'items-global-enable')
                .setLabel(guildData.items.enabled ? 'disable' : 'enable')
                .setStyle(guildData.items.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermissionForButton);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalItemsButton);

            const containers = Array.isArray(containerResult)
                ? [selectMenu, ...containerResult, globalButtonRow]
                : [selectMenu, containerResult, globalButtonRow];

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: containers
            });
            return;
        }
    }
};

/**
 * Get comprehensive 17 command system performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive 17 command system performance data
 */
function getSeventeenSystemStats() {
    const cacheHitRate = seventeenMetrics.cacheHits + seventeenMetrics.cacheMisses > 0 ?
        (seventeenMetrics.cacheHits / (seventeenMetrics.cacheHits + seventeenMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: seventeenMetrics.cacheHits,
            cacheMisses: seventeenMetrics.cacheMisses,
            databaseQueries: seventeenMetrics.databaseQueries,
            averageQueryTime: `${seventeenMetrics.averageQueryTime.toFixed(2)}ms`,
            commandExecutions: seventeenMetrics.commandExecutions,
            guildConfigFetches: seventeenMetrics.guildConfigFetches,
            featureToggles: seventeenMetrics.featureToggles,
            uiBuilds: seventeenMetrics.uiBuilds,
            parallelOperations: seventeenMetrics.parallelOperations,
            partialFailures: seventeenMetrics.partialFailures,
            dmMessageUpdates: seventeenMetrics.dmMessageUpdates,
            permissionChecks: seventeenMetrics.permissionChecks,
            lastOptimization: new Date(seventeenMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildConfig: guildConfigCache.getStats(),
            featureState: featureStateCache.getStats(),
            uiComponent: uiComponentCache.getStats(),
            permission: permissionCache.getStats(),
            dmMessage: dmMessageCache.getStats()
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            parallelEfficiency: seventeenMetrics.parallelOperations > 0 ?
                ((seventeenMetrics.parallelOperations - seventeenMetrics.partialFailures) / seventeenMetrics.parallelOperations * 100).toFixed(2) + '%' : 'N/A'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    seventeenMetrics.lastOptimization = Date.now();

    const stats = getSeventeenSystemStats();
    if (seventeenMetrics.verboseLogging) {
        console.log(`[17] 📊 Performance Report:`);
        console.log(`[17]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[17]   Command Executions: ${stats.performance.commandExecutions}`);
        console.log(`[17]   Guild Config Fetches: ${stats.performance.guildConfigFetches}`);
        console.log(`[17]   Feature Toggles: ${stats.performance.featureToggles}`);
        console.log(`[17]   UI Builds: ${stats.performance.uiBuilds}`);
        console.log(`[17]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[17]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[17]   DM Message Updates: ${stats.performance.dmMessageUpdates}`);
        console.log(`[17]   Permission Checks: ${stats.performance.permissionChecks}`);
        console.log(`[17]   Parallel Efficiency: ${stats.systemHealth.parallelEfficiency}`);
        console.log(`[17]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[17]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Invalidate guild configuration cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildConfigCache(guildId) {
    const cacheKey = `guild_config_${guildId}`;
    guildConfigCache.delete(cacheKey);

    // Also invalidate related feature state caches
    const features = ['items', 'exp', 'logs', 'sticky', 'dehoist', 'opener'];
    features.forEach(feature => {
        const featureCacheKey = `feature_${feature}_${guildId}`;
        featureStateCache.delete(featureCacheKey);
    });

    if (seventeenMetrics.verboseLogging) {
        console.log(`[17] 🗑️ Invalidated guild config and feature state caches for ${guildId}`);
    }
}

/**
 * Invalidate feature state cache
 * @param {string} guildId - Guild ID
 * @param {string} feature - Feature name
 */
function invalidateFeatureStateCache(guildId, feature) {
    const cacheKey = `feature_${feature}_${guildId}`;
    featureStateCache.delete(cacheKey);

    if (seventeenMetrics.verboseLogging) {
        console.log(`[17] 🗑️ Invalidated feature state cache for ${feature} in ${guildId}`);
    }
}

/**
 * Invalidate permission cache for a user
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {string} feature - Feature name (optional)
 */
function invalidatePermissionCache(userId, guildId, feature = null) {
    if (feature) {
        const cacheKey = `permission_${userId}_${feature}_${guildId}`;
        permissionCache.delete(cacheKey);
    } else {
        // Clear all permission caches for this user in this guild
        const keys = Array.from(permissionCache.keys());
        const userKeys = keys.filter(key => key.includes(`permission_${userId}_`) && key.includes(`_${guildId}`));

        userKeys.forEach(key => {
            permissionCache.delete(key);
        });
    }

    if (seventeenMetrics.verboseLogging) {
        console.log(`[17] 🗑️ Invalidated permission cache for ${userId} in ${guildId}${feature ? ` (${feature})` : ''}`);
    }
}

/**
 * Invalidate DM message cache
 * @param {string} guildId - Guild ID
 */
function invalidateDmMessageCache(guildId) {
    const cacheKey = `dm_message_${guildId}`;
    dmMessageCache.delete(cacheKey);

    if (seventeenMetrics.verboseLogging) {
        console.log(`[17] 🗑️ Invalidated DM message cache for ${guildId}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, seventeenMetrics.performanceReportInterval);

// Export performance functions for external monitoring
module.exports.getSeventeenSystemStats = getSeventeenSystemStats;
module.exports.invalidateGuildConfigCache = invalidateGuildConfigCache;
module.exports.invalidateFeatureStateCache = invalidateFeatureStateCache;
module.exports.invalidatePermissionCache = invalidatePermissionCache;
module.exports.invalidateDmMessageCache = invalidateDmMessageCache;