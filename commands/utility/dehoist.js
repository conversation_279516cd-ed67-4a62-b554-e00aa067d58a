const { Container<PERSON>uilder, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, MessageFlags } = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require('../../utils/database-optimizer.js');
const { buildSelectMenu } = require('./featuresMenu');
const { safeBulkScan, checkBulkScanCooldown } = require('../../utils/dehoistRateLimit');
const { sendFeatureToggleLog, sendDehoistScanCompletedLog } = require("../../utils/sendLog.js");
const { getCachedGuildDehoistConfig, invalidateGuildDehoistConfig } = require('../../utils/dehoistCache.js');
const { defaults } = require("../../utils/default_db_structures.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');

// Demo data for users without permissions
function getDefaultDehoistData() {
    return {
        nameEnabled: true,
        names: ["Alien", "Pluto", "Neptune"],
        blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "`", "{", "|", "}", "~"],
        lastScan: Date.now() - (13 * 60 * 1000), // 13 minutes ago
        dehoisted: 1,
        failed: 0
    };
}

function buildDehoistContainer({ dehoist, enabled = true, hasPermission = true, statusMessage = null }) {
    // Use demo data if user doesn't have permission
    if (!hasPermission) {
        dehoist = getDefaultDehoistData();
        enabled = true; // Show demo as enabled
    }
    // Ensure dehoist object has all required properties with defaults
    const safeDehost = {
        lastScan: null,
        dehoisted: 0,
        failed: 0,
        blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"],
        nameEnabled: true,
        ...dehoist // Override with actual values if they exist
    };

    const heading = new TextDisplayBuilder().setContent('# dehoist');
    const description = new TextDisplayBuilder().setContent('> remove hoisted chars from nicknames');
    // Show scan line above chars
    let scanLine = '**scan:** never';
    if (safeDehost.lastScan) {
        scanLine = `**scan:** <t:${Math.floor(safeDehost.lastScan / 1000)}:R>`;
        if (typeof safeDehost.dehoisted === 'number') {
            scanLine += ` dehoisted ${safeDehost.dehoisted}`;
        }
        if (typeof safeDehost.failed === 'number' && safeDehost.failed > 0) {
            scanLine += ` failed ${safeDehost.failed}`;
        }
    }
    const charsDisplay = new TextDisplayBuilder().setContent(`${scanLine}\n**char(s):**\n> ${safeDehost.blocked.join(' ')}`);
    // Add char(s) and scan all members buttons at the bottom of the container
    const charsButton = new ButtonBuilder()
        .setCustomId('dehoist-editchars')
        .setLabel('char(s)')
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(!enabled || !safeDehost.nameEnabled || !hasPermission);

    // Check if scan is on cooldown (24 hours)
    const scanButton = new ButtonBuilder()
        .setCustomId('dehoist-scan')
        .setLabel('scan all members')
        .setStyle(ButtonStyle.Primary)
        .setDisabled(!enabled || !safeDehost.nameEnabled || !hasPermission);

    // Use the new rate limiting system for cooldown check
    const cooldownCheck = checkBulkScanCooldown(safeDehost.lastScan);
    if (!cooldownCheck.allowed) {
        scanButton
            .setDisabled(true)
            .setLabel(`scan all members (${cooldownCheck.timeLeft}h)`);
    }

    const buttonRow = new ActionRowBuilder().addComponents(charsButton, scanButton);
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, description, charsDisplay)
        .addSeparatorComponents(separator)
        .addActionRowComponents(buttonRow);

    // Add status message at the bottom if present (temporary feedback)
    if (statusMessage) {
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
        container.addTextDisplayComponents(statusDisplay);
    }

    container.setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    //data: new SlashCommandBuilder()
    //    .setName('dehoist')
    //    .setDescription('Configure Dehoist Filter!'),
    async execute(interaction) {
        // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');

        // Use cached dehoist configuration for better performance
        const { getCachedGuildDehoistConfig } = require('../../utils/dehoistCache.js');
        let data = await getCachedGuildDehoistConfig(interaction.guild.id);

        // Ensure the configuration exists in database if it's using defaults
        if (!data.lastScan) {
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });

            if (!guildData || !guildData.dehoist) {
                const defaultDehoist = {
                    nameEnabled: true,
                    names: ["Alien", "Pluto", "Neptune"],
                    blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "`", "{", "|", "}", "~"],
                    lastScan: null
                };

                if (!guildData) {
                    await optimizedInsertOne("guilds", {
                        id: interaction.guild.id,
                        dehoist: defaultDehoist
                    });
                } else {
                    await optimizedUpdateOne("guilds",
                        { id: interaction.guild.id },
                        { $set: { dehoist: defaultDehoist } }
                    );
                }

                // Update cached data
                data = defaultDehoist;
            }
        }
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
        const container = buildDehoistContainer({
            dehoist: data,
            enabled: data.nameEnabled,
            hasPermission
        });
        const disableButton = new ButtonBuilder()
            .setCustomId('dehoist-disable')
            .setLabel('disable')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!hasPermission);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ]
        });
    },

    /**
     * @param { ButtonInteraction<"cached"> } interaction
     * @param { string[] } args
    */
    async buttons(interaction, args) {
        const [action] = args;
        console.log('[dehoist.buttons] Action:', action);

        // Check permissions for all button actions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');
        if (!hasPermission) {
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({
                dehoist: getDefaultDehoistData(),
                enabled: true,
                hasPermission: false
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [
                    selectMenu,
                    container,
                    new ActionRowBuilder().addComponents(disableButton)
                ]
            });
            return;
        }
        // PERFORMANCE OPTIMIZATION: Use optimized database operations with monitoring and retry logic
        var guildData = await optimizedFindOne('guilds', { id: interaction.guild.id });
        if (guildData == null) {
            await optimizedInsertOne('guilds', {
                    id: interaction.guild.id,
                    dehoist: {
                        nameEnabled: true,
                        names: ["Alien", "Pluto", "Neptune"],
                        blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"]
                    },
            });
            guildData = await optimizedFindOne('guilds', { id: interaction.guild.id });
        }

        // Ensure dehoist object exists with defaults
        if (!guildData.dehoist) {
            const defaultDehoist = {
                nameEnabled: true,
                names: ["Alien", "Pluto", "Neptune"],
                blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "\`", "{", "|", "}", "~"]
            };
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { dehoist: defaultDehoist } }
            );
            guildData.dehoist = defaultDehoist;
        }

        let data = guildData.dehoist;
        const charsButton = new ButtonBuilder()
            .setCustomId('dehoist-editchars')
            .setLabel('char(s)')
            .setStyle(ButtonStyle.Secondary);
        const scanButton = new ButtonBuilder()
            .setCustomId('dehoist-scan')
            .setLabel('scan all members')
            .setStyle(ButtonStyle.Primary);
        if (action === 'disable') {
            data.nameEnabled = false;
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'dehoist.nameEnabled': false } });

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Dehoist',
                null,
                false,
                interaction.user.id,
                interaction.client
            );
            const enableButton = new ButtonBuilder()
                .setCustomId('dehoist-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: data, enabled: data.nameEnabled });
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [
                    selectMenu,
                    container,
                    new ActionRowBuilder().addComponents(enableButton)
                ]
            });
            return;
        } else if (action === 'enable') {
            data.nameEnabled = true;
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'dehoist.nameEnabled': true } });

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Dehoist',
                null,
                true,
                interaction.user.id,
                interaction.client
            );
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: data, enabled: data.nameEnabled });
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [
                    selectMenu,
                    container,
                    new ActionRowBuilder().addComponents(disableButton)
                ]
            });
            return;
        } else if (action === 'scan') {
            // Check if dehoist is enabled for this guild
            if (!data.nameEnabled) {
                // Feature is disabled, don't process the scan
                const container = buildDehoistContainer({ dehoist: data, enabled: false });
                const enableButton = new ButtonBuilder()
                    .setCustomId('dehoist-enable')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            // Check cooldown using new rate limiting system
            const cooldownCheck = checkBulkScanCooldown(data.lastScan);
            if (!cooldownCheck.allowed) {
                // Build container with status message instead of ephemeral reply
                const container = buildDehoistContainer({ dehoist: data, enabled: true });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                const statusContainer = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent(`**status:** Please wait ${cooldownCheck.timeLeft} hours before scanning again.`))
                    .setAccentColor(OPERATION_COLORS.WARNING);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, statusContainer]
                });
                return;
            }

            console.log('[dehoist] Starting enhanced bulk scan with rate limiting...');
            await interaction.deferUpdate();

            // Use the new safe bulk scan function
            const results = await safeBulkScan(interaction.guild, data, (progress) => {
                // Log progress every 5 batches to avoid spam
                if (progress.batch % 5 === 0) {
                    console.log(`[dehoist] Progress: ${progress.processed}/${progress.total} (${progress.dehoisted} dehoisted, ${progress.failed} failed)`);
                }
            });

            // Update lastScan timestamp and results in DB
            const now = Date.now();
            await optimizedUpdateOne("guilds", {
                id: interaction.guild.id
            }, {
                $set: {
                    'dehoist.lastScan': now,
                    'dehoist.dehoisted': results.dehoisted,
                    'dehoist.failed': results.failed
                }
            });
            data.lastScan = now;
            data.dehoisted = results.dehoisted;
            data.failed = results.failed;

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            // Send dehoist scan completed log
            await sendDehoistScanCompletedLog(
                interaction.guild.id,
                interaction.user.id,
                results.totalMembers,
                results.dehoisted,
                results.failed,
                interaction.client
            );
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: data, enabled: data.nameEnabled });
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [
                    selectMenu,
                    container,
                    new ActionRowBuilder().addComponents(disableButton)
                ]
            });
            // No ephemeral follow-up needed
            return;
        } else if (action === 'editchars') {
            // Check if dehoist is enabled for this guild
            if (!data.nameEnabled) {
                // Feature is disabled, don't process the edit
                const container = buildDehoistContainer({ dehoist: data, enabled: false });
                const enableButton = new ButtonBuilder()
                    .setCustomId('dehoist-enable')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            console.log('[dehoist.buttons] Showing edit chars modal');
            const modal = new ModalBuilder()
                .setCustomId('dehoist-editchars-modal')
                .setTitle('Edit Hoisted Characters')
                .addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('chars')
                            .setLabel('Characters (space separated)')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(data.blocked.join(' '))
                            .setRequired(true)
                    )
                );
            await interaction.showModal(modal);
            return;
        }
        // Update UI
        const container = buildDehoistContainer({ dehoist: data, enabled: data.nameEnabled });
        const disableButton = new ButtonBuilder()
            .setCustomId('dehoist-disable')
            .setLabel('disable')
            .setStyle(ButtonStyle.Danger);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ]
        });
    },
    async modal(interaction) {
        if (interaction.customId === 'dehoist-editchars-modal') {
            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');
            if (!hasPermission) {
                // Show demo mode if no permission
                const container = buildDehoistContainer({
                    dehoist: getDefaultDehoistData(),
                    enabled: true,
                    hasPermission: false
                });
                const disableButton = new ButtonBuilder()
                    .setCustomId('dehoist-disable')
                    .setLabel('disable')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(true);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)]
                });
                return;
            }

            const chars = interaction.fields.getTextInputValue('chars').split(/\s+/).filter(Boolean);
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const data = guildData.dehoist;

            // Check if dehoist is enabled for this guild
            if (!data.nameEnabled) {
                // Feature is disabled, don't process the modal
                const container = buildDehoistContainer({ dehoist: data, enabled: false });
                const enableButton = new ButtonBuilder()
                    .setCustomId('dehoist-enable')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'dehoist.blocked': chars } });

            // Invalidate cache to ensure fresh data on next access
            invalidateGuildDehoistConfig(interaction.guild.id);

            const updatedGuildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const updatedData = updatedGuildData.dehoist;
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
            const container = buildDehoistContainer({ dehoist: updatedData, enabled: updatedData.nameEnabled });
            const disableButton = new ButtonBuilder()
                .setCustomId('dehoist-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [
                    selectMenu,
                    container,
                    new ActionRowBuilder().addComponents(disableButton)
                ]
            });
        }
    },

    async selectMenu(interaction) {
        // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'dehoist');

        var guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (guildData == null) {
            await optimizedInsertOne("guilds", defaults.guild(interaction.guild.id))
            guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        }

        // Initialize dehoist data if it doesn't exist
        if (!guildData.dehoist) {
            const defaultDehoist = {
                nameEnabled: false,
                blocked: ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '=', '+', '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/', '~', '`'],
                lastScan: null
            };
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { dehoist: defaultDehoist } }
            );
            guildData.dehoist = defaultDehoist;
        }

        let data = guildData.dehoist;
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'dehoist');
        const container = buildDehoistContainer({
            dehoist: data,
            enabled: data.nameEnabled,
            hasPermission
        });
        const disableButton = new ButtonBuilder()
            .setCustomId('dehoist-disable')
            .setLabel('disable')
            .setStyle(ButtonStyle.Danger)
            .setDisabled(!hasPermission);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [
                selectMenu,
                container,
                new ActionRowBuilder().addComponents(disableButton)
            ]
        });
    },

    // Export container builder for testing purposes
    buildDehoistContainer: buildDehoistContainer
};