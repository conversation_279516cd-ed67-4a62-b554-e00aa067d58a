const { mongoClient } = require('../../mongo/client.js');
const { optimizedFind, optimizedFindOne, optimizedUpdateOne, optimizedInsertOne, optimizedDeleteOne, optimizedDeleteMany, batchDbOperations } = require('../../utils/database-optimizer.js');
const { sendOpenerThreadWatchedLog, sendOpenerThreadUnwatchedLog, sendOpenerThreadBumpedLog } = require("../../utils/sendLog.js");
const {
    getCachedGuildOpenerConfig,
    getCachedThreadData,
    getCachedRefreshQueue,
    invalidateGuildOpenerConfig,
    invalidateThreadData,
    invalidateRefreshQueue,
    batchInvalidateThreads
} = require('../../utils/openerCache.js');

async function setThreadLastOpened(threadId, timestamp, autoArchiveDuration = null, guildId = null) {
    try {

        // Calculate when the thread needs to be refreshed (5 minutes before archive)
        const refreshAt = autoArchiveDuration ?
            timestamp + ((autoArchiveDuration - 5) * 60 * 1000) :
            null;

        const updateData = {
            lastOpened: timestamp,
            autoArchiveDuration,
            refreshAt
        };

        // Only set guildId if provided (preserve existing guildId if not provided)
        if (guildId) {
            updateData.guildId = guildId;
        }

        await optimizedUpdateOne("opener_threads",
            { threadId },
            { $set: updateData },
            { upsert: true }
        );

        // Invalidate caches for this thread
        invalidateThreadData(threadId);
        invalidateRefreshQueue();

        // Concise log
        console.log(`[opener] Set lastOpened for thread: ${threadId} | autoArchive: ${autoArchiveDuration}min`);
    } catch (error) {
        console.error(`[opener] Error setting lastOpened for thread ${threadId}:`, error);
    }
}

async function getLastOpenedThread() {
    try {
        const docs = await optimizedFind("opener_threads", {}, {
            sort: { lastOpened: -1 },
            limit: 1
        });
        const doc = docs.length > 0 ? docs[0] : null;
        return doc; // { threadId, lastOpened, autoArchiveDuration, refreshAt }
    } catch (error) {
        console.error('[opener] Error getting last opened thread:', error);
        return null;
    }
}

// Add or update multiple threads
async function setThreadsLastOpened(threadIds, timestamp, autoArchiveDurations = {}, guildId = null) {
    try {


        // Use bulk operations for better performance
        const bulkOps = threadIds.map(threadId => {
            const autoArchiveDuration = autoArchiveDurations[threadId];
            const refreshAt = autoArchiveDuration ?
                timestamp + ((autoArchiveDuration - 5) * 60 * 1000) :
                null;

            const updateData = {
                lastOpened: timestamp,
                autoArchiveDuration,
                refreshAt
            };

            // Only set guildId if provided (preserve existing guildId if not provided)
            if (guildId) {
                updateData.guildId = guildId;
            }

            return {
                updateOne: {
                    filter: { threadId },
                    update: { $set: updateData },
                    upsert: true
                }
            };
        });

        if (bulkOps.length > 0) {
            await batchDbOperations("opener_threads", bulkOps);
        }

        // Invalidate caches for affected threads
        batchInvalidateThreads(threadIds);
        invalidateRefreshQueue();

        console.log(`[opener] Set lastOpened for ${threadIds.length} threads`);
    } catch (error) {
        console.error(`[opener] Error setting lastOpened for ${threadIds.length} threads:`, error);
    }
}

// Get all tracked threads, sorted by lastOpened desc
async function getAllOpenedThreads() {
    try {
        const docs = await optimizedFind("opener_threads", {}, {
            sort: { lastOpened: -1 }
        });
        return docs; // [{ threadId, lastOpened, autoArchiveDuration, refreshAt }, ...]
    } catch (error) {
        console.error('[opener] Error getting all opened threads:', error);
        return [];
    }
}

// Get tracked threads for a specific guild, sorted by lastOpened desc
async function getGuildOpenedThreads(guildId) {
    try {
        const docs = await optimizedFind("opener_threads", { guildId }, {
            sort: { lastOpened: -1 }
        });
        return docs; // [{ threadId, lastOpened, autoArchiveDuration, refreshAt, guildId }, ...]
    } catch (error) {
        console.error(`[opener] Error getting opened threads for guild ${guildId}:`, error);
        return [];
    }
}

// Replace the tracked threads with a new list
async function replaceTrackedThreads(threadIds, timestamp, autoArchiveDurations = {}, guildId = null, userId = null, client = null) {
    try {


        // Get current threads to detect changes for logging
        const currentThreads = await optimizedFind("opener_threads", {});
        const currentThreadIds = currentThreads.map(t => t.threadId);

        // Detect newly watched threads (in new list but not in current)
        const newlyWatched = threadIds.filter(id => !currentThreadIds.includes(id));

        // Detect unwatched threads (in current but not in new list)
        const unwatched = currentThreads.filter(t => !threadIds.includes(t.threadId));

        // Remove any threads not in the new list
        if (unwatched.length > 0) {
            await optimizedDeleteMany("opener_threads", { threadId: { $nin: threadIds } });
        }

        // Use bulk operations for better performance
        const bulkOps = threadIds.map(threadId => {
            const autoArchiveDuration = autoArchiveDurations[threadId];
            const refreshAt = autoArchiveDuration ?
                timestamp + ((autoArchiveDuration - 5) * 60 * 1000) :
                null;

            // Only set lastOpened for newly watched threads, preserve existing lastOpened for others
            const isNewlyWatched = newlyWatched.includes(threadId);
            const updateData = {
                autoArchiveDuration,
                refreshAt,
                guildId // Always store guildId for privacy filtering
            };

            if (isNewlyWatched) {
                updateData.lastOpened = timestamp; // Only update lastOpened for newly watched threads
            }

            return {
                updateOne: {
                    filter: { threadId },
                    update: { $set: updateData },
                    upsert: true
                }
            };
        });

        if (bulkOps.length > 0) {
            await batchDbOperations("opener_threads", bulkOps);
        }

        console.log(`[opener] Replaced tracked threads: ${threadIds.length} total, ${newlyWatched.length} newly watched, ${unwatched.length} unwatched`);

        // Send logs and immediately open newly watched threads
        if (guildId && userId && client) {
            // Log and immediately open newly watched threads
            for (const threadId of newlyWatched) {
                try {
                    const thread = await client.channels.fetch(threadId);
                    if (thread && thread.isThread()) {
                        // Send watched log
                        await sendOpenerThreadWatchedLog(guildId, userId, threadId, thread.name, client);

                        // Immediately open/refresh the thread
                        if (thread.manageable) {
                            if (thread.archived) {
                                // If archived, unarchive it
                                await thread.setArchived(false, 'Auto-opened when added to tracking');
                                await thread.setAutoArchiveDuration(thread.autoArchiveDuration);
                                console.log(`[replaceTrackedThreads] Immediately opened newly watched thread: ${threadId} | ${thread.name}`);

                                // Send thread bumped log
                                await sendOpenerThreadBumpedLog(
                                    guildId,
                                    threadId,
                                    thread.name,
                                    'auto-opened when watched',
                                    client
                                );
                            } else {
                                // If already active, reset the timer (like the refresh mechanism)
                                const current = thread.autoArchiveDuration;
                                const alt = current === 60 ? 1440 : 60;
                                await thread.setAutoArchiveDuration(alt);
                                await thread.setAutoArchiveDuration(current);
                                console.log(`[replaceTrackedThreads] Reset timer for newly watched thread: ${threadId} | ${thread.name}`);

                                // Send thread bumped log
                                await sendOpenerThreadBumpedLog(
                                    guildId,
                                    threadId,
                                    thread.name,
                                    'timer reset when watched',
                                    client
                                );
                            }
                        }
                    }
                } catch (err) {
                    console.error(`[replaceTrackedThreads] Error processing watched thread ${threadId}:`, err.message);
                }
            }

            // Log unwatched threads
            for (const threadData of unwatched) {
                try {
                    const thread = await client.channels.fetch(threadData.threadId);
                    const threadName = thread && thread.isThread() ? thread.name : 'Unknown Thread';
                    await sendOpenerThreadUnwatchedLog(guildId, userId, threadData.threadId, threadName, client);
                } catch (err) {
                    console.error(`[replaceTrackedThreads] Error logging unwatched thread ${threadData.threadId}:`, err.message);
                }
            }
        }
    } catch (error) {
        console.error('[opener] Error replacing tracked threads:', error);
    }
}

// Update auto-archive duration for a thread
async function updateThreadAutoArchiveDuration(threadId, autoArchiveDuration) {
    try {

        // Get current thread data
        const thread = await optimizedFindOne("opener_threads", { threadId });
        if (!thread) {
            console.log(`[opener] Thread ${threadId} not found for autoArchive update`);
            return;
        }
        // Calculate new refresh time
        const refreshAt = autoArchiveDuration ?
            thread.lastOpened + ((autoArchiveDuration - 5) * 60 * 1000) :
            null;
        await optimizedUpdateOne("opener_threads",
            { threadId },
            { $set: {
                autoArchiveDuration,
                refreshAt
            }}
        );
        // Concise log
        console.log(`[opener] Updated autoArchive for thread: ${threadId} | autoArchive: ${autoArchiveDuration}min`);
    } catch (error) {
        console.error(`[opener] Error updating autoArchive for thread ${threadId}:`, error);
    }
}

// Get threads that need refresh (refreshAt is in the past) - optimized with caching
async function getThreadsNeedingRefresh() {
    try {
        // Use cached refresh queue for better performance
        const docs = await getCachedRefreshQueue();

        if (docs.length > 0) {
            console.log(`[opener] Found ${docs.length} threads needing refresh`);
            // Only log details for first few threads to avoid spam
            const logLimit = Math.min(docs.length, 5);
            for (let i = 0; i < logLimit; i++) {
                const thread = docs[i];
                console.log(`[opener] Thread needs refresh: ${thread.threadId} | autoArchive: ${thread.autoArchiveDuration}min | lastOpened: ${new Date(thread.lastOpened).toISOString()}`);
            }
            if (docs.length > logLimit) {
                console.log(`[opener] ... and ${docs.length - logLimit} more threads`);
            }
        }
        return docs;
    } catch (error) {
        console.error('[opener] Error getting threads needing refresh:', error);
        return [];
    }
}

// Enable/disable the opener feature for a guild (with cache invalidation)
async function setOpenerEnabled(guildId, enabled) {
    try {
        // PERFORMANCE OPTIMIZATION: Use optimized database operation with monitoring and retry logic
        await optimizedUpdateOne('guilds',
            { id: guildId },
            { $set: { 'opener.enabled': enabled } }
        );

        // Invalidate cache for this guild
        invalidateGuildOpenerConfig(guildId);

        console.log(`[opener] Set enabled state for guild ${guildId}: ${enabled}`);
    } catch (error) {
        console.error(`[opener] Error setting enabled state for guild ${guildId}:`, error);
    }
}

// Get the enabled state for a guild (optimized with caching)
async function isOpenerEnabled(guildId) {
    try {
        const config = await getCachedGuildOpenerConfig(guildId);
        return config.enabled ?? true; // Default to true if not set
    } catch (error) {
        console.error(`[opener] Error getting enabled state for guild ${guildId}:`, error);
        return true; // Default to enabled on error
    }
}

// Get all tracked threads for a guild
async function getGuildTrackedThreads(guildId) {
    try {
        const guild = await optimizedFindOne("guilds", { id: guildId });
        return guild?.opener?.threads ?? [];
    } catch (error) {
        console.error(`[opener] Error getting tracked threads for guild ${guildId}:`, error);
        return [];
    }
}

// Update tracked threads for a guild
async function updateGuildTrackedThreads(guildId, threads) {
    try {
        await optimizedUpdateOne("guilds",
            { id: guildId },
            { $set: { 'opener.threads': threads } }
        );
        console.log(`[opener] Updated tracked threads for guild ${guildId}: ${threads.length} threads`);
    } catch (error) {
        console.error(`[opener] Error updating tracked threads for guild ${guildId}:`, error);
    }
}

module.exports = {
    setThreadLastOpened,
    getLastOpenedThread,
    setThreadsLastOpened,
    getAllOpenedThreads,
    getGuildOpenedThreads, // New guild-specific function
    replaceTrackedThreads,
    updateThreadAutoArchiveDuration,
    getThreadsNeedingRefresh,
    setOpenerEnabled,
    isOpenerEnabled,
    getGuildTrackedThreads,
    updateGuildTrackedThreads
};