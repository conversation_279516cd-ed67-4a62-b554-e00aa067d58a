const { Container<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, ChannelSelectMenuBuilder, RoleSelectMenuBuilder, MessageFlags, PermissionFlagsBits } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne, optimizedFindOneAndUpdate } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');
const { invalidateGuildLogConfig } = require('../../utils/sendLog.js');
const { defaults } = require("../../utils/default_db_structures.js");
const config = require("../../config.js");
const { buildSelectMenu } = require('./featuresMenu');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// OPTIMIZED: LRU cache for guild log data to reduce database queries
const guildLogDataCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes

// Register cache for global cleanup
registerCache(guildLogDataCache);
const { getLogsDemoData } = require("../../utils/demoData.js");

/**
 * Get cached guild data with logs configuration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild data with logs configuration
 */
async function getCachedGuildData(guildId) {
    // OPTIMIZED: Check LRU cache first (automatic TTL and size management)
    const cached = guildLogDataCache.get(guildId);
    if (cached) {
        return cached;
    }

    try {
        let guildData = await optimizedFindOne("guilds", { id: guildId });

        // Create default guild data if not exists
        if (!guildData) {
            await optimizedInsertOne("guilds", defaults.guild(guildId));
            guildData = await optimizedFindOne("guilds", { id: guildId });
        }

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        guildLogDataCache.set(guildId, guildData);

        return guildData;
    } catch (error) {
        console.error(`[logs] Error getting guild data for ${guildId}:`, error);
        return null;
    }
}

/**
 * Invalidate guild data cache and related caches
 * @param {string} guildId - Guild ID
 */
function invalidateGuildDataCache(guildId) {
    guildLogDataCache.delete(guildId);
    invalidateGuildLogConfig(guildId); // Also invalidate sendLog cache
}



// Helper function to expand unified specialty events to individual events
function expandSpecialtyEvents() {
    const expandedEvents = [];
    for (const unifiedEvent of config.specialtyEvents) {
        if (config.specialtyEventMappings[unifiedEvent]) {
            expandedEvents.push(...config.specialtyEventMappings[unifiedEvent]);
        } else {
            expandedEvents.push(unifiedEvent);
        }
    }
    return expandedEvents;
}

// Helper function to check if a unified specialty event has any configured channels
function isUnifiedSpecialtyEventConfigured(unifiedEvent, channels) {
    if (!config.specialtyEventMappings[unifiedEvent]) {
        // Not a unified event, check directly
        return channels.some(ch => ch.events.includes(unifiedEvent));
    }

    // Check if any of the individual events in this unified group are configured
    const individualEvents = config.specialtyEventMappings[unifiedEvent];
    return individualEvents.some(individualEvent =>
        channels.some(ch => ch.events.includes(individualEvent))
    );
}

// Helper function to split unset events into core, specialty, and owner categories
function splitUnsetEvents(unsetArray, interaction) {
    const unsetCore = [];
    const unsetSpecialty = [];
    const unsetOwner = [];
    const coreEvents = config.events;
    const specialtyEvents = [...config.specialtyEvents];
    const ownerEvents = [...config.ownerEvents];

    for (const event of unsetArray) {
        const eventName = event.replace(/`/g, ''); // Remove backticks
        if (coreEvents.includes(eventName)) {
            unsetCore.push(event);
        } else if (specialtyEvents.includes(eventName)) {
            unsetSpecialty.push(event);
        } else if (ownerEvents.includes(eventName)) {
            unsetOwner.push(event);
        }
    }

    return { unsetCore, unsetSpecialty, unsetOwner };
}

// Helper function to get channel or thread by ID (async, fetches if needed)
async function getChannelOrThread(guild, channelId) {
    // First check regular channels
    let channel = guild.channels.cache.get(channelId);
    if (channel) return channel;

    // If not found, look for thread in all channels
    for (const [, guildChannel] of guild.channels.cache) {
        if (guildChannel.threads?.cache.has(channelId)) {
            return guildChannel.threads.cache.get(channelId);
        }
    }

    // If still not found, try to fetch directly by ID
    try {
        const fetchedChannel = await guild.channels.fetch(channelId);
        if (fetchedChannel) return fetchedChannel;
    } catch (error) {
        // If direct fetch fails, it might be a thread - try client.channels.fetch
        try {
            const fetchedThread = await guild.client.channels.fetch(channelId);
            if (fetchedThread && fetchedThread.isThread()) return fetchedThread;
        } catch (threadError) {
            // Only log if it's not a common "Unknown Channel" error to reduce spam
            if (!threadError.message.includes('Unknown Channel')) {
                console.error(`[getChannelOrThread] Could not fetch channel/thread ${channelId}:`, threadError.message);
            }
        }
    }

    return null;
}

// Auto-detect admin and moderator roles
function getAutoSelectedRoles(guild) {
    const autoSelectedRoles = [];

    // Target permissions for auto-selection
    const targetPermissions = [
        PermissionFlagsBits.Administrator,
        PermissionFlagsBits.ManageGuild,
        PermissionFlagsBits.ManageChannels,
        PermissionFlagsBits.ModerateMembers
    ];

    // Check each role in the guild
    guild.roles.cache.forEach(role => {
        // Skip @everyone role
        if (role.id === guild.id) return;

        // Check if role has any of the target permissions
        const hasTargetPermission = targetPermissions.some(permission =>
            role.permissions.has(permission)
        );

        if (hasTargetPermission) {
            autoSelectedRoles.push(role.id);
        }
    });

    return autoSelectedRoles;
}

// Helper function to send messages to appropriate channels/threads
async function sendToLogChannel(channel, messageOptions, eventType) {
    try {
        // If it's a regular text channel, send normally
        if (channel.type === 0) { // Text channel
            return await channel.send(messageOptions);
        }

        // If it's a forum channel, find the appropriate thread
        if (channel.type === 15) { // Forum channel
            // Map event types to thread names
            const eventToThreadMap = {
                // Member events
                'guildMemberAdd': 'Member Activity',
                'guildMemberRemove': 'Member Activity',
                'guildMemberUpdate': 'Member Activity',
                'guildBanAdd': 'Member Activity',
                'guildBanRemove': 'Member Activity',

                // Message events
                'messageDelete': 'Message Moderation',
                'messageUpdate': 'Message Moderation',
                'messageDeleteBulk': 'Message Moderation',

                // Server events
                'guildUpdate': 'Server Changes',
                'channelCreate': 'Server Changes',
                'channelDelete': 'Server Changes',
                'channelUpdate': 'Server Changes',
                'roleCreate': 'Server Changes',
                'roleDelete': 'Server Changes',
                'roleUpdate': 'Server Changes',
                'guildCreate': 'Server Changes',
                'guildDelete': 'Server Changes',

                // Content events
                'emojiCreate': 'Content Updates',
                'emojiDelete': 'Content Updates',
                'emojiUpdate': 'Content Updates',
                'stickerCreate': 'Content Updates',
                'stickerDelete': 'Content Updates',
                'stickerUpdate': 'Content Updates',

                // Voice events
                'voiceStateUpdate': 'Voice Activity',
                'stageInstanceCreate': 'Voice Activity',
                'stageInstanceDelete': 'Voice Activity',
                'stageInstanceUpdate': 'Voice Activity',

                // Thread events
                'threadCreate': 'Thread Management',
                'threadDelete': 'Thread Management',
                'threadUpdate': 'Thread Management',
                'threadMemberUpdate': 'Thread Management',

                // Event/scheduling
                'guildScheduledEventCreate': 'Events & Scheduling',
                'guildScheduledEventDelete': 'Events & Scheduling',
                'guildScheduledEventUpdate': 'Events & Scheduling',
                'guildScheduledEventUserAdd': 'Events & Scheduling',
                'guildScheduledEventUserRemove': 'Events & Scheduling',

                // Integrations
                'integrationCreate': 'Integrations & Webhooks',
                'integrationDelete': 'Integrations & Webhooks',
                'integrationUpdate': 'Integrations & Webhooks',
                'webhookUpdate': 'Integrations & Webhooks',
                'inviteCreate': 'Integrations & Webhooks',
                'inviteDelete': 'Integrations & Webhooks',

                // Bot events
                'featureEnabled': 'Bot Features',
                'featureDisabled': 'Bot Features',
                'expLevelUp': 'Bot Features',
                'expVoiceSession': 'Bot Features',
                'expLevelCreated': 'Bot Features',
                'expLevelEdited': 'Bot Features',
                'expLevelDeleted': 'Bot Features',
                'openerThreadWatched': 'Bot Features',
                'openerThreadBumped': 'Bot Features',
                'openerThreadUnwatched': 'Bot Features',
                'dehoistUsername': 'Bot Features',
                'dehoistScanCompleted': 'Bot Features',
                'stickyNicknameRecovered': 'Bot Features',
                'stickyRolesRecovered': 'Bot Features',
                'botJoinedServer': 'Bot Features',
                'botLeftServer': 'Bot Features',

                // Audit events
                'guildAuditLogEntryCreate': 'Audit & Security',
                'applicationCommandPermissionsUpdate': 'Audit & Security',
                'autoModerationActionExecution': 'Audit & Security'
            };

            const targetThreadName = eventToThreadMap[eventType];
            if (targetThreadName) {
                // Find the thread with the matching name
                const threads = await channel.threads.fetchActive();
                const targetThread = threads.threads.find(thread => thread.name === targetThreadName);

                if (targetThread) {
                    return await targetThread.send(messageOptions);
                } else {
                    console.log(`[sendToLogChannel] Could not find thread "${targetThreadName}" for event "${eventType}"`);
                    // Fallback: send to the first available thread or create a new one
                    const firstThread = threads.threads.first();
                    if (firstThread) {
                        return await firstThread.send(messageOptions);
                    }
                }
            }
        }

        // Fallback: try to send to the channel directly
        return await channel.send(messageOptions);

    } catch (error) {
        console.error(`[sendToLogChannel] Error sending to channel ${channel.name}:`, error);
        throw error;
    }
}

// Smart event grouping for channel creation
function getEventChannelMapping() {
    return {
        'member-logs': [
            'guildMemberAdd', 'guildMemberRemove', 'guildMemberUpdate',
            'guildBanAdd', 'guildBanRemove'
        ],
        'message-logs': [
            'messageDelete', 'messageUpdate', 'messageDeleteBulk'
        ],
        'server-logs': [
            'guildUpdate', 'channelCreate', 'channelDelete', 'channelUpdate',
            'roleCreate', 'roleDelete', 'roleUpdate', 'guildCreate', 'guildDelete'
        ],
        'content-logs': [
            'emojiCreate', 'emojiDelete', 'emojiUpdate',
            'stickerCreate', 'stickerDelete', 'stickerUpdate'
        ],
        'voice-logs': [
            'voiceStateUpdate', 'stageInstanceCreate', 'stageInstanceDelete', 'stageInstanceUpdate'
        ],
        'thread-logs': [
            'threadCreate', 'threadDelete', 'threadUpdate', 'threadMemberUpdate'
        ],
        'event-logs': [
            'guildScheduledEventCreate', 'guildScheduledEventDelete', 'guildScheduledEventUpdate',
            'guildScheduledEventUserAdd', 'guildScheduledEventUserRemove'
        ],
        'integration-logs': [
            'integrationCreate', 'integrationDelete', 'integrationUpdate',
            'webhookUpdate', 'inviteCreate', 'inviteDelete'
        ],
        'bot-logs': [
            'featureEnabled', 'featureDisabled', 'expLevelUp', 'expVoiceSession',
            'expLevelCreated', 'expLevelEdited', 'expLevelDeleted',
            'openerThreadWatched', 'openerThreadBumped', 'openerThreadUnwatched',
            'dehoistUsername', 'dehoistScanCompleted',
            'stickyNicknameRecovered', 'stickyRolesRecovered',
            'itemCreated', 'itemUpdated', 'itemDeleted', 'itemDisabled', 'itemEnabled', 'itemDropped',
            'botJoinedServer', 'botLeftServer'
        ],
        'audit-logs': [
            'guildAuditLogEntryCreate', 'applicationCommandPermissionsUpdate', 'autoModerationActionExecution'
        ]
    };
}

function buildLogsContainer({ logs, interaction, set, unsetCore, unsetSpecialty, unsetOwner = [], selectedChannelId, enabled = true, statusMessage = null, hasPermission = true, member = null, commandChannel = null, showAutomation = false, mainSelectMenu = null }) {
    // Use demo data if user doesn't have permission
    if (!hasPermission && interaction?.guild && interaction?.member) {
        const demoData = getLogsDemoData(interaction.guild, interaction.member, interaction.channel);
        set = demoData.set;
        unsetCore = demoData.unsetCore;
        unsetSpecialty = demoData.unsetSpecialty;
        unsetOwner = []; // Demo data doesn't include owner events
        enabled = true; // Show demo as enabled
    }
    const heading = new TextDisplayBuilder().setContent('# logs');
    const description = new TextDisplayBuilder().setContent('> log all sorts of discord events');

    // Create separate text displays for each category
    const textDisplays = [heading, description];

    // Show set channels first
    if (set.length) {
        // Use subtext formatting for set channels to make them more compact
        const formattedSet = set.map(line => `-# ${line}`);
        const setDisplay = new TextDisplayBuilder().setContent(formattedSet.join('\n'));
        textDisplays.push(setDisplay);
    }

    // Create separate text displays for unset events
    if (unsetCore.length) {
        const unsetCoreDisplay = new TextDisplayBuilder().setContent(`-# **unset core:** ${unsetCore.join(' ')}`);
        textDisplays.push(unsetCoreDisplay);
    }

    if (unsetSpecialty.length) {
        const unsetSpecialtyDisplay = new TextDisplayBuilder().setContent(`-# **unset specialty:** ${unsetSpecialty.join(' ')}`);
        textDisplays.push(unsetSpecialtyDisplay);
    }

    // Only show unset owner events to the bot owner
    if (unsetOwner.length && interaction.user.id === process.env.OWNER) {
        const unsetOwnerDisplay = new TextDisplayBuilder().setContent(`-# **unset owner:** ${unsetOwner.join(' ')}`);
        textDisplays.push(unsetOwnerDisplay);
    }

    // Channel select menu - support both text channels and threads
    const channelSelect = new ChannelSelectMenuBuilder()
        .setCustomId('logs-channel-select')
        .setPlaceholder('select channel or thread')
        .setMinValues(1)
        .setMaxValues(1)
        .addChannelTypes(0, 11, 12) // Text channels (0), Public threads (11), Private threads (12)
        .setDisabled(!enabled || !hasPermission);

    if (selectedChannelId) {
        channelSelect.setDefaultChannels(selectedChannelId);
    }
    const channelRow = new ActionRowBuilder().addComponents(channelSelect);

    // Separate event categories for clear distinction
    const coreEvents = config.events;
    const specialtyEvents = [...config.specialtyEvents];
    const ownerEvents = [...config.ownerEvents];

    let selectedEvents = [];
    if (selectedChannelId) {
        const log = (logs.channels || []).find(ch => ch.id === selectedChannelId);
        if (log) selectedEvents = log.events;
    }

    // Core events select menu (Discord events - single menu)
    const coreEventSelect = new StringSelectMenuBuilder()
        .setCustomId(`logs-core-event-select-${selectedChannelId || 'none'}`)
        .setPlaceholder('select core events to log')
        .setMinValues(0)
        .setMaxValues(Math.min(coreEvents.length, 25))
        .setDisabled(!enabled || !selectedChannelId || !hasPermission)
        .addOptions(coreEvents.map(e => ({
            label: e,
            value: e,
            default: selectedEvents.includes(e)
        })));
    const coreEventRow = new ActionRowBuilder().addComponents(coreEventSelect);

    // Specialty events select menu (Feature events)
    const specialtyEventSelect = new StringSelectMenuBuilder()
        .setCustomId(`logs-specialty-event-select-${selectedChannelId || 'none'}`)
        .setPlaceholder('select specialty events to log')
        .setMinValues(0)
        .setMaxValues(Math.min(specialtyEvents.length, 25))
        .setDisabled(!enabled || !selectedChannelId || !hasPermission)
        .addOptions(specialtyEvents.map(e => {
            // Check if this unified specialty event should be marked as default
            // by checking if any of its individual events are in selectedEvents
            let isDefault = false;
            if (config.specialtyEventMappings[e]) {
                // This is a unified event, check if any individual events are selected
                const individualEvents = config.specialtyEventMappings[e];
                isDefault = individualEvents.some(individualEvent => selectedEvents.includes(individualEvent));
            } else {
                // Not a unified event, check directly
                isDefault = selectedEvents.includes(e);
            }

            return {
                label: e,
                value: e,
                default: isDefault
            };
        }));
    const specialtyEventRow = new ActionRowBuilder().addComponents(specialtyEventSelect);

    // Owner-only events select menu (only visible to bot owner)
    let ownerEventRow = null;
    if (interaction.user.id === process.env.OWNER && ownerEvents.length > 0) {
        const ownerEventSelect = new StringSelectMenuBuilder()
            .setCustomId(`logs-owner-event-select-${selectedChannelId || 'none'}`)
            .setPlaceholder('select owner events to log')
            .setMinValues(0)
            .setMaxValues(Math.min(ownerEvents.length, 25))
            .setDisabled(!enabled || !selectedChannelId)
            .addOptions(ownerEvents.map(e => ({
                label: e,
                value: e,
                default: selectedEvents.includes(e)
            })));
        ownerEventRow = new ActionRowBuilder().addComponents(ownerEventSelect);
    }

    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(...textDisplays)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    // Add main select menu if provided (should be right after the quote)
    if (mainSelectMenu) {
        container.addActionRowComponents(mainSelectMenu);
    }

    container.addSeparatorComponents(separator);

    // Add action rows - include owner event row only if it exists
    if (ownerEventRow) {
        container.addActionRowComponents(channelRow, coreEventRow, specialtyEventRow, ownerEventRow);
    } else {
        container.addActionRowComponents(channelRow, coreEventRow, specialtyEventRow);
    }

    // Add status message at the bottom if present (temporary feedback)
    if (statusMessage) {
        const statusMessageDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
        container.addTextDisplayComponents(statusMessageDisplay);
    }

    return container;
}





module.exports = {
    //data: new SlashCommandBuilder()
    //    .setName("logs")
    //    .setDescription("17 sees all")
    //    // .setDMPermission(false)
    //    .setDefaultMemberPermissions(config.permissions.logs),
    async execute(interaction) {
        // Use the new main interface by default
        await this.showMainInterface(interaction);
    },

    async selectMenu(interaction) {
        // Use the new main interface for select menu navigation
        await this.showMainInterface(interaction);
    },
    /**
     * @param { ButtonInteraction<"cached"> } interaction 
     * @param { string[] } args
    */
    async buttons(interaction, args) {
        // Check permissions for all button actions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
        if (!hasPermission) {
            const container = buildLogsContainer({
                logs: {}, // Will be replaced by demo data
                interaction,
                set: [], // Will be replaced by demo data
                unsetCore: [], // Will be replaced by demo data
                unsetSpecialty: [], // Will be replaced by demo data
                unsetOwner: [], // Will be replaced by demo data
                enabled: true,
                hasPermission: false,
                statusMessage: '❌ You need Administrator permission to configure Logs'
            });
            const enableButton = new ButtonBuilder()
                .setCustomId('logs-enabler')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        }
        // let data = await mongo.p.get();

        const [action] = args;



        // OPTIMIZED: Use cached guild data
        var guildData = await getCachedGuildData(interaction.guild.id);
        let data = guildData.logs || { channels: [], enabled: false };

        if (action == "enabler") {
            await interaction.deferUpdate();
            // OPTIMIZED: Simplify toggle operation to single database call
            const newEnabledState = !data.enabled;
            data = (await optimizedFindOneAndUpdate("guilds", { id: interaction.guild.id }, { $set: { "logs.enabled": newEnabledState } }, { returnDocument: "after" })).logs;

            // OPTIMIZED: Invalidate all related caches when logging config changes
            invalidateGuildDataCache(interaction.guild.id);
            const { invalidateGuildCachingStatus, invalidateAllGuildCaches } = require('../../utils/messageCache.js');
            invalidateGuildCachingStatus(interaction.guild.id);
            invalidateAllGuildCaches(interaction.guild.id);

            // #region buttons
            const buttons = new ActionRowBuilder().setComponents(
                new ButtonBuilder({ style: data.enabled ? ButtonStyle.Danger : ButtonStyle.Success, customId: "logs-enabler", label: data.enabled ? "disable" : "enable" }),
                new ButtonBuilder({ style: ButtonStyle.Primary, customId: "logs-set", label: "set" }),
                new ButtonBuilder({ style: ButtonStyle.Secondary, customId: "logs-unset", label: "unset" }));
            // #endregion
            return interaction.editReply({ components: [buttons] });
        }
        else {
            const modal = new ModalBuilder({ customId: "modal", title: "setting events" });
            const questions = [
                new TextInputBuilder({ customId: "channelid", style: TextInputStyle.Short, label: "What's the channel id?", value: interaction.message.channelId }),
                new TextInputBuilder({ customId: "events", style: TextInputStyle.Paragraph, label: "Events? Separate by spacing", placeholder: "messageDelete messageUpdate guildMemberAdd" })
            ].map(question => new ActionRowBuilder({ components: [question] }));
            if (action == "unset") modal.setTitle("unsetting events");
            modal.setComponents(questions);
            interaction.showModal(modal);

            const submission = await interaction.awaitModalSubmit({ time: 30_000 }).catch(() => null);
            if (!submission) return;
            await submission.deferUpdate().catch(() => null);

            // Channels
            const id = submission.fields.getTextInputValue("channelid");
            const channel = interaction.guild.channels.cache.get(id);
            if (!channel) {
                // Show error in container instead of ephemeral response
                const set = [], unset = [];
                const allEvents = [...config.events, ...config.specialtyEvents];
                if (interaction.user.id === process.env.OWNER) {
                    allEvents.push(...config.ownerEvents);
                }
                for (const event of allEvents) {
                    const channels = (data.channels || []).filter(ch => ch.events.includes(event) && interaction.guild.channels.cache.get(ch.id));
                    if (!channels.length) unset.push(`\`${event}\``);
                }
                for (const log of data.channels) {
                    const channel = interaction.guild.channels.cache.get(log.id);
                    if (channel && log.events.length) set.push(`${channel}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                }
                const { unsetCore, unsetSpecialty, unsetOwner } = splitUnsetEvents(unset, interaction);
                const container = buildLogsContainer({ logs: data, interaction, set, unsetCore, unsetSpecialty, unsetOwner, enabled: data.enabled, statusMessage: '❌ No channel found with that ID' });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel(data.enabled ? 'disable' : 'enable')
                    .setStyle(data.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await submission.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }
            if (!channel.isTextBased()) {
                // Show error in container instead of ephemeral response
                const set = [], unset = [];
                const allEvents = [...config.events, ...config.specialtyEvents];
                if (interaction.user.id === process.env.OWNER) {
                    allEvents.push(...config.ownerEvents);
                }
                for (const event of allEvents) {
                    const channels = (data.channels || []).filter(ch => ch.events.includes(event) && interaction.guild.channels.cache.get(ch.id));
                    if (!channels.length) unset.push(`\`${event}\``);
                }
                for (const log of data.channels) {
                    const channel = interaction.guild.channels.cache.get(log.id);
                    if (channel && log.events.length) set.push(`${channel}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                }
                const { unsetCore, unsetSpecialty, unsetOwner } = splitUnsetEvents(unset, interaction);
                const container = buildLogsContainer({ logs: data, interaction, set, unsetCore, unsetSpecialty, unsetOwner, enabled: data.enabled, statusMessage: '❌ Channel must be text-based' });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel(data.enabled ? 'disable' : 'enable')
                    .setStyle(data.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await submission.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }
            // Events
            const inputs = submission.fields.getTextInputValue("events").split(" ").map(s => s.toLowerCase().trim());
            const allEvents = [...config.events, ...config.specialtyEvents];
            if (interaction.user.id === process.env.OWNER) {
                allEvents.push(...config.ownerEvents);
            }
            const events = allEvents.filter(e => inputs.includes(e.toLowerCase()));
            if (!events.length) {
                // Show error in container instead of ephemeral response
                const set = [], unset = [];
                const allEvents = [...config.events, ...config.specialtyEvents];
                if (interaction.user.id === process.env.OWNER) {
                    allEvents.push(...config.ownerEvents);
                }
                for (const event of allEvents) {
                    const channels = (data.channels || []).filter(ch => ch.events.includes(event) && interaction.guild.channels.cache.get(ch.id));
                    if (!channels.length) unset.push(`\`${event}\``);
                }
                for (const log of data.channels) {
                    const channel = interaction.guild.channels.cache.get(log.id);
                    if (channel && log.events.length) set.push(`${channel}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                }
                const { unsetCore, unsetSpecialty, unsetOwner } = splitUnsetEvents(unset, interaction);
                const container = buildLogsContainer({ logs: data, interaction, set, unsetCore, unsetSpecialty, unsetOwner, enabled: data.enabled, statusMessage: '❌ None of the events you provided are valid' });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel(data.enabled ? 'disable' : 'enable')
                    .setStyle(data.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await submission.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            
            let channel_val = channel.id ? channel.id : channel;
            const log = data.channels.filter(ch => ch.id == channel_val)[0] ?? null;
            // const log = await mongo.logs.getByChannel(channel);
            if (action == "set") {
                if (log?.events.length && !events.filter(e => !log.events.includes(e)).length) {
                    // Show error in container instead of ephemeral response
                    const set = [], unset = [];
                    const allEvents = [...config.events, ...config.specialtyEvents];
                    if (interaction.user.id === process.env.OWNER) {
                        allEvents.push(...config.ownerEvents);
                    }
                    for (const event of allEvents) {
                        const channels = (data.channels || []).filter(ch => ch.events.includes(event) && interaction.guild.channels.cache.get(ch.id));
                        if (!channels.length) unset.push(`\`${event}\``);
                    }
                    for (const log of data.channels) {
                        const channel = interaction.guild.channels.cache.get(log.id);
                        if (channel && log.events.length) set.push(`${channel}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                    }
                    const { unsetCore, unsetSpecialty, unsetOwner } = splitUnsetEvents(unset, interaction);
                    const container = buildLogsContainer({ logs: data, interaction, set, unsetCore, unsetSpecialty, unsetOwner, enabled: data.enabled, statusMessage: '❌ These events are already being logged in this channel' });
                    const enableButton = new ButtonBuilder()
                        .setCustomId('logs-enabler')
                        .setLabel(data.enabled ? 'disable' : 'enable')
                        .setStyle(data.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                    await submission.editReply({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                    });
                    return;
                }
                let query = { id: channel.id, events };
                const index = data.channels.map(d => d.id).indexOf(query.id);
                // If there is an existing channel with events in the database
                if (index != -1) data.channels[index].events.push(...query.events.filter(e => !data.channels[index].events.includes(e)));
                else data.channels.push(query);
                data = (await optimizedFindOneAndUpdate("guilds", { id: interaction.guild.id }, { $set: { "logs.channels": data.channels } }, { returnDocument: "after" })).logs;
            }
            else if (action == "unset") {
                if (!log?.events.length || !events.filter(e => log.events.includes(e)).length) return submission.followUp({ content: "None of these events are logged in the first place.", ephemeral: true });
                let query = { id: channel.id, events };
                const index = data.channels.map(d => d.id).indexOf(query.id);
                // If there's an existing channel with events in the database
                if (index != -1) {
                    data.channels[index].events = data.channels[index].events.filter(e => !query.events.includes(e));
                    // If there's no more event after being removed, remove the channel itself
                    if (!data.channels[index].events.length) data.channels.splice(index, 1);
                    data = (await optimizedFindOneAndUpdate("guilds", { id: interaction.guild.id }, { $set: { "logs.channels": data.channels } }, { returnDocument: "after" })).logs;
                }

                // data = (await mongo.logs.remove({ id: channel.id, events })).logs;
            }

            // #region buttons
            const buttons = new ActionRowBuilder().setComponents(
                new ButtonBuilder({ style: data.enabled ? ButtonStyle.Danger : ButtonStyle.Success, customId: "logs-enabler", label: data.enabled ? "disable" : "enable" }),
                new ButtonBuilder({ style: ButtonStyle.Primary, customId: "logs-set", label: "set" }),
                new ButtonBuilder({ style: ButtonStyle.Secondary, customId: "logs-unset", label: "unset" }));
            // #endregion
            // #region Getting the unset channels
            const set = [], unset = [];
            const allEventsForCheck = [...config.events, ...config.specialtyEvents];
            if (interaction.user.id === process.env.OWNER) {
                allEventsForCheck.push(...config.ownerEvents);
            }
            for (const event of allEventsForCheck) {
                const channels = (await data.channels.filter(ch => ch.events.includes(event)) ?? null).filter(ch => interaction.guild.channels.cache.get(ch.id));
                // const channels = (await mongo.logs.getByEvent(event)).filter(ch => interaction.guild.channels.cache.get(ch.id));
                if (!channels.length) unset.push(`\`${event}\``);
            }
            // #endregion
            // #region Getting the set channels
            for (const log of data.channels) {
                const channel = interaction.guild.channels.cache.get(log.id);
                if (channel && log.events.length) set.push(`${channel}: ${log.events.map(e => `\`${e}\``).join(" ")}`);
            }
            // #endregion
            let description = "";
            if (unset.length) description += `**unset**: ${unset.join(" ")}\n`;
            if (set.length) description += set.join("\n");
            const embed = new EmbedBuilder()
                .setTitle("logs")
                .setColor("ffff84")
                .setDescription(description);
            return submission.editReply({ embeds: [embed], components: [buttons] }).catch(() => null);
        }
    },
    async selectChannel(interaction) {
        try {
            // Defer the interaction immediately to prevent timeout
            await interaction.deferUpdate();

            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
            if (!hasPermission) {
                // Show demo mode if no permission
                const container = buildLogsContainer({
                    logs: { enabled: true, channels: [] },
                    interaction,
                    set: [],
                    unsetCore: [],
                    unsetSpecialty: [],
                    unsetOwner: [],
                    enabled: true,
                    hasPermission: false
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('disable')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(true);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            const selectedChannelId = interaction.values[0]; // Get the first selected channel
            // OPTIMIZED: Use cached guild data instead of direct database calls
            const guildData = await getCachedGuildData(interaction.guild.id);
            let logs = guildData.logs || { channels: [], enabled: false };

            // Check if logs is enabled for this guild
            if (!logs.enabled) {
                // Feature is disabled, don't process the selection
                const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
                const coreEvents = config.events;
                const specialtyEvents = [...config.specialtyEvents];
                const ownerEvents = [...config.ownerEvents];

                unsetCore.push(...coreEvents.map(e => `\`${e}\``));
                unsetSpecialty.push(...specialtyEvents.map(e => `\`${e}\``));
                if (interaction.user.id === process.env.OWNER) {
                    unsetOwner.push(...ownerEvents.map(e => `\`${e}\``));
                }

                const container = buildLogsContainer({
                    logs,
                    interaction,
                    set,
                    unsetCore,
                    unsetSpecialty,
                    unsetOwner,
                    enabled: false,
                    hasPermission: true
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            // Build set/unset arrays for the UI
            const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
            const coreEvents = config.events;
            const specialtyEvents = [...config.specialtyEvents];
            const ownerEvents = [...config.ownerEvents];

            // Check core events
            for (const event of coreEvents) {
                const channels = (logs.channels || []).filter(ch => {
                    if (ch.events.includes(event)) {
                        // Use synchronous check - just check if channel exists in cache
                        const channel = interaction.guild.channels.cache.get(ch.id);
                        return channel !== null;
                    }
                    return false;
                });
                if (!channels.length) unsetCore.push(`\`${event}\``);
            }

            // Check specialty events (unified groups)
            for (const unifiedEvent of specialtyEvents) {
                const isConfigured = isUnifiedSpecialtyEventConfigured(unifiedEvent, logs.channels || []);
                if (!isConfigured) {
                    unsetSpecialty.push(`\`${unifiedEvent}\``);
                }
            }

            // Check owner events (only if user is bot owner)
            if (interaction.user.id === process.env.OWNER) {
                for (const event of ownerEvents) {
                    const channels = (logs.channels || []).filter(ch => {
                        if (ch.events.includes(event)) {
                            // Use synchronous check - just check if channel exists in cache
                            const channel = interaction.guild.channels.cache.get(ch.id);
                            return channel !== null;
                        }
                        return false;
                    });
                    if (!channels.length) unsetOwner.push(`\`${event}\``);
                }
            }

            for (const log of logs.channels || []) {
                const channel = await getChannelOrThread(interaction.guild, log.id);
                if (channel && log.events.length) {
                    // Use proper thread detection - check if it's a thread type
                    const isThread = channel.type === 11 || channel.type === 12; // Public or Private thread
                    const channelDisplay = isThread ? `🧵 ${channel.name}` : `${channel}`;
                    set.push(`${channelDisplay}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                }
            }

            // Build the logs container with the selected channel
            const container = buildLogsContainer({
                logs,
                interaction,
                set,
                unsetCore,
                unsetSpecialty,
                unsetOwner,
                selectedChannelId: selectedChannelId, // Pass the selected channel ID
                enabled: logs.enabled,
                hasPermission: hasPermission
            });

            // Build enable button
            const enableButton = new ButtonBuilder()
                .setCustomId('logs-enabler')
                .setLabel(logs.enabled ? 'disable' : 'enable')
                .setStyle(logs.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');

            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });

        } catch (error) {
            console.error('[logs.selectChannel] Error:', error);
            await this.handleSelectError(interaction);
        }
    },
    async selectCoreEvents(interaction) {
        try {
            console.log('[logs.selectCoreEvents] triggered');
            await interaction.deferUpdate();

            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
            if (!hasPermission) {
                // Show demo mode if no permission
                const container = buildLogsContainer({
                    logs: { enabled: true, channels: [] },
                    interaction,
                    set: [],
                    unsetCore: [],
                    unsetSpecialty: [],
                    unsetOwner: [],
                    enabled: true,
                    hasPermission: false
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('disable')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(true);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            // Extract the selected channel from the customId
            const selectedChannelId = interaction.customId.split('-').slice(4).join('-');
            if (!selectedChannelId || selectedChannelId === 'none') {
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('No channel selected.'));
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;
            }

            // Update the selected channel's events in the DB
            // OPTIMIZED: Use cached guild data instead of direct database calls
            const guildData = await getCachedGuildData(interaction.guild.id);
            let logs = guildData.logs || { channels: [], enabled: false };

            // Check if logs is enabled for this guild
            if (!logs.enabled) {
                // Feature is disabled, don't process the selection
                const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
                const coreEvents = config.events;
                const specialtyEvents = [...config.specialtyEvents];
                const ownerEvents = [...config.ownerEvents];

                unsetCore.push(...coreEvents.map(e => `\`${e}\``));
                unsetSpecialty.push(...specialtyEvents.map(e => `\`${e}\``));
                if (interaction.user.id === process.env.OWNER) {
                    unsetOwner.push(...ownerEvents.map(e => `\`${e}\``));
                }

                const container = buildLogsContainer({
                    logs,
                    interaction,
                    set,
                    unsetCore,
                    unsetSpecialty,
                    unsetOwner,
                    selectedChannelId,
                    enabled: false,
                    hasPermission: true
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }
            let channelEntry = (logs.channels || []).find(ch => ch.id === selectedChannelId);
            if (!channelEntry) {
                channelEntry = { id: selectedChannelId, events: [] };
                if (!logs.channels) logs.channels = [];
                logs.channels.push(channelEntry);
            }

            // Merge core events with existing specialty and owner events
            const expandedSpecialtyEvents = expandSpecialtyEvents();
            const ownerEvents = [...config.ownerEvents];

            const existingSpecialtyEvents = channelEntry.events.filter(e => expandedSpecialtyEvents.includes(e));
            const existingOwnerEvents = channelEntry.events.filter(e => ownerEvents.includes(e));
            channelEntry.events = [...interaction.values, ...existingSpecialtyEvents, ...existingOwnerEvents];

            // Remove the channel entry if no events are selected
            if (channelEntry.events.length === 0) {
                logs.channels = (logs.channels || []).filter(ch => ch.id !== selectedChannelId);
            }
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'logs.channels': logs.channels } });

            // OPTIMIZED: Invalidate all related caches when channel events change
            invalidateGuildDataCache(interaction.guild.id);
            const { invalidateGuildCachingStatus, invalidateAllGuildCaches } = require('../../utils/messageCache.js');
            invalidateGuildCachingStatus(interaction.guild.id);
            invalidateAllGuildCaches(interaction.guild.id);

            // Rebuild UI with updated data
            await this.rebuildLogsUIWithChannel(interaction, logs, selectedChannelId);
            console.log('[logs.selectCoreEvents] UI updated');
        } catch (err) {
            console.error('[logs.selectCoreEvents] error:', err);
            await this.handleSelectError(interaction);
        }
    },

    async selectSpecialtyEvents(interaction) {
        try {
            console.log('[logs.selectSpecialtyEvents] triggered');
            await interaction.deferUpdate();

            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
            if (!hasPermission) {
                // Show demo mode if no permission
                const container = buildLogsContainer({
                    logs: { enabled: true, channels: [] },
                    interaction,
                    set: [],
                    unsetCore: [],
                    unsetSpecialty: [],
                    enabled: true,
                    hasPermission: false
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('disable')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(true);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            // Extract the selected channel from the customId
            const selectedChannelId = interaction.customId.split('-').slice(4).join('-');
            if (!selectedChannelId || selectedChannelId === 'none') {
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('No channel selected.'));
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;
            }

            // Update the selected channel's events in the DB
            // OPTIMIZED: Use cached guild data instead of direct database calls
            const guildData = await getCachedGuildData(interaction.guild.id);
            let logs = guildData.logs || { channels: [], enabled: false };

            // Check if logs is enabled for this guild
            if (!logs.enabled) {
                // Feature is disabled, don't process the selection
                const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
                const coreEvents = config.events;
                const specialtyEvents = [...config.specialtyEvents];
                const ownerEvents = [...config.ownerEvents];

                unsetCore.push(...coreEvents.map(e => `\`${e}\``));
                unsetSpecialty.push(...specialtyEvents.map(e => `\`${e}\``));
                if (interaction.user.id === process.env.OWNER) {
                    unsetOwner.push(...ownerEvents.map(e => `\`${e}\``));
                }

                const container = buildLogsContainer({
                    logs,
                    interaction,
                    set,
                    unsetCore,
                    unsetSpecialty,
                    unsetOwner,
                    selectedChannelId,
                    enabled: false,
                    hasPermission: true
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }
            let channelEntry = (logs.channels || []).find(ch => ch.id === selectedChannelId);
            if (!channelEntry) {
                channelEntry = { id: selectedChannelId, events: [] };
                if (!logs.channels) logs.channels = [];
                logs.channels.push(channelEntry);
            }

            // Expand unified specialty events to individual events
            const expandedSpecialtyEvents = [];
            for (const selectedEvent of interaction.values) {
                if (config.specialtyEventMappings[selectedEvent]) {
                    // This is a unified event, expand it to individual events
                    expandedSpecialtyEvents.push(...config.specialtyEventMappings[selectedEvent]);
                } else {
                    // Regular event, add as-is
                    expandedSpecialtyEvents.push(selectedEvent);
                }
            }

            // Merge expanded specialty events with existing core and owner events
            const existingCoreEvents = channelEntry.events.filter(e => config.events.includes(e));
            const existingOwnerEvents = channelEntry.events.filter(e => config.ownerEvents.includes(e));
            channelEntry.events = [...existingCoreEvents, ...existingOwnerEvents, ...expandedSpecialtyEvents];

            // Remove the channel entry if no events are selected
            if (channelEntry.events.length === 0) {
                logs.channels = (logs.channels || []).filter(ch => ch.id !== selectedChannelId);
            }
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'logs.channels': logs.channels } });

            // OPTIMIZED: Invalidate all related caches when channel events change
            invalidateGuildDataCache(interaction.guild.id);
            const { invalidateGuildCachingStatus, invalidateAllGuildCaches } = require('../../utils/messageCache.js');
            invalidateGuildCachingStatus(interaction.guild.id);
            invalidateAllGuildCaches(interaction.guild.id);

            // Rebuild UI with updated data
            await this.rebuildLogsUIWithChannel(interaction, logs, selectedChannelId);
            console.log('[logs.selectSpecialtyEvents] UI updated');
        } catch (err) {
            console.error('[logs.selectSpecialtyEvents] error:', err);
            await this.handleSelectError(interaction);
        }
    },

    async selectOwnerEvents(interaction) {
        try {
            console.log('[logs.selectOwnerEvents] triggered');
            await interaction.deferUpdate();

            // Check if user is bot owner
            if (interaction.user.id !== process.env.OWNER) {
                await this.handleSelectError(interaction);
                return;
            }

            // Extract the selected channel from the customId
            const selectedChannelId = interaction.customId.split('-').slice(4).join('-');
            if (!selectedChannelId || selectedChannelId === 'none') {
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('No channel selected.'));
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;
            }

            // Update the selected channel's events in the DB
            // OPTIMIZED: Use cached guild data instead of direct database calls
            const guildData = await getCachedGuildData(interaction.guild.id);
            let logs = guildData.logs || { channels: [], enabled: false };

            // Check if logs is enabled for this guild
            if (!logs.enabled) {
                // Feature is disabled, don't process the selection
                const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
                const coreEvents = config.events;
                const specialtyEvents = [...config.specialtyEvents];
                const ownerEvents = [...config.ownerEvents];

                unsetCore.push(...coreEvents.map(e => `\`${e}\``));
                unsetSpecialty.push(...specialtyEvents.map(e => `\`${e}\``));
                unsetOwner.push(...ownerEvents.map(e => `\`${e}\``));

                const container = buildLogsContainer({
                    logs,
                    interaction,
                    set,
                    unsetCore,
                    unsetSpecialty,
                    unsetOwner,
                    selectedChannelId,
                    enabled: false,
                    hasPermission: true
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }
            let channelEntry = (logs.channels || []).find(ch => ch.id === selectedChannelId);
            if (!channelEntry) {
                channelEntry = { id: selectedChannelId, events: [] };
                if (!logs.channels) logs.channels = [];
                logs.channels.push(channelEntry);
            }

            // Merge owner events with existing core and specialty events
            const coreEvents = [...config.events];
            const expandedSpecialtyEvents = expandSpecialtyEvents();
            const existingCoreEvents = channelEntry.events.filter(e => coreEvents.includes(e));
            const existingSpecialtyEvents = channelEntry.events.filter(e => expandedSpecialtyEvents.includes(e));
            channelEntry.events = [...interaction.values, ...existingCoreEvents, ...existingSpecialtyEvents];

            // Remove the channel entry if no events are selected
            if (channelEntry.events.length === 0) {
                logs.channels = (logs.channels || []).filter(ch => ch.id !== selectedChannelId);
            }
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'logs.channels': logs.channels } });

            // OPTIMIZED: Invalidate all related caches when channel events change
            invalidateGuildDataCache(interaction.guild.id);
            const { invalidateGuildCachingStatus, invalidateAllGuildCaches } = require('../../utils/messageCache.js');
            invalidateGuildCachingStatus(interaction.guild.id);
            invalidateAllGuildCaches(interaction.guild.id);

            // Rebuild UI with updated data
            await this.rebuildLogsUIWithChannel(interaction, logs, selectedChannelId);
            console.log('[logs.selectOwnerEvents] UI updated');
        } catch (err) {
            console.error('[logs.selectOwnerEvents] error:', err);
            await this.handleSelectError(interaction);
        }
    },

    async rebuildLogsUIWithChannel(interaction, logs, selectedChannelId) {
        // Build set/unset arrays for the UI with split categories
        const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
        const coreEvents = config.events;
        const specialtyEvents = [...config.specialtyEvents];
        const ownerEvents = [...config.ownerEvents];

        // Check core events
        for (const event of coreEvents) {
            const channels = (logs.channels || []).filter(ch => {
                if (ch.events.includes(event)) {
                    // Use synchronous check - just check if channel exists in cache
                    const channel = interaction.guild.channels.cache.get(ch.id);
                    return channel !== null;
                }
                return false;
            });
            if (!channels.length) unsetCore.push(`\`${event}\``);
        }

        // Check specialty events (unified groups)
        for (const unifiedEvent of specialtyEvents) {
            const isConfigured = isUnifiedSpecialtyEventConfigured(unifiedEvent, logs.channels || []);
            if (!isConfigured) {
                unsetSpecialty.push(`\`${unifiedEvent}\``);
            }
        }

        // Check owner events (only if user is bot owner)
        if (interaction.user.id === process.env.OWNER) {
            for (const event of ownerEvents) {
                const channels = (logs.channels || []).filter(ch => {
                    if (ch.events.includes(event)) {
                        // Use synchronous check - just check if channel exists in cache
                        const channel = interaction.guild.channels.cache.get(ch.id);
                        return channel !== null;
                    }
                    return false;
                });
                if (!channels.length) unsetOwner.push(`\`${event}\``);
            }
        }

        // Show all channels with events
        for (const log of logs.channels || []) {
            const channel = await getChannelOrThread(interaction.guild, log.id);
            if (channel && log.events.length) {
                const channelDisplay = channel.isThread() ? `🧵 ${channel.name}` : `${channel}`;
                set.push(`${channelDisplay}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
            }
        }

        const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
        const container = buildLogsContainer({
            logs,
            interaction,
            set,
            unsetCore,
            unsetSpecialty,
            unsetOwner,
            selectedChannelId,
            enabled: logs.enabled,
            hasPermission: hasPermission
        });
        const enableButton = new ButtonBuilder()
            .setCustomId('logs-enabler')
            .setLabel(logs.enabled ? 'disable' : 'enable')
            .setStyle(logs.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission);
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
        });
    },
    async rebuildLogsUI(interaction, logs, selectedChannelId) {
        // Build set/unset arrays for the UI
        const set = [], unset = [];

        // Check core events
        for (const event of config.events) {
            const channels = (logs.channels || []).filter(ch => {
                if (ch.events.includes(event)) {
                    // Use synchronous check - just check if channel exists in cache
                    const channel = interaction.guild.channels.cache.get(ch.id);
                    return channel !== null;
                }
                return false;
            });
            if (!channels.length) unset.push(`\`${event}\``);
        }

        // Check unified specialty events
        for (const unifiedEvent of config.specialtyEvents) {
            const isConfigured = isUnifiedSpecialtyEventConfigured(unifiedEvent, logs.channels || []);
            if (!isConfigured) {
                unset.push(`\`${unifiedEvent}\``);
            }
        }

        // Check owner events (only if user is bot owner)
        if (interaction.user.id === process.env.OWNER) {
            for (const event of config.ownerEvents) {
                const channels = (logs.channels || []).filter(ch => {
                    if (ch.events.includes(event)) {
                        // Use synchronous check - just check if channel exists in cache
                        const channel = interaction.guild.channels.cache.get(ch.id);
                        return channel !== null;
                    }
                    return false;
                });
                if (!channels.length) unset.push(`\`${event}\``);
            }
        }
        // Show all channels with events
        for (const log of logs.channels || []) {
            const channel = await getChannelOrThread(interaction.guild, log.id);
            if (channel && log.events.length) {
                const channelDisplay = channel.isThread() ? `🧵 ${channel.name}` : `${channel}`;
                set.push(`${channelDisplay}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
            }
        }

        const container = buildLogsContainer({ logs, interaction, set, unset, selectedChannelId, enabled: logs.enabled });
        const enableButton = new ButtonBuilder()
            .setCustomId('logs-enabler')
            .setLabel(logs.enabled ? 'disable' : 'enable')
            .setStyle(logs.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
        await interaction.editReply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
        });
    },
    async handleSelectError(interaction) {
        if (!interaction.replied && !interaction.deferred) {
            const container = new ContainerBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent('Something went wrong.'));
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
        }
    },
    async selectEvents(interaction) {
        try {
            console.log('[logs.selectEvents] triggered');
            await interaction.deferUpdate();

            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
            if (!hasPermission) {
                // Show demo mode if no permission
                const container = buildLogsContainer({
                    logs: { enabled: true, channels: [] },
                    interaction,
                    set: [],
                    unset: [],
                    enabled: true,
                    hasPermission: false
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('disable')
                    .setStyle(ButtonStyle.Danger)
                    .setDisabled(true);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }

            // Extract the selected channel from the customId
            const selectedChannelId = interaction.customId.split('-').slice(3).join('-');
            if (!selectedChannelId || selectedChannelId === 'none') {
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('No channel selected.'));
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
                return;
            }

            // Update the selected channel's events in the DB
            // OPTIMIZED: Use cached guild data instead of direct database calls
            const guildData = await getCachedGuildData(interaction.guild.id);
            let logs = guildData.logs || { channels: [], enabled: false };

            // Check if logs is enabled for this guild
            if (!logs.enabled) {
                // Feature is disabled, don't process the selection
                const set = [], unset = [];
                let allEvents = [...config.events, ...config.specialtyEvents];
                if (interaction.user.id === process.env.OWNER) {
                    allEvents = [...allEvents, ...config.ownerEvents];
                }
                unset.push(...allEvents.map(e => `\`${e}\``));

                const container = buildLogsContainer({
                    logs,
                    interaction,
                    set,
                    unset,
                    selectedChannelId,
                    enabled: false
                });
                const enableButton = new ButtonBuilder()
                    .setCustomId('logs-enabler')
                    .setLabel('enable')
                    .setStyle(ButtonStyle.Success);
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
                });
                return;
            }
            let channelEntry = (logs.channels || []).find(ch => ch.id === selectedChannelId);
            if (!channelEntry) {
                channelEntry = { id: selectedChannelId, events: [] };
                if (!logs.channels) logs.channels = [];
                logs.channels.push(channelEntry);
            }
            // Expand unified specialty events to individual events
            const expandedEvents = [];
            for (const selectedEvent of interaction.values) {
                if (config.specialtyEventMappings[selectedEvent]) {
                    // This is a unified specialty event, expand it to individual events
                    expandedEvents.push(...config.specialtyEventMappings[selectedEvent]);
                } else {
                    // Regular event, add as-is
                    expandedEvents.push(selectedEvent);
                }
            }
            channelEntry.events = expandedEvents;
            // Remove the channel entry if no events are selected
            if (channelEntry.events.length === 0) {
                logs.channels = (logs.channels || []).filter(ch => ch.id !== selectedChannelId);
            }
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'logs.channels': logs.channels } });

            // OPTIMIZED: Invalidate all related caches when channel events change
            invalidateGuildDataCache(interaction.guild.id);
            const { invalidateGuildCachingStatus, invalidateAllGuildCaches } = require('../../utils/messageCache.js');
            invalidateGuildCachingStatus(interaction.guild.id);
            invalidateAllGuildCaches(interaction.guild.id);

            // Build set/unset arrays for the UI
            const set = [], unset = [];
            const allEvents = [...config.events, ...config.specialtyEvents];
            if (interaction.user.id === process.env.OWNER) {
                allEvents.push(...config.ownerEvents);
            }
            for (const event of allEvents) {
                const channels = (logs.channels || []).filter(ch => {
                    if (ch.events.includes(event)) {
                        // Use synchronous check - just check if channel exists in cache
                        const channel = interaction.guild.channels.cache.get(ch.id);
                        return channel !== null;
                    }
                    return false;
                });
                if (!channels.length) unset.push(`\`${event}\``);
            }
            // Show all channels with events
            for (const log of logs.channels || []) {
                const channel = await getChannelOrThread(interaction.guild, log.id);
                if (channel && log.events.length) {
                    const channelDisplay = channel.isThread() ? `🧵 ${channel.name}` : `${channel}`;
                    set.push(`${channelDisplay}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                }
            }

            const container = buildLogsContainer({ logs, interaction, set, unset, selectedChannelId, enabled: logs.enabled });
            const enableButton = new ButtonBuilder()
                .setCustomId('logs-enabler')
                .setLabel(logs.enabled ? 'disable' : 'enable')
                .setStyle(logs.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            console.log('[logs.selectEvents] UI updated');
        } catch (err) {
            console.error('[logs.selectEvents] error:', err);
            if (!interaction.replied && !interaction.deferred) {
                const container = new ContainerBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('Something went wrong.'));
                await interaction.editReply({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
            }
        }
    },
    async toggleEnabled(interaction) {
        // OPTIMIZED: Use cached guild data instead of direct database calls
        const guildData = await getCachedGuildData(interaction.guild.id);
        let logs = guildData.logs || { channels: [], enabled: false };
        logs.enabled = !logs.enabled;
        await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'logs.enabled': logs.enabled } });

        // OPTIMIZED: Invalidate all related caches when logging config changes
        invalidateGuildDataCache(interaction.guild.id);
        const { invalidateGuildCachingStatus, invalidateAllGuildCaches } = require('../../utils/messageCache.js');
        invalidateGuildCachingStatus(interaction.guild.id);
        invalidateAllGuildCaches(interaction.guild.id);
        // Get set/unset channels with split categories
        const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
        const coreEvents = config.events;
        const specialtyEvents = [...config.specialtyEvents];
        const ownerEvents = [...config.ownerEvents];

        // Check core events
        for (const event of coreEvents) {
            const channels = (logs.channels || []).filter(ch => ch.events.includes(event) && interaction.guild.channels.cache.get(ch.id));
            if (!channels.length) unsetCore.push(`\`${event}\``);
        }

        // Check specialty events (unified groups)
        for (const unifiedEvent of specialtyEvents) {
            const isConfigured = isUnifiedSpecialtyEventConfigured(unifiedEvent, logs.channels || []);
            if (!isConfigured) {
                unsetSpecialty.push(`\`${unifiedEvent}\``);
            }
        }

        // Check owner events (only if user is bot owner)
        if (interaction.user.id === process.env.OWNER) {
            for (const event of ownerEvents) {
                const channels = (logs.channels || []).filter(ch => ch.events.includes(event) && interaction.guild.channels.cache.get(ch.id));
                if (!channels.length) unsetOwner.push(`\`${event}\``);
            }
        }

        for (const log of logs.channels || []) {
            const channel = interaction.guild.channels.cache.get(log.id);
            if (channel && log.events.length) set.push(`${channel}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
        }
        const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
        const container = buildLogsContainer({ logs, interaction, set, unsetCore, unsetSpecialty, unsetOwner, enabled: logs.enabled, hasPermission });
        const enableButton = new ButtonBuilder()
            .setCustomId('logs-enabler')
            .setLabel(logs.enabled ? 'disable' : 'enable')
            .setStyle(logs.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission);

        const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
        });
    },






    async showMainInterface(interaction) {
        try {
            // Defer the interaction first to prevent timeout during cleanup
            if (!interaction.deferred && !interaction.replied) {
                await interaction.deferUpdate();
            }

            // Get current logs data
            // OPTIMIZED: Use cached guild data instead of direct database calls
            const guildData = await getCachedGuildData(interaction.guild.id);
            let logs = guildData.logs || { channels: [], enabled: false };

            // Build set/unset arrays for main container
            const set = [], unsetCore = [], unsetSpecialty = [], unsetOwner = [];
            const coreEvents = config.events;
            const specialtyEvents = [...config.specialtyEvents];
            const ownerEvents = [...config.ownerEvents];

            // Check which core events are unset
            for (const event of coreEvents) {
                let eventHasChannel = false;
                for (const ch of logs.channels || []) {
                    if (ch.events.includes(event)) {
                        eventHasChannel = true;
                        break;
                    }
                }
                if (!eventHasChannel) unsetCore.push(`\`${event}\``);
            }

            // Check which specialty events are unset (unified groups)
            for (const unifiedEvent of specialtyEvents) {
                const isConfigured = isUnifiedSpecialtyEventConfigured(unifiedEvent, logs.channels || []);
                if (!isConfigured) {
                    unsetSpecialty.push(`\`${unifiedEvent}\``);
                }
            }

            // Check which owner events are unset (only if user is bot owner)
            if (interaction.user.id === process.env.OWNER) {
                for (const event of ownerEvents) {
                    let eventHasChannel = false;
                    for (const ch of logs.channels || []) {
                        if (ch.events.includes(event)) {
                            eventHasChannel = true;
                            break;
                        }
                    }
                    if (!eventHasChannel) unsetOwner.push(`\`${event}\``);
                }
            }

            // Process valid channels/threads for display
            for (const log of logs.channels || []) {
                const channel = await getChannelOrThread(interaction.guild, log.id);
                if (channel && log.events.length) {
                    // Add thread emoji for threads
                    const channelDisplay = channel.isThread() ? `🧵 ${channel.name}` : `${channel}`;
                    set.push(`${channelDisplay}: ${log.events.map(e => `\`${e}\``).join(' ')}`);
                }
            }

            // Build main logs interface
            const hasPermission = global.hasFeaturePermission(interaction.member, 'logs');
            const mainContainer = buildLogsContainer({
                logs,
                interaction,
                set,
                unsetCore,
                unsetSpecialty,
                unsetOwner,
                enabled: logs.enabled,
                hasPermission: hasPermission
            });

            // Build enable button only
            const enableButton = new ButtonBuilder()
                .setCustomId('logs-enabler')
                .setLabel(logs.enabled ? 'disable' : 'enable')
                .setStyle(logs.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission); // Only disabled for demo users

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'logs');
            const buttonRow = new ActionRowBuilder().addComponents(enableButton);

            // Use editReply since we deferred the interaction
            await interaction.editReply({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, mainContainer, buttonRow]
            });

        } catch (error) {
            console.error('[logs.showMainInterface] Error:', error);
            await this.handleSelectError(interaction);
        }
    },



    // Export the helper function for use in other logging files
    sendToLogChannel: sendToLogChannel,

    // Export container builder for testing purposes
    buildLogsContainer: buildLogsContainer
};