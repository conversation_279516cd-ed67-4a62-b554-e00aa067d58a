const { <PERSON><PERSON><PERSON>ommandBuilder, PresenceUpdateStatus, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle, ActivityType, ModalBuilder, TextInputBuilder, TextInputStyle, PermissionFlagsBits } = require('discord.js');
const { EmbedBuilder } = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { defaults } = require("../../utils/default_db_structures.js");
const config = require("../../config.js");

module.exports = {
    //data: new SlashCommandBuilder()
    //    .setName("instantiate")
    //    .setDescription("instantiate indefinite buttons for owner")
    //    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
        // .setDMPermission(false),
    async execute(interaction, options) {
        const image = "https://cdn.discordapp.com/attachments/1060064351203098704/1060069968428945438/bot.png";
        const buttons = new ActionRowBuilder().setComponents(
            new ButtonBuilder({ customId: "info", label: "info", style: ButtonStyle.Secondary }),
            // new ButtonBuilder({ url: "https://discord.com/api/oauth2/authorize?client_id=331669060221796362&permissions=0&scope=bot%20applications.commands", label: "invite", style: ButtonStyle.Link }),
        );

        interaction.reply({ content: "sent, feel free to delete this command when you're done", ephemeral: true });
        interaction.channel.send({ content: image, components: [buttons] });

    }
};