const { Container<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags, SeparatorBuilder, SeparatorSpacingSize, StringSelectMenuBuilder, TextInputBuilder, ModalBuilder, TextInputStyle } = require('discord.js');
const { optimizedFindOne, optimizedUpdateOne, optimizedUpdateMany, optimizedDeleteOne } = require('../../utils/database-optimizer.js');
const { sendFeatureToggleLog, sendExpLevelCreatedLog, sendExpLevelEditedLog } = require("../../utils/sendLog.js");
const { getRecentImagesFromChannel, uploadImageAsEmote, buildImageSelectMenu, buildNoImagesSelectMenu } = require('../../utils/imageUploader.js');
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { REST } = require('@discordjs/rest');
const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);
const { getExpDemoData } = require("../../utils/demoData.js");
const { logger } = require('../../utils/consoleLogger.js');
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');

/**
 * Experience System (Enterprise-Grade Performance Optimized)
 * Handles all experience management with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const expMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    configsProcessed: 0,
    levelsProcessed: 0,
    tempStatesProcessed: 0,
    uiBuildsProcessed: 0,
    togglesProcessed: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildExpConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild exp configurations
const expTempStateCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for temporary states
const expMainMsgCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for main message tracking
const expLevelDataCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for level calculations
const expChannelTempCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for channel temp states

// Register caches for global cleanup
registerCache(guildExpConfigCache);
registerCache(expTempStateCache);
registerCache(expMainMsgCache);
registerCache(expLevelDataCache);
registerCache(expChannelTempCache);

/**
 * Get cached guild experience configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild experience configuration data
 */
async function getCachedGuildExpConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_exp_config_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildExpConfigCache.get(cacheKey);
        if (cached) {
            expMetrics.cacheHits++;
            if (expMetrics.verboseLogging) {
                console.log(`[exp] ⚡ Guild exp config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expMetrics.cacheMisses++;
        expMetrics.databaseQueries++;
        expMetrics.configsProcessed++;

        // Get guild data
        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const config = guildData || { exp: { enabled: true } };

        // Ensure exp structure exists
        if (!config.exp) {
            config.exp = { enabled: true };
        }

        // Cache the result
        guildExpConfigCache.set(cacheKey, config);

        const duration = Date.now() - startTime;
        expMetrics.averageQueryTime =
            (expMetrics.averageQueryTime * (expMetrics.databaseQueries - 1) + duration) /
            expMetrics.databaseQueries;

        if (expMetrics.verboseLogging || duration > 100) {
            console.log(`[exp] ✅ Guild exp config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return config;
    } catch (error) {
        console.error(`[exp] ❌ Error getting guild exp config for ${guildId}:`, error);
        return { exp: { enabled: true } };
    }
}

/**
 * Get cached temporary state data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for temp state lookups
 * @param {string} collection - Collection name (exp_create_level_temp, exp_level_channel_temp, etc.)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Temporary state data
 */
async function getCachedTempState(collection, userId, guildId) {
    const startTime = Date.now();
    const cacheKey = `temp_state_${collection}_${userId}_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = expTempStateCache.get(cacheKey);
        if (cached) {
            expMetrics.cacheHits++;
            if (expMetrics.verboseLogging) {
                console.log(`[exp] ⚡ Temp state cache hit for ${collection} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expMetrics.cacheMisses++;
        expMetrics.databaseQueries++;
        expMetrics.tempStatesProcessed++;

        // Get temp state data
        const tempData = await optimizedFindOne(collection, { userId, guildId });

        // Cache the result (including null results to avoid repeated queries)
        expTempStateCache.set(cacheKey, tempData);

        const duration = Date.now() - startTime;
        expMetrics.averageQueryTime =
            (expMetrics.averageQueryTime * (expMetrics.databaseQueries - 1) + duration) /
            expMetrics.databaseQueries;

        if (expMetrics.verboseLogging || duration > 50) {
            console.log(`[exp] ✅ Temp state fetched for ${collection}: ${duration}ms - cached for future access`);
        }

        return tempData;
    } catch (error) {
        console.error(`[exp] ❌ Error getting temp state for ${collection}:`, error);
        return null;
    }
}

/**
 * Get cached main message data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for main message tracking
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Main message data
 */
async function getCachedMainMsg(guildId) {
    const startTime = Date.now();
    const cacheKey = `main_msg_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = expMainMsgCache.get(cacheKey);
        if (cached) {
            expMetrics.cacheHits++;
            if (expMetrics.verboseLogging) {
                console.log(`[exp] ⚡ Main msg cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expMetrics.cacheMisses++;
        expMetrics.databaseQueries++;

        // Get main message data
        const mainMsgData = await optimizedFindOne("exp_main_msgs", { guildId });

        // Cache the result
        expMainMsgCache.set(cacheKey, mainMsgData);

        const duration = Date.now() - startTime;
        expMetrics.averageQueryTime =
            (expMetrics.averageQueryTime * (expMetrics.databaseQueries - 1) + duration) /
            expMetrics.databaseQueries;

        if (expMetrics.verboseLogging || duration > 50) {
            console.log(`[exp] ✅ Main msg data fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return mainMsgData;
    } catch (error) {
        console.error(`[exp] ❌ Error getting main msg data for ${guildId}:`, error);
        return null;
    }
}

// Set the maximum number of levels (roles) allowed (excluding Level 0)
// Level 0 is the base level (0 EXP) and doesn't count toward this limit
// So MAX_LEVELS = 5 allows: Level 0 + Levels 1-5 = 6 total levels
const MAX_LEVELS = 5; // Change this value to adjust the limit

/**
 * Get comprehensive exp system performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive exp system performance data
 */
function getExpSystemStats() {
    const cacheHitRate = expMetrics.cacheHits + expMetrics.cacheMisses > 0 ?
        (expMetrics.cacheHits / (expMetrics.cacheHits + expMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: expMetrics.cacheHits,
            cacheMisses: expMetrics.cacheMisses,
            databaseQueries: expMetrics.databaseQueries,
            averageQueryTime: `${expMetrics.averageQueryTime.toFixed(2)}ms`,
            configsProcessed: expMetrics.configsProcessed,
            levelsProcessed: expMetrics.levelsProcessed,
            tempStatesProcessed: expMetrics.tempStatesProcessed,
            uiBuildsProcessed: expMetrics.uiBuildsProcessed,
            togglesProcessed: expMetrics.togglesProcessed,
            lastOptimization: new Date(expMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildExpConfig: guildExpConfigCache.getStats(),
            expTempState: expTempStateCache.getStats(),
            expMainMsg: expMainMsgCache.getStats(),
            expLevelData: expLevelDataCache.getStats(),
            expChannelTemp: expChannelTempCache.getStats()
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    expMetrics.lastOptimization = Date.now();

    const stats = getExpSystemStats();
    if (expMetrics.verboseLogging) {
        console.log(`[exp] 📊 Performance Report:`);
        console.log(`[exp]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[exp]   Configs Processed: ${stats.performance.configsProcessed}`);
        console.log(`[exp]   Levels Processed: ${stats.performance.levelsProcessed}`);
        console.log(`[exp]   Temp States Processed: ${stats.performance.tempStatesProcessed}`);
        console.log(`[exp]   UI Builds Processed: ${stats.performance.uiBuildsProcessed}`);
        console.log(`[exp]   Toggles Processed: ${stats.performance.togglesProcessed}`);
        console.log(`[exp]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[exp]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Invalidate guild experience configuration cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildExpConfigCache(guildId) {
    const cacheKey = `guild_exp_config_${guildId}`;
    guildExpConfigCache.delete(cacheKey);

    if (expMetrics.verboseLogging) {
        console.log(`[exp] 🗑️ Invalidated guild exp config cache for ${guildId}`);
    }
}

/**
 * Invalidate temporary state cache
 * @param {string} collection - Collection name
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 */
function invalidateTempStateCache(collection, userId, guildId) {
    const cacheKey = `temp_state_${collection}_${userId}_${guildId}`;
    expTempStateCache.delete(cacheKey);

    if (expMetrics.verboseLogging) {
        console.log(`[exp] 🗑️ Invalidated temp state cache for ${collection}`);
    }
}

/**
 * Invalidate main message cache
 * @param {string} guildId - Guild ID
 */
function invalidateMainMsgCache(guildId) {
    const cacheKey = `main_msg_${guildId}`;
    expMainMsgCache.delete(cacheKey);

    if (expMetrics.verboseLogging) {
        console.log(`[exp] 🗑️ Invalidated main msg cache for ${guildId}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, expMetrics.performanceReportInterval);

// Helper function to safely check permissions
function hasExpPermission(member) {
    return global.hasFeaturePermission ? global.hasFeaturePermission(member, 'exp') : true;
}

// Helper function to delete old emote from Discord
async function deleteOldEmote(emoteString, client) {
    if (!emoteString || !client) return;

    try {
        // Extract emote ID from string format: <:name:id> or <a:name:id>
        const emoteMatch = emoteString.match(/<a?:[\w\d_]+:(\d+)>/);
        if (!emoteMatch) return;

        const emoteId = emoteMatch[1];

        // Try to delete the application emote
        await client.application.emojis.delete(emoteId);
        console.log(`[deleteOldEmote] Successfully deleted emote ${emoteId}`);
    } catch (error) {
        // Don't throw error - emote might already be deleted or not exist
        console.log(`[deleteOldEmote] Could not delete emote: ${error.message}`);
    }
}



// This function is now replaced by the unified demo data system

// Build the EXP container with all UI logic
async function buildExpContainer({ subcomponent = 'levels', enabled = true, guildData = null, guild = null, statusMessage = null, hasPermission = true, member = null, commandChannel = null, user = null, selectedLevelOption = null }) {
    // Use demo data if user doesn't have permission
    if (!hasPermission && guild && member) {
        guildData = { exp: getExpDemoData(guild, member, commandChannel) };
        enabled = true; // Show demo as enabled
    }
    // Heading and description
    const heading = new TextDisplayBuilder().setContent('# exp');
    const description = new TextDisplayBuilder().setContent('> be active and get exp to get roles and items');

    // Subcomponent select menu (do not show the current selection)
    const allSubcomponents = [
        { label: 'levels', description: 'configure level roles and exp requirements', value: 'levels' },
        { label: 'text', description: 'earn exp from chatting', value: 'text' },
        { label: 'voice', description: 'earn exp from voice channels', value: 'voice' }
    ];
    const subcomponentOptions = allSubcomponents.filter(opt => opt.value !== subcomponent);
    const subcomponentSelect = new StringSelectMenuBuilder()
        .setCustomId('exp-subcomponent-select')
        .setPlaceholder(subcomponent)
        .addOptions(subcomponentOptions.slice(0, 25))
        .setDisabled(!enabled); // Keep navigation enabled even without permission
    const subcomponentRow = new ActionRowBuilder().addComponents(subcomponentSelect);

    // Always define these for config dropdown
    const levelsEnabled = guildData?.exp?.levelsEnabled ?? true;
    const levelMsgEnabled = guildData?.exp?.levelMsgEnabled ?? true;
    const levelChannel = guildData?.exp?.levelChannel ?? null;
    const levelMsg = guildData?.exp?.levelMsg ?? '{mention} leveled up to level {level} and received the {role} role.';
    const levels = guildData?.exp?.levels ?? [];

    // Subcomponent display
    let subcomponentContent;
    if (subcomponent === 'levels') {
        let levelsText = '';
        if (levels.length === 0) {
            levelsText = '__no levels created__';
        } else {
            levelsText = levels.map((lvl, i) => {
                // Level 0 if first level has 0 exp, otherwise level number starts from 1
                const levelNumber = (i === 0 && lvl.exp === 0) ? 0 : i;
                const levelIcon = lvl.levelIcon ? `${lvl.levelIcon} ` : '';
                // Use Discord role mention format for main display
                const roleMention = guild && guild.roles && guild.roles.cache.get(lvl.roleId)
                    ? `<@&${lvl.roleId}>`
                    : '<@&deleted-role>';
                return `level ${levelNumber}: ${levelIcon}${roleMention} ${lvl.exp} exp`;
            }).join('\n');
        }
        const levelsHeading = new TextDisplayBuilder().setContent(
            '## levels\n> select a role and exp requirement per level'
        );
        const levelsDetails = new TextDisplayBuilder().setContent(
            `**level msg:** ${levelMsgEnabled ? 'enabled' : 'disabled'}\n` +
            `**level channel:** ${levelChannel ? '<#' + levelChannel + '>' : 'not set'}\n` +
            `**msg:** ${levelMsg}`
        );
        const levelsList = new TextDisplayBuilder().setContent(levelsText);
        subcomponentContent = [levelsHeading, levelsDetails, levelsList];
    } else if (subcomponent === 'text') {
        // --- TEXT SUBCOMPONENT UI ---
        // Defaults
        const textExpPerMin = guildData?.exp?.text?.expPerMin ?? 10;
        const textCooldown = guildData?.exp?.text?.cooldown ?? 1;
        const textMinChars = guildData?.exp?.text?.minChars ?? 10;
        const textHeading = new TextDisplayBuilder().setContent('## text\n> earn exp from chatting');
        const textDetails = new TextDisplayBuilder().setContent(
            `**exp / cd:** ${textExpPerMin}\n` +
            `**cd:** ${textCooldown} min\n` +
            `**min chars:** ${textMinChars}`
        );
        subcomponentContent = [textHeading, textDetails];
    } else if (subcomponent === 'voice') {
        // --- VOICE SUBCOMPONENT UI ---
        // Defaults
        const voiceExpPerMin = guildData?.exp?.voice?.expPerMin ?? 2;
        const voiceCooldown = guildData?.exp?.voice?.cooldown ?? 1;
        const voiceMsgEnabled = guildData?.exp?.voice?.msgEnabled ?? true;
        const voiceMsg = guildData?.exp?.voice?.msg ?? '{mention} spent {duration} in voice and earned {exp} exp.';
        
        const voiceHeading = new TextDisplayBuilder().setContent('## voice\n> earn exp from voice channels');
        const voiceDetails = new TextDisplayBuilder().setContent(
            `**exp / cd:** ${voiceExpPerMin}\n` +
            `**cd:** ${voiceCooldown} min\n` +
            `**exp msg:** ${voiceMsgEnabled ? 'enabled' : 'disabled'}\n` +
            `**msg:** ${voiceMsg}`
        );
        subcomponentContent = [voiceHeading, voiceDetails];
    }

    // Config dropdown for levels
    let configOptions = [];
    if (subcomponent === 'levels') {
        // Level message toggle
        configOptions.push({
            label: 'level message',
            description: levelMsgEnabled ? 'Currently enabled' : 'Currently disabled',
            value: 'level-msg'
        });
        // Level channel - keep visible even when channel select is revealed
        configOptions.push({
            label: 'level channel',
            description: levelChannel && guild
                ? `#${guild.channels.cache.get(levelChannel)?.name || 'deleted-channel'}`
                : 'not set',
            value: 'level-channel',
            default: selectedLevelOption === 'level-channel' // Keep selected when channel select is revealed
        });
        // Level up message - only show to bot owner
        if (user && user.id === process.env.OWNER) {
            let msgDesc = levelMsg;
            if (msgDesc.length > 50) msgDesc = msgDesc.slice(0, 47) + '...';
            configOptions.push({
                label: 'msg',
                description: msgDesc,
                value: 'level-msg-template'
            });
        }
        // Add new level (only if under the limit)
        // Level 0 doesn't count toward the MAX_LEVELS limit since it's the base level
        const nonZeroLevels = levels.filter(level => level.exp > 0).length;
        if (nonZeroLevels < MAX_LEVELS) {
            configOptions.push({
                label: 'add new level',
                value: 'add-level'
            });
        }
        // Each level
        for (let i = 0; i < levels.length; i++) {
            const lvl = levels[i];
            let roleName = lvl.roleId;
            if (guild) {
                const role = guild.roles.cache.get(lvl.roleId);
                if (role) roleName = role.name;
            }
            // Show level 0 for first level if it has 0 exp, otherwise start from 1
            const levelNumber = (i === 0 && lvl.exp === 0) ? 0 : i;

            // Extract emoji name from levelIcon (e.g., "<hole:342345234253235>" -> "hole")
            let emojiName = '';
            if (lvl.levelIcon) {
                const emojiMatch = lvl.levelIcon.match(/<:([^:]+):/);
                if (emojiMatch) {
                    emojiName = emojiMatch[1] + ' '; // Add space after emoji name
                } else {
                    // Handle unicode emojis or plain text
                    emojiName = lvl.levelIcon + ' ';
                }
            }

            configOptions.push({
                label: `level ${levelNumber}`,
                description: `${emojiName}@${roleName} ${lvl.exp} exp`,
                value: `level-${i+1}` // Keep internal value as i+1 for compatibility
            });
        }
    } else if (subcomponent === 'text') {
        // --- TEXT CONFIG OPTIONS ---
        const textExpPerMin = guildData?.exp?.text?.expPerMin ?? 1; // Use standardized default
        const textCooldown = guildData?.exp?.text?.cooldown ?? 1;
        const textMinChars = guildData?.exp?.text?.minChars ?? 4; // Use standardized default

        // Only show options to bot owner (text config is bot owner only)
        if (user && user.id === process.env.OWNER) {
            configOptions.push({
                label: 'exp / cd',
                description: String(textExpPerMin),
                value: 'text-exp-per-min'
            });
            configOptions.push({
                label: 'cd',
                description: String(textCooldown),
                value: 'text-cooldown'
            });
            configOptions.push({
                label: 'min chars',
                description: String(textMinChars),
                value: 'text-min-chars'
            });
        } else {
            // No options for non-bot owners - select menu will be disabled
            configOptions.push({
                label: 'bot owner only',
                description: 'Text EXP configuration requires bot owner permissions',
                value: 'text-no-access'
            });
        }
    } else if (subcomponent === 'voice') {
        // --- VOICE CONFIG OPTIONS ---
        const voiceExpPerMin = guildData?.exp?.voice?.expPerMin ?? 1; // Use standardized default
        const voiceCooldown = guildData?.exp?.voice?.cooldown ?? 1;
        const voiceMsgEnabled = guildData?.exp?.voice?.msgEnabled ?? true;
        const voiceMsg = guildData?.exp?.voice?.msg ?? '{mention} spent {duration} in voice and earned {exp} exp.';

        // Only show EXP rate options to bot owner
        if (user && user.id === process.env.OWNER) {
            configOptions.push({
                label: 'exp / cd',
                description: String(voiceExpPerMin),
                value: 'voice-exp-per-min'
            });
            configOptions.push({
                label: 'cd',
                description: String(voiceCooldown),
                value: 'voice-cooldown'
            });
        }

        // Voice message toggle is available to all admins
        configOptions.push({
            label: 'exp msg',
            description: voiceMsgEnabled ? 'Currently enabled' : 'Currently disabled',
            value: 'voice-msg-toggle'
        });

        // Voice message template only visible to bot owner
        if (user && user.id === process.env.OWNER) {
            configOptions.push({
                label: 'msg',
                description: voiceMsg.length > 50 ? voiceMsg.slice(0, 47) + '...' : voiceMsg,
                value: 'voice-msg-template'
            });
        }
    } else {
        // Placeholder for other subcomponents
        configOptions = [
            { label: 'coming soon', value: 'soon' }
        ];
    }
    // Determine if config should be disabled
    let configDisabled = !enabled || !hasPermission; // Disabled if global EXP is off OR no permission
    if (subcomponent === 'text') {
        const textEnabled = guildData?.exp?.text?.enabled ?? true;
        const isBotOwner = user && user.id === process.env.OWNER;
        configDisabled = !enabled || !textEnabled || !hasPermission || !isBotOwner; // Also disabled if not bot owner
    } else if (subcomponent === 'voice') {
        const voiceEnabled = guildData?.exp?.voice?.enabled ?? true;
        configDisabled = !enabled || !voiceEnabled || !hasPermission; // Also disabled if voice EXP is off OR no permission
    }

    const configSelect = new StringSelectMenuBuilder()
        .setCustomId(subcomponent === 'text' ? 'exp-text-config' :
                    subcomponent === 'voice' ? 'exp-voice-config' : 'exp-levels-config')
        .setPlaceholder('config')
        .addOptions(configOptions.slice(0, 25))
        .setDisabled(configDisabled);
    const configRow = new ActionRowBuilder().addComponents(configSelect);

    // Add channel select if revealed for level channel configuration
    let channelSelectRow = null;
    if (subcomponent === 'levels' && selectedLevelOption === 'level-channel') {
        const currentChannel = guildData?.exp?.levelChannel || null;
        const channelSelect = new (require('discord.js').ChannelSelectMenuBuilder)()
            .setCustomId('exp-levels-channel-select')
            .setPlaceholder('select level channel')
            .addChannelTypes(0)
            .setMinValues(1)
            .setMaxValues(1);
        if (currentChannel) channelSelect.setDefaultChannels(currentChannel);
        channelSelectRow = new ActionRowBuilder().addComponents(channelSelect);
    }

    // Per-subcomponent enable/disable button (remove levels button since it's useless)
    let subcomponentButton = null;
    if (subcomponent === 'text') {
        const textEnabled = guildData?.exp?.text?.enabled ?? true;
        subcomponentButton = new ButtonBuilder()
            .setCustomId(textEnabled ? 'exp-text-disable' : 'exp-text-enable')
            .setLabel(textEnabled ? 'disable' : 'enable')
            .setStyle(textEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!enabled || !hasPermission); // Disabled if global EXP is off OR no permission
    } else if (subcomponent === 'voice') {
        const voiceEnabled = guildData?.exp?.voice?.enabled ?? true;
        subcomponentButton = new ButtonBuilder()
            .setCustomId(voiceEnabled ? 'exp-voice-disable' : 'exp-voice-enable')
            .setLabel(voiceEnabled ? 'disable' : 'enable')
            .setStyle(voiceEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!enabled || !hasPermission); // Disabled if global EXP is off OR no permission
    }
    // Separator logic
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    const smallSeparator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false);

    // Build the container in the correct order
    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, description)
        .addActionRowComponents(subcomponentRow)
        .addTextDisplayComponents(...subcomponentContent)
        .addSeparatorComponents(smallSeparator)
        .addActionRowComponents(configRow);

    // Add channel select row if revealed
    if (channelSelectRow) {
        container.addActionRowComponents(channelSelectRow);
    }

    container.addSeparatorComponents(smallSeparator);

    // Add status message at the bottom if present (temporary feedback)
    if (statusMessage) {
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${statusMessage}`);
        container.addTextDisplayComponents(statusDisplay);
    }

    // Only add subcomponent button if it exists (not for levels)
    if (subcomponentButton) {
        const subcomponentButtonRow = new ActionRowBuilder().addComponents(subcomponentButton);
        container.addActionRowComponents(subcomponentButtonRow);
    }

    container.setAccentColor(OPERATION_COLORS.NEUTRAL);

    return container;
}

// Import centralized features menu
const { buildSelectMenu } = require('./featuresMenu');

// Main select handler
async function select(interaction) {
    console.log('Select handler triggered:', interaction.customId, interaction.values);
    
    if (interaction.customId === '17-select') {
        // Handle main feature selection
        const selected = interaction.values[0];
        if (selected === 'exp') {
            // Check permissions
            const hasPermission = hasExpPermission(interaction.member);

            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { exp: { enabled: true } };
            if (!guildData.exp) guildData.exp = { enabled: true };

            const container = await buildExpContainer({
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission); // Disable if no permission
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);

            const mainMsg = await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow],
                fetchReply: true
            });
            // Store main EXP container message ID in MongoDB
            await optimizedUpdateOne("exp_main_msgs",
                { guildId: interaction.guild.id },
                { $set: { messageId: mainMsg.id, updatedAt: new Date() } },
                { upsert: true }
            );
            return;
        }
    } else if (interaction.customId === 'exp-subcomponent-select') {
        const selected = interaction.values[0];
        console.log('Selected subcomponent:', selected);

        // Check permissions
        const hasPermission = hasExpPermission(interaction.member);
        if (!hasPermission) {
            // Show demo mode if no permission - include the selected subcomponent
            const container = await buildExpContainer({
                subcomponent: selected,
                enabled: true,
                hasPermission: false,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const toggleButton = new ButtonBuilder()
                .setCustomId('exp-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)]
            });
            return;
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };

        // Check if EXP is enabled for this guild
        if (!guildData.exp.enabled) {
            // Feature is disabled, don't process the selection
            const container = await buildExpContainer({
                enabled: false,
                guildData,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const enableButton = new ButtonBuilder()
                .setCustomId('exp-global-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        }

        console.log('Building container for subcomponent:', selected);
        const container = await buildExpContainer({
            subcomponent: selected,
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user
        });
        console.log('Container built successfully');

        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission); // Disable if no permission
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);

        try {
            console.log('Attempting to update message');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            console.log('Message updated successfully');
        } catch (error) {
            console.error('Error updating message:', error);
            await interaction.reply({
                content: `Failed to update message: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    } else if (interaction.customId === 'exp-levels-config') {
        // Check permissions
        const hasPermission = hasExpPermission(interaction.member);
        if (!hasPermission) {
            // Show demo mode if no permission
            const container = await buildExpContainer({
                enabled: true,
                hasPermission: false,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const toggleButton = new ButtonBuilder()
                .setCustomId('exp-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)]
            });
            return;
        }

        // Check if EXP is enabled for this guild
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };

        if (!guildData.exp.enabled) {
            // Feature is disabled, don't process the selection
            const container = await buildExpContainer({
                enabled: false,
                guildData,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const enableButton = new ButtonBuilder()
                .setCustomId('exp-global-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        }

        const selected = interaction.values[0];
        console.log('[exp-levels-config] Selected:', selected);
        if (selected === 'level-channel') {
            // Show channel select menu (cascading) - keep the level-channel option visible
            // This should NOT rebuild the entire interface, just reveal the channel select
            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { exp: { enabled: true } };
            if (!guildData.exp) guildData.exp = { enabled: true };
            const hasPermission = hasExpPermission(interaction.member);

            // Build the main container with level channel select revealed
            const container = await buildExpContainer({
                subcomponent: 'levels',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user,
                selectedLevelOption: 'level-channel' // Keep the option visible and show channel select
            });

            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        } else if (selected === 'add-level') {
            console.log('[exp-levels-config] add-level branch reached');
            // CREATE LEVEL PAGE COMPONENT (edit main message)
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const levels = guildData?.exp?.levels ?? [];
            // Allow level 0 creation when no levels exist, otherwise increment from highest
            const nextLevel = levels.length === 0 ? 0 : levels.length;

            // Store temp state (no role/exp yet)
            await optimizedUpdateOne("exp_create_level_temp",
                { userId: interaction.user.id, guildId: interaction.guild.id },
                { $set: { roleId: null, exp: null } },
                { upsert: true }
            );

            // Build unified level container (similar to items system)
            const container = await buildUnifiedLevelContainer(interaction.user.id, interaction.guild.id, false, null, interaction);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        } else if (selected === 'level-msg') {
            // Toggle level message enabled/disabled
            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { exp: { levelMsgEnabled: true } };
            if (!guildData.exp) guildData.exp = { levelMsgEnabled: true };
            const newState = !guildData.exp.levelMsgEnabled;
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { 'exp.levelMsgEnabled': newState } }
            );
            // Fetch updated data and rebuild UI
            guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const hasPermission = hasExpPermission(interaction.member);
            const container = await buildExpContainer({
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user,
                selectedLevelOption: null // Clear to hide channel select menu
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        } else if (selected === 'level-msg-template') {
            // Only allow bot owner to edit level message template
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild container with error message
                const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit level messages',
                    selectedLevelOption: null // Clear to hide channel select menu
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            // Show modal to edit the level up message
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const currentMsg = guildData?.exp?.levelMsg ?? '{mention} leveled up to level {level} and received the {role} role.';
            const msgInput = new TextInputBuilder()
                .setCustomId('level-msg-template-input')
                .setLabel('Level Up Message')
                .setPlaceholder('Available: {mention}, {level}, {role}, {exp}, {server}')
                .setValue(currentMsg)
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(200) // Increased to accommodate longer messages with new parameters
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(msgInput);
            const modal = new ModalBuilder()
                .setCustomId('exp-level-msg-template-modal')
                .setTitle('Edit Level Up Message')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        } else if (selected.startsWith('level-')) {
            // EDIT LEVEL PAGE COMPONENT (edit main message)
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const levels = guildData?.exp?.levels ?? [];
            const levelIndex = parseInt(selected.replace('level-', '')) - 1;
            const level = levels[levelIndex];
            if (!level) {
                // Show error in container instead of ephemeral response
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const container = await buildExpContainer({
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    statusMessage: '❌ Level not found',
                    selectedLevelOption: null // Clear to hide channel select menu
                });
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            // Store current level data in temp for editing
            await optimizedUpdateOne("exp_create_level_temp",
                { userId: interaction.user.id, guildId: interaction.guild.id },
                { $set: { roleId: level.roleId, exp: level.exp } },
                { upsert: true }
            );

            // Build unified level container for editing
            const container = await buildUnifiedLevelContainer(interaction.user.id, interaction.guild.id, true, levelIndex, interaction);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }
    } else if (interaction.customId === 'exp-levels-channel-select') {
        // User selected a channel for level up messages
        const channelId = interaction.values[0];
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.levelChannel': channelId } }
        );
        // Fetch updated guildData
        const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        // Build updated main EXP container (clear selectedLevelOption to hide channel select)
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const hasPermission = hasExpPermission(interaction.member);
        const container = await buildExpContainer({
            subcomponent: 'levels',
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user,
            selectedLevelOption: null // Clear to hide the channel select menu after selection
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'exp-text-config') {
        // Check permissions
        const hasPermission = hasExpPermission(interaction.member);
        if (!hasPermission) {
            // Show demo mode if no permission
            const container = await buildExpContainer({
                enabled: true,
                hasPermission: false,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const toggleButton = new ButtonBuilder()
                .setCustomId('exp-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)]
            });
            return;
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { text: {}, enabled: true } };
        if (!guildData.exp) guildData.exp = { text: {}, enabled: true };
        if (!guildData.exp.text) guildData.exp.text = {};

        // Check if EXP is enabled for this guild
        if (!guildData.exp.enabled) {
            // Feature is disabled, don't process the selection
            const container = await buildExpContainer({
                enabled: false,
                guildData,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const enableButton = new ButtonBuilder()
                .setCustomId('exp-global-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        }

        const selected = interaction.values[0];

        // Build ephemeral container for text config
        const heading = new TextDisplayBuilder().setContent('# text exp settings');
        const description = new TextDisplayBuilder().setContent('> configure text exp settings');

        if (selected === 'text-exp-per-min') {
            // Only allow bot owner to edit text EXP rates
            if (interaction.user.id !== process.env.OWNER) {
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    subcomponent: 'text',
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit text EXP rates'
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            const currentValue = guildData.exp.text.expPerMin ?? 1; // Use standardized default
            const expInput = new TextInputBuilder()
                .setCustomId('text-exp-per-min-input')
                .setLabel('exp per cooldown')
                .setPlaceholder('Enter exp per cooldown')
                .setValue(String(currentValue))
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(4)
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(expInput);
            const modal = new ModalBuilder()
                .setCustomId('text-exp-per-min-modal')
                .setTitle('Set Exp Per Cooldown')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        } else if (selected === 'text-cooldown') {
            // Only allow bot owner to edit text EXP rates
            if (interaction.user.id !== process.env.OWNER) {
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    subcomponent: 'text',
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit text EXP rates'
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            const currentValue = guildData.exp.text.cooldown ?? 1;
            const cdInput = new TextInputBuilder()
                .setCustomId('text-cooldown-input')
                .setLabel('cooldown (minutes)')
                .setPlaceholder('Enter cooldown in minutes')
                .setValue(String(currentValue))
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(2)
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(cdInput);
            const modal = new ModalBuilder()
                .setCustomId('text-cooldown-modal')
                .setTitle('Set Cooldown')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        } else if (selected === 'text-min-chars') {
            // Only allow bot owner to edit text EXP rates
            if (interaction.user.id !== process.env.OWNER) {
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    subcomponent: 'text',
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit text EXP rates'
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            const currentValue = guildData.exp.text.minChars ?? 4; // Use standardized default
            const charsInput = new TextInputBuilder()
                .setCustomId('text-min-chars-input')
                .setLabel('minimum characters')
                .setPlaceholder('Enter minimum characters')
                .setValue(String(currentValue))
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(3)
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(charsInput);
            const modal = new ModalBuilder()
                .setCustomId('text-min-chars-modal')
                .setTitle('Set Minimum Characters')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        }
    } else if (interaction.customId === 'exp-voice-config') {
        // Check permissions
        const hasPermission = hasExpPermission(interaction.member);
        if (!hasPermission) {
            // Show demo mode if no permission
            const container = await buildExpContainer({
                enabled: true,
                hasPermission: false,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const toggleButton = new ButtonBuilder()
                .setCustomId('exp-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)]
            });
            return;
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { voice: {}, enabled: true } };
        if (!guildData.exp) guildData.exp = { voice: {}, enabled: true };
        if (!guildData.exp.voice) guildData.exp.voice = {};

        // Check if EXP is enabled for this guild
        if (!guildData.exp.enabled) {
            // Feature is disabled, don't process the selection
            const container = await buildExpContainer({
                enabled: false,
                guildData,
                guild: interaction.guild,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const enableButton = new ButtonBuilder()
                .setCustomId('exp-global-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        }

        const selected = interaction.values[0];

        if (selected === 'voice-exp-per-min') {
            // Only allow bot owner to edit voice EXP rates
            if (interaction.user.id !== process.env.OWNER) {
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    subcomponent: 'voice',
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit voice EXP rates'
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            const currentValue = guildData.exp.voice.expPerMin ?? 1; // Use standardized default
            const expInput = new TextInputBuilder()
                .setCustomId('voice-exp-per-min-input')
                .setLabel('exp per minute')
                .setPlaceholder('Enter exp per minute')
                .setValue(String(currentValue))
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(4)
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(expInput);
            const modal = new ModalBuilder()
                .setCustomId('voice-exp-per-min-modal')
                .setTitle('Set Exp Per Minute')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        } else if (selected === 'voice-cooldown') {
            // Only allow bot owner to edit voice EXP rates
            if (interaction.user.id !== process.env.OWNER) {
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    subcomponent: 'voice',
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit voice EXP rates'
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            const currentValue = guildData.exp.voice.cooldown ?? 1;
            const cdInput = new TextInputBuilder()
                .setCustomId('voice-cooldown-input')
                .setLabel('cooldown (minutes)')
                .setPlaceholder('Enter cooldown in minutes')
                .setValue(String(currentValue))
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(2)
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(cdInput);
            const modal = new ModalBuilder()
                .setCustomId('voice-cooldown-modal')
                .setTitle('Set Cooldown')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        } else if (selected === 'voice-msg-toggle') {
            // Toggle voice message enabled/disabled directly
            const currentValue = guildData.exp.voice.msgEnabled ?? true;
            const newValue = !currentValue;
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { 'exp.voice.msgEnabled': newValue } }
            );
            // Fetch updated data and rebuild UI
            guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                subcomponent: 'voice',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild
            });
            const hasPermission = hasExpPermission(interaction.member);
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        } else if (selected === 'voice-msg-template') {
            // Only allow bot owner to edit voice message template
            if (interaction.user.id !== process.env.OWNER) {
                const hasPermission = hasExpPermission(interaction.member);
                const container = await buildExpContainer({
                    subcomponent: 'voice',
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    hasPermission,
                    user: interaction.user,
                    statusMessage: '❌ Only the bot owner can edit voice messages'
                });
                const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                    .setDisabled(!hasPermission);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container, globalButtonRow]
                });
                return;
            }

            // Show modal to edit the voice EXP message
            const currentMsg = guildData?.exp?.voice?.msg ?? '{mention} spent {duration} in voice and earned {exp} exp.';
            const msgInput = new TextInputBuilder()
                .setCustomId('voice-msg-template-input')
                .setLabel('Voice EXP Message')
                .setPlaceholder('Available: {mention}, {exp}, {duration} (2m, 1h23m), {totalexp}, {server}')
                .setValue(currentMsg)
                .setRequired(true)
                .setMinLength(1)
                .setMaxLength(200) // Increased to accommodate longer messages with new parameters
                .setStyle(TextInputStyle.Short);
            const row = new ActionRowBuilder().addComponents(msgInput);
            const modal = new ModalBuilder()
                .setCustomId('voice-msg-template-modal')
                .setTitle('Edit Voice EXP Message')
                .addComponents(row);
            await interaction.showModal(modal);
            return;
        }
    } else if (interaction.customId === 'exp-level-msg-template-modal') {
        // Only allow bot owner to edit level message template
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const hasPermission = hasExpPermission(interaction.member);
            const container = await buildExpContainer({
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user,
                statusMessage: '❌ Only the bot owner can edit level messages'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }

        const value = interaction.fields.getTextInputValue('level-msg-template-input');
        console.log('[modalSubmit] level-msg-template-input value:', value);

        // Update all guilds with the new level message template (bot-wide setting)
        await optimizedUpdateMany("guilds",
            { 'exp.enabled': { $ne: false } }, // Only update guilds that have EXP enabled
            { $set: { 'exp.levelMsg': value } }
        );

        // CRITICAL FIX: Invalidate guild config caches after bot-wide EXP template update
        try {
            const { invalidateSpecificGuildConfigCaches } = require('../../utils/guildConfigInvalidation.js');
            // This is a bot-wide update, but we'll invalidate for the current guild as an example
            await invalidateSpecificGuildConfigCaches(interaction.guild.id, ['exp', 'message']);
            console.log(`[exp] 🔄 Invalidated guild config caches after level message template update for ${interaction.guild.id}`);
        } catch (cacheError) {
            console.error('[exp] Error invalidating guild config caches after level message template update:', cacheError);
        }
        // Fetch updated guildData
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true, levels: [] } };
        if (!guildData.exp) guildData.exp = { enabled: true, levels: [] };
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const hasPermission = hasExpPermission(interaction.member);
        const container = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId.startsWith('exp-edit-level-modal-')) {
        const levelIndex = parseInt(interaction.customId.replace('exp-edit-level-modal-', ''));
        const expValue = parseInt(interaction.fields.getTextInputValue('exp-edit-level-exp-input'));
        if (isNaN(expValue) || expValue < 1) {
            // Show error in container instead of ephemeral response
            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { exp: { levels: [] } };
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                statusMessage: '❌ Please enter a valid EXP value greater than 0'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { levels: [] } };
        if (!guildData.exp) guildData.exp = { levels: [] };
        if (!guildData.exp.levels) guildData.exp.levels = [];
        if (!guildData.exp.levels[levelIndex]) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const hasPermission = hasExpPermission(interaction.member);
            const container = await buildExpContainer({
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user,
                statusMessage: 'Level not found.'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        // Update exp
        const oldExp = guildData.exp.levels[levelIndex].exp;
        guildData.exp.levels[levelIndex].exp = expValue;
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.levels': guildData.exp.levels } }
        );

        // CRITICAL FIX: Invalidate guild config caches after EXP level update
        try {
            const { invalidateSpecificGuildConfigCaches } = require('../../utils/guildConfigInvalidation.js');
            await invalidateSpecificGuildConfigCaches(interaction.guild.id, ['exp']);
            console.log(`[exp] 🔄 Invalidated EXP config caches after level update for ${interaction.guild.id}`);
        } catch (cacheError) {
            console.error('[exp] Error invalidating guild config caches after level update:', cacheError);
        }

        // Send level edited log
        await sendExpLevelEditedLog(
            interaction.guild.id,
            interaction.user.id,
            levelIndex + 1,
            { exp: { old: oldExp, new: expValue } },
            interaction.client
        );
        // Update main component
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const hasPermission = hasExpPermission(interaction.member);
        const container = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user
        });
        try {
            await interaction.message.edit({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
        } catch (e) {}

        // Show success in container instead of ephemeral response
        const updatedSelectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const updatedContainer = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            statusMessage: '✅ EXP updated successfully'
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [updatedSelectMenu, updatedContainer, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'exp-create-level-modal') {
        try {
            const userId = interaction.user.id;
            const guildId = interaction.guild.id;
            // Retrieve the role from the temp collection
            const temp = await optimizedFindOne("exp_create_level_temp", { userId, guildId });
            const roleId = temp?.roleId;
            if (!roleId) {
                // Show error in container instead of ephemeral response
                const errorSelectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const errorContainer = await buildExpContainer({
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    statusMessage: '❌ Please select a role first!'
                });
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [errorSelectMenu, errorContainer, globalButtonRow]
                });
                return;
            }
            const expValue = parseInt(interaction.fields.getTextInputValue('exp-create-level-exp-input'));
            // Allow 0 EXP only for level 0, otherwise require at least 1 EXP
            const isLevel0 = guildData.exp.levels.length === 0;
            const minExpRequired = isLevel0 ? 0 : 1;
            if (isNaN(expValue) || expValue < minExpRequired) {
                // Show error in container instead of ephemeral response
                const validationSelectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                const validationContainer = await buildExpContainer({
                    enabled: guildData.exp.enabled,
                    guildData,
                    guild: interaction.guild,
                    statusMessage: '❌ Please enter a valid EXP value greater than 0'
                });
                const globalExpButton = new ButtonBuilder()
                    .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                    .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                    .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [validationSelectMenu, validationContainer, globalButtonRow]
                });
                return;
            }
            // Add new level
            let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
            if (!guildData) guildData = { exp: { levels: [] } };
            if (!guildData.exp) guildData.exp = { levels: [] };
            if (!guildData.exp.levels) guildData.exp.levels = [];
            // Allow level 0 creation when no levels exist, otherwise start from 1
            const newLevelNumber = guildData.exp.levels.length === 0 ? 0 : guildData.exp.levels.length;
            guildData.exp.levels.push({ roleId, exp: expValue });
            await optimizedUpdateOne("guilds",
                { id: interaction.guild.id },
                { $set: { 'exp.levels': guildData.exp.levels } }
            );

            // Send level created log
            await sendExpLevelCreatedLog(
                interaction.guild.id,
                interaction.user.id,
                newLevelNumber,
                roleId,
                expValue,
                interaction.client
            );

            // Clean up temp
            await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

            // FIXED: Invalidate guild exp config cache to ensure main interface shows new level
            invalidateGuildExpConfigCache(interaction.guild.id);

            // Build updated main EXP container
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const hasPermission = hasExpPermission(interaction.member);
        const container = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user
        });
            // Fetch main EXP container message ID from MongoDB
            const mainMsgDoc = await optimizedFindOne("exp_main_msgs", { guildId });
            if (mainMsgDoc && mainMsgDoc.messageId) {
                try {
                    await interaction.channel.messages.edit(mainMsgDoc.messageId, {
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, container]
                    });
                } catch (e) { console.error('Failed to update main EXP container:', e); }
            }
            // No need to reply or send a new ephemeral message
            return;
        } catch (err) {
            console.error('[modalSubmit] Error handling exp-create-level-modal:', err);
            if (!interaction.replied) {
                try {
                    // Try to rebuild container with error status message
                    let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                    if (!guildData) guildData = { exp: { enabled: true } };
                    if (!guildData.exp) guildData.exp = { enabled: true };

                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
                    const hasPermission = hasExpPermission(interaction.member);
                    const container = await buildExpContainer({
                        enabled: guildData.exp.enabled,
                        guildData,
                        guild: interaction.guild,
                        hasPermission,
                        member: interaction.member,
                        commandChannel: interaction.channel,
                        user: interaction.user,
                        statusMessage: 'An error occurred while creating the level.'
                    });
                    const globalExpButton = new ButtonBuilder()
                        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                        .setDisabled(!hasPermission);
                    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, container, globalButtonRow]
                    });
                } catch (fallbackErr) {
                    console.error('[modalSubmit] Fallback error:', fallbackErr);
                    await interaction.reply({ content: 'An error occurred while creating the level.', flags: MessageFlags.Ephemeral });
                }
            }
            return;
        }
    } else if (interaction.customId === 'exp-global-disable' || interaction.customId === 'exp-global-enable') {
        // OPTIMIZED: Use cached guild configuration function
        let guildData = await getCachedGuildExpConfig(interaction.guild.id);
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };

        // Toggle the enabled state
        const newState = interaction.customId === 'exp-global-enable';
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.enabled': newState } }
        );

        // OPTIMIZED: Invalidate cache after update and fetch fresh data
        invalidateGuildExpConfigCache(interaction.guild.id);
        guildData = await getCachedGuildExpConfig(interaction.guild.id);

        // Track performance metrics
        expMetrics.togglesProcessed++;

        // Determine current subcomponent from the message components
        let currentSubcomponent = 'levels'; // default
        try {
            const messageContent = JSON.stringify(interaction.message.components);
            if (messageContent.includes('exp-text-config')) {
                currentSubcomponent = 'text';
            } else if (messageContent.includes('exp-voice-config')) {
                currentSubcomponent = 'voice';
            }
        } catch (e) {
            currentSubcomponent = 'levels';
        }

        // Build updated container with correct enabled state and preserve subcomponent
        const container = await buildExpContainer({
            subcomponent: currentSubcomponent,
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild
        });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        // Global enable/disable button always enabled, label and color reflect state
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId.startsWith('exp-edit-level-exp-select-')) {
        await expEditLevelExpSelect(interaction);
    } else if (interaction.customId.startsWith('exp-edit-level-role-')) {
        await expEditLevelRoleSelect(interaction);
    } else if (interaction.customId === 'exp-create-level-back') {
        await expCreateLevelBack(interaction);
        return;
    }
}

/**
 * Build unified level container (similar to items system)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {boolean} isEditing - Whether editing existing level
 * @param {number} editingLevelIndex - Index of level being edited (if editing)
 * @returns {ContainerBuilder} The unified container
 */
async function buildUnifiedLevelContainer(userId, guildId, isEditing, editingLevelIndex = null, interaction = null) {
    // OPTIMIZED: Parallel data fetching with Promise.allSettled
    const [guildDataResult, tempStateResult] = await Promise.allSettled([
        getCachedGuildExpConfig(guildId),
        getCachedTempState("exp_create_level_temp", userId, guildId)
    ]);

    // Handle results with graceful fallbacks
    const guildData = guildDataResult.status === 'fulfilled' ? guildDataResult.value : { exp: { levels: [] } };
    const levels = guildData?.exp?.levels ?? [];
    const temp = tempStateResult.status === 'fulfilled' ? tempStateResult.value : null;

    // Track performance metrics
    expMetrics.levelsProcessed++;
    expMetrics.uiBuildsProcessed++;

    const components = [];

    // Header with back button - dynamic text for editing vs creating
    let levelNumber;
    if (isEditing) {
        // For editing: show the actual level being edited (0, 1, 2, etc.)
        levelNumber = (editingLevelIndex === 0 && levels[0]?.exp === 0) ? 0 : editingLevelIndex;
    } else {
        // For creating: show level 0 if no levels exist, otherwise increment
        levelNumber = levels.length === 0 ? 0 : levels.length;
    }
    const headerText = isEditing ? `# edit level ${levelNumber}` : `# create level ${levelNumber}`;
    const quoteText = isEditing ? `> edit the role and exp required` :
        (levelNumber === 0 ? '> select the role' : '> select the role and exp required');

    const backButton = new ButtonBuilder()
        .setCustomId(isEditing ? `exp-unified-edit-back-${editingLevelIndex}` : 'exp-unified-level-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(new TextDisplayBuilder().setContent(headerText))
        .setButtonAccessory(backButton);

    components.push(backSection);
    components.push(new TextDisplayBuilder().setContent(quoteText));

    // Determine if this is level 0 (declare once and reuse)
    const isLevel0 = (!isEditing && levels.length === 0) || (isEditing && editingLevelIndex === 0 && levels[0]?.exp === 0);

    // Show current selection if available
    // For editing mode, prioritize temp values (user's changes) over original level data
    const selectedRole = isEditing ? (temp?.roleId || levels[editingLevelIndex]?.roleId) : temp?.roleId;
    let selectedExp = isEditing ? (temp?.exp || levels[editingLevelIndex]?.exp) : temp?.exp;
    // Icon select (exactly like global levels) - moved up before first usage
    const selectedIcon = isEditing ? (temp?.levelIcon || levels[editingLevelIndex]?.levelIcon) : temp?.levelIcon;

    // Show current selection if available
    // For level 0, show role + 0 exp even if only role is selected
    // For other levels, show only when both role and exp are selected

    if (selectedRole) {
        let detailsText;
        const iconPrefix = selectedIcon ? `${selectedIcon} ` : '';

        if (isLevel0) {
            // For level 0, always show 0 exp with icon
            detailsText = `${iconPrefix}<@&${selectedRole}> 0 exp`;
        } else if (selectedExp) {
            // For other levels, show exp only if selected with icon
            detailsText = `${iconPrefix}<@&${selectedRole}> ${selectedExp} exp`;
        } else {
            // Just show role if exp not selected yet with icon
            detailsText = `${iconPrefix}<@&${selectedRole}>`;
        }
        components.push(new TextDisplayBuilder().setContent(detailsText));
    }

    // Role select
    const usedRoleIds = levels.map(level => level.roleId).filter(Boolean);
    // If editing, don't exclude the current level's role
    const excludeRoles = isEditing ? usedRoleIds.filter(id => id !== selectedRole) : usedRoleIds;

    const roleSelectBuilder = new (require('discord.js').RoleSelectMenuBuilder)()
        .setCustomId(isEditing ? `exp-unified-edit-role-${editingLevelIndex}` : 'exp-unified-level-role')
        .setPlaceholder('select role')
        .setMinValues(1)
        .setMaxValues(1)
        .setDisabled(false);

    if (selectedRole) {
        roleSelectBuilder.setDefaultRoles(selectedRole);
    }

    const roleRow = new ActionRowBuilder().addComponents(roleSelectBuilder);
    components.push(roleRow);

    // Use shared image uploader utility
    if (interaction) {
        try {
            const recentImages = await getRecentImagesFromChannel(interaction);
            const customId = isEditing ? `exp-unified-edit-icon-${editingLevelIndex}` : 'exp-unified-level-icon';

            const imageSelectRow = buildImageSelectMenu(
                recentImages,
                customId,
                'select icon',
                selectedIcon
            );

            if (imageSelectRow) {
                components.push(imageSelectRow);
            } else {
                const noImagesRow = buildNoImagesSelectMenu(customId, 'select icon');
                components.push(noImagesRow);
            }
        } catch (error) {
            console.error('[buildUnifiedLevelContainer] Error loading images:', error);
            const customId = isEditing ? `exp-unified-edit-icon-${editingLevelIndex}` : 'exp-unified-level-icon';
            const noImagesRow = buildNoImagesSelectMenu(customId, 'select icon');
            components.push(noImagesRow);
        }
    }

    // Exp select - handle level 0 specially
    // isLevel0 already declared above, reuse it

    // Always show EXP selection, but handle level 0 specially
    let minExp = 0;
    if (isEditing) {
        // For editing, use the previous level's exp as minimum (if exists)
        if (editingLevelIndex > 0) {
            minExp = levels[editingLevelIndex - 1].exp;
        }
    } else {
        // For creating, use the last level's exp as minimum
        minExp = levels.length > 0 ? levels[levels.length - 1].exp : 0;
    }

    const maxExp = minExp + 2500;

    // For level 0, force selectedExp to 0 and create options with 0 selected
    let expOptionsToUse;
    let expSelectDisabled;

    if (isLevel0) {
        // Force 0 EXP for level 0 and disable the select
        selectedExp = 0;
        expOptionsToUse = generateLinearExpOptions(0, 100, 0, 100); // Generate options starting from 0
        expSelectDisabled = true;
    } else {
        // Normal EXP selection for levels 1+
        expOptionsToUse = generateLinearExpOptions(minExp, maxExp, selectedExp, 100);
        // FIXED: For non-level-0 creation, enable EXP select after role is selected
        expSelectDisabled = isEditing ? false : !selectedRole;
    }

    const expSelect = new StringSelectMenuBuilder()
        .setCustomId(isEditing ? `exp-unified-edit-exp-${editingLevelIndex}` : 'exp-unified-level-exp')
        .setPlaceholder('select exp value')
        .addOptions(expOptionsToUse)
        .setDisabled(expSelectDisabled);

    const expRow = new ActionRowBuilder().addComponents(expSelect);
    components.push(expRow);

    // Create main container
    const container = new ContainerBuilder();

    // Set accent color
    container.setAccentColor(isEditing ? OPERATION_COLORS.EDIT : OPERATION_COLORS.ADD);

    // Add components using the correct methods
    components.forEach(component => {
        if (component instanceof SectionBuilder) {
            container.addSectionComponents(component);
        } else if (component instanceof TextDisplayBuilder) {
            container.addTextDisplayComponents(component);
        } else if (component instanceof ActionRowBuilder) {
            container.addActionRowComponents(component);
        }
    });

    // Always show Create/Modify button, but control its enabled state
    // isLevel0 already declared above, reuse it
    const hasRequiredFields = isLevel0 ? selectedRole : (selectedRole && selectedExp);
    let buttonDisabled = false;

    if (isEditing) {
        // For editing: disable if no changes made from original values
        const originalRole = levels[editingLevelIndex]?.roleId;
        const originalExp = levels[editingLevelIndex]?.exp;
        const hasChanges = selectedRole !== originalRole || selectedExp !== originalExp;
        buttonDisabled = !hasChanges;
    } else {
        // For creating: disable until required fields are selected (role for level 0, role+exp for others)
        buttonDisabled = !hasRequiredFields;
    }

    const createButton = new ButtonBuilder()
        .setCustomId(isEditing ? `exp-modify-level-final-${editingLevelIndex}` : 'exp-create-level-final')
        .setLabel(isEditing ? 'modify level' : 'create level')
        .setStyle(ButtonStyle.Success)
        .setDisabled(buttonDisabled);
    const createRow = new ActionRowBuilder().addComponents(createButton);

    container.addActionRowComponents(createRow);

    return container;
}

// Utility to generate exp options
function generateExpOptions(minExp) {
    // Use a quadratic formula: exp = 100 * (level^2)
    const options = [];
    let level = 1;
    // Find the starting level so that exp > minExp
    while (true) {
        const exp = Math.round(100 * Math.pow(level, 2));
        if (exp > minExp) break;
        level++;
    }
    // Generate up to 25 options
    for (let i = 0; i < 25; i++) {
        const exp = Math.round(100 * Math.pow(level, 2));
        options.push({ label: String(exp), value: String(exp) });
        level++;
    }
    return options;
}

// Add new handler for exp-create-level-exp-select
async function expCreateLevelExpSelect(interaction) {
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;
    const expValue = parseInt(interaction.values[0]);
    
    // Store exp in temp
    await optimizedUpdateOne("exp_create_level_temp",
        { userId, guildId },
        { $set: { exp: expValue } },
        { upsert: true }
    );
    const tempExp = await optimizedFindOne("exp_create_level_temp", { userId, guildId });
    console.log('[expCreateLevelExpSelect] temp after setting exp:', tempExp);
    // Get current levels to determine next level number
    const guildData = await optimizedFindOne("guilds", { id: guildId });
    const levels = guildData?.exp?.levels ?? [];
    // Allow level 0 creation when no levels exist, otherwise increment
    const nextLevel = levels.length === 0 ? 0 : levels.length;

    // Build UI
    const heading = new TextDisplayBuilder().setContent(`# create level ${nextLevel}`);
    const quote = new TextDisplayBuilder().setContent(nextLevel === 0 ?
        '> select the role' :
        '> select the role and exp required');

    // Get current temp state
    const temp = await optimizedFindOne("exp_create_level_temp", { userId, guildId });
    const roleId = temp?.roleId;

    // Show selected role and exp in the UI
    let details = null;
    if (roleId) {
        details = new TextDisplayBuilder().setContent(`<@&${roleId}> ${expValue} exp`);
    }

    // Back button in section with title (matching edit level pattern)
    const backButton = new ButtonBuilder()
        .setCustomId('exp-create-level-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);

    // Role select, pre-selected and enabled
    const roleSelectBuilder = new (require('discord.js').RoleSelectMenuBuilder)()
        .setCustomId('exp-create-level-role')
        .setPlaceholder('select role')
        .setMinValues(1)
        .setMaxValues(1)
        .setDisabled(false);
    if (roleId) {
        roleSelectBuilder.setDefaultRoles(roleId);
    }
    const roleRow = new ActionRowBuilder().addComponents(roleSelectBuilder);

    // Exp select, pre-selected and enabled
    let minExp = levels.length > 0 ? levels[levels.length - 1].exp : 0;
    let maxExp = minExp + 2500;
    const expOptions = generateLinearExpOptions(minExp, maxExp, expValue, 100);
    const expSelect = new StringSelectMenuBuilder()
        .setCustomId('exp-create-level-exp-select')
        .setPlaceholder('select exp value')
        .addOptions(expOptions)
        .setDisabled(false);
    const expRow = new ActionRowBuilder().addComponents(expSelect);

    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(details ? [quote, details] : [quote])
        .addActionRowComponents(roleRow)
        .addActionRowComponents(expRow)
        .setAccentColor(OPERATION_COLORS.ADD);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [container]
    });
}

// Handler for exp-create-level-back
async function expCreateLevelBack(interaction) {
    console.log('[expCreateLevelBack] called with customId:', interaction.customId, 'user:', interaction.user.id);
    const guildId = interaction.guild.id;
    const userId = interaction.user.id;
    const temp = await optimizedFindOne("exp_create_level_temp", { userId, guildId });
    console.log('[expCreateLevelBack] temp state:', temp);

    // If both role and exp are set, add the new level
    if (temp?.roleId && temp?.exp) {
        let guildData = await optimizedFindOne("guilds", { id: guildId });
        if (!guildData) {
            guildData = { id: guildId, exp: { levels: [] } };
            // FIXED: Use optimizedInsertOne instead of deprecated col.insertOne
            await optimizedInsertOne("guilds", guildData);
        }
        if (!guildData.exp) {
            guildData.exp = { levels: [] };
            await optimizedUpdateOne("guilds", { id: guildId }, { $set: { 'exp': { levels: [] } } });
        }
        if (!guildData.exp.levels) {
            guildData.exp.levels = [];
            await optimizedUpdateOne("guilds", { id: guildId }, { $set: { 'exp.levels': [] } });
        }
        const newLevelNumber = guildData.exp.levels.length + 1;
        guildData.exp.levels.push({ roleId: temp.roleId, exp: temp.exp });
        await optimizedUpdateOne("guilds",
            { id: guildId },
            { $set: { 'exp.levels': guildData.exp.levels } }
        );

        // Send level created log
        await sendExpLevelCreatedLog(
            guildId,
            interaction.user.id,
            newLevelNumber,
            temp.roleId,
            temp.exp,
            interaction.client
        );

        console.log('[expCreateLevelBack] Added new level:', { roleId: temp.roleId, exp: temp.exp });
    } else {
        console.log('[expCreateLevelBack] Not adding level, missing role or exp:', temp);
    }

    // Clean up temp state
    await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

    // FIXED: Invalidate guild exp config cache to ensure main interface shows new level
    invalidateGuildExpConfigCache(guildId);

    // Build updated main EXP container
    let guildData = await optimizedFindOne("guilds", { id: guildId });
    if (!guildData) guildData = { exp: { enabled: true, levels: [] } };
    if (!guildData.exp) guildData.exp = { enabled: true, levels: [] };
    console.log('[expCreateLevelBack] levels before building main embed:', guildData.exp.levels);
    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
    const hasPermission = hasExpPermission(interaction.member);
    const containerMain = await buildExpContainer({
        enabled: guildData.exp.enabled,
        guildData,
        guild: interaction.guild,
        hasPermission,
        member: interaction.member,
        commandChannel: interaction.channel,
        user: interaction.user
    });
    const globalExpButton = new ButtonBuilder()
        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(false);
    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [selectMenu, containerMain, globalButtonRow]
    });
}

// Utility to build a reusable Back button row
function buildBackButton(customId) {
    return new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId(customId)
            .setLabel('Back')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(false)
    );
}

// Add handler for exp-edit-level-back
async function expEditLevelBack(interaction) {
    // Just return to the main EXP menu
    const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
    const hasPermission = hasExpPermission(interaction.member);
    const containerMain = await buildExpContainer({
        enabled: guildData.exp.enabled,
        guildData,
        guild: interaction.guild,
        hasPermission,
        member: interaction.member,
        commandChannel: interaction.channel,
        user: interaction.user
    });
    const globalExpButton = new ButtonBuilder()
        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(false);
    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [selectMenu, containerMain, globalButtonRow]
    });
}

// Utility to generate linear exp options for editing a level
function generateLinearExpOptions(minExp, maxExp, currentExp, step = 100) {
    const options = [];
    // Start from minExp + step, but ensure we have at least some options
    let value = Math.max(minExp + step, step);
    while (value <= maxExp && options.length < 25) {
        options.push({ label: String(value), value: String(value) });
        value += step;
    }
    // Ensure currentExp is included and preselected, but only if it's a valid number
    if (typeof currentExp === 'number' && !isNaN(currentExp)) {
        if (!options.some(opt => opt.value === String(currentExp))) {
            options.unshift({ label: String(currentExp), value: String(currentExp), default: true });
            if (options.length > 25) options.pop(); // maintain 25 limit
        } else {
            // Mark as default
            options.forEach(opt => {
                if (opt.value === String(currentExp)) opt.default = true;
            });
        }
    }
    return options;
}

// Add new handler for exp-edit-level-exp-select
async function expEditLevelExpSelect(interaction) {
    try {
        const userId = interaction.user.id;
        const guildId = interaction.guild.id;
        const parts = interaction.customId.split("-");
        const levelIndex = parseInt(parts[parts.length - 1]);
        const expValue = parseInt(interaction.values[0]);
        // Always fetch fresh data
        let guildData = await optimizedFindOne("guilds", { id: guildId });
        if (!guildData) guildData = { exp: { levels: [] } };
        if (!guildData.exp) guildData.exp = { levels: [] };
        if (!guildData.exp.levels) guildData.exp.levels = [];
        console.log(`[exp-edit-level-exp-select-*] customId=${interaction.customId}, levelIndex=${levelIndex}, levels.length=${guildData.exp.levels.length}`);
        if (!guildData.exp.levels[levelIndex]) {
            console.error(`[expEditLevelExpSelect] Level not found: index=${levelIndex}, levels.length=${guildData.exp.levels.length}`);
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const hasPermission = hasExpPermission(interaction.member);
            const container = await buildExpContainer({
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user,
                statusMessage: 'Level not found.'
            });
            try {
                await interaction.message.edit({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container]
                });
            } catch (e) {
                console.error('[expEditLevelExpSelect] Error updating message:', e);
            }
            return;
        }
        const oldExp = guildData.exp.levels[levelIndex].exp;
        guildData.exp.levels[levelIndex].exp = expValue;
        await optimizedUpdateOne("guilds",
            { id: guildId },
            { $set: { 'exp.levels': guildData.exp.levels } }
        );

        // Send level edited log
        await sendExpLevelEditedLog(
            guildId,
            interaction.user.id,
            levelIndex + 1,
            { exp: { old: oldExp, new: expValue } },
            interaction.client
        );
        // Build updated main EXP container
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user
        });
        try {
            await interaction.message.edit({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
        } catch (e) {}
        // Update the ephemeral UI to show completed state (enabled controls)
        const heading = new TextDisplayBuilder().setContent(`# edit level ${levelIndex + 1}`);
        const quote = new TextDisplayBuilder().setContent('> edit the role and the exp required');
        const details = new TextDisplayBuilder().setContent(`<@&${guildData.exp.levels[levelIndex].roleId}> ${expValue} exp`);
        const roleSelect = new (require('discord.js').RoleSelectMenuBuilder)()
            .setCustomId(`exp-edit-level-role-${levelIndex}`)
            .setPlaceholder('select role')
            .setMinValues(1)
            .setMaxValues(1)
            .setDefaultRoles(guildData.exp.levels[levelIndex].roleId)
            .setDisabled(false);
        const roleRow = new ActionRowBuilder().addComponents(roleSelect);
        const expOptions = generateLinearExpOptions(expValue, expValue + 2500, expValue, 100);
        const expSelect = new StringSelectMenuBuilder()
            .setCustomId(`exp-edit-level-exp-select-${levelIndex}`)
            .setPlaceholder('select exp value')
            .addOptions(expOptions)
            .setDisabled(false);
        const expRow = new ActionRowBuilder().addComponents(expSelect);

        // Build back button section (like owner.js pattern)
        const backButton = new ButtonBuilder()
            .setCustomId(`exp-edit-level-back-${levelIndex}`)
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent(`# edit level ${levelIndex + 1}`))
            .setButtonAccessory(backButton);

        const containerEphemeral = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(quote, details)
            .addActionRowComponents(roleRow)
            .addActionRowComponents(expRow)
            .setAccentColor(OPERATION_COLORS.EDIT);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [containerEphemeral]
        });
    } catch (err) {
        console.error('[expEditLevelExpSelect] Error:', err);
        if (!interaction.replied && !interaction.deferred) {
            try {
                // Try to build basic error container
                const backButton = new ButtonBuilder()
                    .setCustomId('exp-back')
                    .setLabel('back')
                    .setStyle(ButtonStyle.Secondary);
                const backSection = new SectionBuilder()
                    .addTextDisplayComponents(new TextDisplayBuilder().setContent('# exp'))
                    .setButtonAccessory(backButton);

                const statusDisplay = new TextDisplayBuilder().setContent('**status:** An error occurred while updating the EXP.');

                const containerEphemeral = new ContainerBuilder()
                    .addSectionComponents(backSection)
                    .addTextDisplayComponents(statusDisplay)
                    .setAccentColor(OPERATION_COLORS.EDIT);

                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [containerEphemeral]
                });
            } catch (fallbackErr) {
                console.error('[expEditLevelExpSelect] Fallback error:', fallbackErr);
                await interaction.reply({ content: 'An error occurred while updating the EXP.', flags: MessageFlags.Ephemeral });
            }
        }
    }
}



// Handler for back button on level channel select (save temp to DB and return to main menu)
async function expLevelChannelBack(interaction) {
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;
    const temp = await optimizedFindOne("exp_level_channel_temp", { userId, guildId });
    if (temp?.channelId) {
        await optimizedUpdateOne("guilds",
            { id: guildId },
            { $set: { 'exp.levelChannel': temp.channelId } }
        );
    }
    // Clean up temp
    await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

    // FIXED: Invalidate guild exp config cache to ensure main interface shows updated channel
    invalidateGuildExpConfigCache(guildId);

    // Return to main EXP menu
    let guildData = await optimizedFindOne("guilds", { id: guildId });
    if (!guildData) guildData = { exp: { enabled: true, levels: [] } };
    if (!guildData.exp) guildData.exp = { enabled: true, levels: [] };
    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
    const container = await buildExpContainer({
        enabled: guildData.exp.enabled,
        guildData,
        guild: interaction.guild,
        member: interaction.member,
        commandChannel: interaction.channel,
        user: interaction.user
    });
    const globalExpButton = new ButtonBuilder()
        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(false);
    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [selectMenu, container, globalButtonRow]
    });
}

// Handler for editing the role in a level
async function expEditLevelRoleSelect(interaction) {
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;
    const parts = interaction.customId.split("-");
    const levelIndex = parseInt(parts[parts.length - 1]);
    const selectedRole = interaction.values[0];

    // Always fetch fresh data
    let guildData = await optimizedFindOne("guilds", { id: guildId });
    if (!guildData) guildData = { exp: { levels: [] } };
    if (!guildData.exp) guildData.exp = { levels: [] };
    if (!guildData.exp.levels) guildData.exp.levels = [];
    if (!guildData.exp.levels[levelIndex]) {
        // Build edit level container with status message instead of ephemeral reply
        const backButton = new ButtonBuilder()
            .setCustomId(`exp-edit-level-back-${levelIndex}`)
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent(`# edit level ${levelIndex + 1}`))
            .setButtonAccessory(backButton);

        const quote = new TextDisplayBuilder().setContent('> edit the role and the exp required');
        const statusDisplay = new TextDisplayBuilder().setContent('**status:** Level not found.');

        const containerEphemeral = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(quote, statusDisplay)
            .setAccentColor(OPERATION_COLORS.EDIT);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [containerEphemeral]
        });
        return;
    }

    // Check if role is already used by another level (excluding current level)
    const isRoleUsed = guildData.exp.levels.some((level, index) =>
        level.roleId === selectedRole && index !== levelIndex
    );

    if (isRoleUsed) {
        // Stay on current edit level page with status message
        const backButton = new ButtonBuilder()
            .setCustomId(`exp-edit-level-back-${levelIndex}`)
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent(`# edit level ${levelIndex + 1}`))
            .setButtonAccessory(backButton);

        // Content section
        const quote = new TextDisplayBuilder().setContent('> edit the role and the exp required');
        const details = new TextDisplayBuilder().setContent(`<@&${guildData.exp.levels[levelIndex].roleId}> ${guildData.exp.levels[levelIndex].exp} exp`);

        const roleSelect = new (require('discord.js').RoleSelectMenuBuilder)()
            .setCustomId(`exp-edit-level-role-${levelIndex}`)
            .setPlaceholder('select role')
            .setMinValues(1)
            .setMaxValues(1)
            .setDefaultRoles(guildData.exp.levels[levelIndex].roleId) // Keep current role as default
            .setDisabled(false);
        const roleRow = new ActionRowBuilder().addComponents(roleSelect);

        const expOptions = generateLinearExpOptions(
            levelIndex > 0 ? guildData.exp.levels[levelIndex - 1].exp : 0,
            levelIndex < guildData.exp.levels.length - 1
                ? guildData.exp.levels[levelIndex + 1].exp
                : guildData.exp.levels[levelIndex].exp + 2500,
            guildData.exp.levels[levelIndex].exp,
            100
        );
        const expSelect = new StringSelectMenuBuilder()
            .setCustomId(`exp-edit-level-exp-select-${levelIndex}`)
            .setPlaceholder('select exp value')
            .addOptions(expOptions)
            .setDisabled(false);
        const expRow = new ActionRowBuilder().addComponents(expSelect);

        // Status message
        const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ This role is already assigned to another level. Please select a different role.');

        const containerEphemeral = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(quote, details)
            .addActionRowComponents(roleRow)
            .addActionRowComponents(expRow)
            .addTextDisplayComponents(statusDisplay)
            .setAccentColor(OPERATION_COLORS.EDIT);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [containerEphemeral]
        });
        return;
    }

    const oldRole = guildData.exp.levels[levelIndex].roleId;
    guildData.exp.levels[levelIndex].roleId = selectedRole;
    await optimizedUpdateOne("guilds",
        { id: guildId },
        { $set: { 'exp.levels': guildData.exp.levels } }
    );

    // Send level edited log
    await sendExpLevelEditedLog(
        guildId,
        interaction.user.id,
        levelIndex + 1,
        { role: { old: oldRole, new: selectedRole } },
        interaction.client
    );

    // Build updated edit level UI with back button section (like owner.js pattern)
    const backButton = new ButtonBuilder()
        .setCustomId(`exp-edit-level-back-${levelIndex}`)
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(new TextDisplayBuilder().setContent(`# edit level ${levelIndex + 1}`))
        .setButtonAccessory(backButton);

    // Content section
    const quote = new TextDisplayBuilder().setContent('> edit the role and the exp required');
    const details = new TextDisplayBuilder().setContent(`<@&${selectedRole}> ${guildData.exp.levels[levelIndex].exp} exp`);

    const roleSelect = new (require('discord.js').RoleSelectMenuBuilder)()
        .setCustomId(`exp-edit-level-role-${levelIndex}`)
        .setPlaceholder('select role')
        .setMinValues(1)
        .setMaxValues(1)
        .setDefaultRoles(selectedRole)
        .setDisabled(false);
    const roleRow = new ActionRowBuilder().addComponents(roleSelect);

    const expOptions = generateLinearExpOptions(
        levelIndex > 0 ? guildData.exp.levels[levelIndex - 1].exp : 0,
        levelIndex < guildData.exp.levels.length - 1
            ? guildData.exp.levels[levelIndex + 1].exp
            : guildData.exp.levels[levelIndex].exp + 2500,
        guildData.exp.levels[levelIndex].exp,
        100
    );
    const expSelect = new StringSelectMenuBuilder()
        .setCustomId(`exp-edit-level-exp-select-${levelIndex}`)
        .setPlaceholder('select exp value')
        .addOptions(expOptions)
        .setDisabled(false);
    const expRow = new ActionRowBuilder().addComponents(expSelect);

    const containerEphemeral = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(quote, details)
        .addActionRowComponents(roleRow)
        .addActionRowComponents(expRow)
        .setAccentColor(OPERATION_COLORS.EDIT);
    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [containerEphemeral]
    });
}

// Main execute handler for EXP feature
async function execute(interaction) {
    try {
        // Defer the interaction immediately to prevent timeout
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
        }

        // Check permissions
        const hasPermission = hasExpPermission(interaction.member);

    // OPTIMIZED: Use cached guild configuration function
    let guildData = await getCachedGuildExpConfig(interaction.guild.id);
    if (!guildData) {
        guildData = { exp: { enabled: true } };
    }
    if (!guildData.exp) {
        guildData.exp = { enabled: true };
    }

    // Track performance metrics
    expMetrics.configsProcessed++;
    expMetrics.uiBuildsProcessed++;

    // Get subcomponent from interaction options if provided
    const subcomponent = interaction.options?.getString('subcomponent') || 'levels';

    // Build the container with the specified subcomponent
    const container = await buildExpContainer({
        subcomponent,
        enabled: guildData.exp.enabled,
        guildData,
        guild: interaction.guild,
        hasPermission,
        member: interaction.member,
        commandChannel: interaction.channel,
        user: interaction.user
    });
    // Build the select menu
    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
    // Global enable/disable button
    const globalExpButton = new ButtonBuilder()
        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(!hasPermission);
    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
    // Respond with editReply since we deferred
    await interaction.editReply({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [selectMenu, container, globalButtonRow]
    });
    } catch (error) {
        console.error('[exp] Error in execute function:', error);
        logger.error('exp', `Error in execute function: ${error.message}`, interaction.client);

        // Try to send a user-friendly error message
        try {
            if (interaction.deferred) {
                await interaction.editReply({
                    content: 'An error occurred while processing the EXP command. Please try again.',
                    components: []
                });
            } else {
                await interaction.reply({
                    content: 'An error occurred while processing the EXP command. Please try again.',
                    flags: MessageFlags.Ephemeral
                });
            }
        } catch (replyError) {
            console.error('[exp] Error sending error reply:', replyError);
        }
    }
}

// Button handler for global enable/disable
async function buttons(interaction) {
    // Check permissions for all button actions
    const hasPermission = hasExpPermission(interaction.member);
    if (!hasPermission) {
        // Show demo mode with status message
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };

        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission: false,
            statusMessage: '❌ You need Kick Members permission to configure EXP',
            member: interaction.member,
            commandChannel: interaction.channel
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(true);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    }
    // Handle voice enable/disable buttons
    if (interaction.customId === 'exp-voice-disable' || interaction.customId === 'exp-voice-enable') {
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { voice: { enabled: true } } };
        if (!guildData.exp) guildData.exp = { voice: { enabled: true } };
        if (!guildData.exp.voice) guildData.exp.voice = { enabled: true };

        // Toggle the voice enabled state
        const newState = interaction.customId === 'exp-voice-enable';
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.voice.enabled': newState } }
        );

        // Send feature toggle log
        await sendFeatureToggleLog(
            interaction.guild.id,
            'EXP System',
            'voice',
            newState,
            interaction.user.id,
            interaction.client
        );

        // Fetch updated data
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        // Build updated container with voice subcomponent
        const container = await buildExpContainer({
            subcomponent: 'voice',
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild
        });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    }

    // Handle text enable/disable buttons
    if (interaction.customId === 'exp-text-disable' || interaction.customId === 'exp-text-enable') {
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { text: { enabled: true } } };
        if (!guildData.exp) guildData.exp = { text: { enabled: true } };
        if (!guildData.exp.text) guildData.exp.text = { enabled: true };

        // Toggle the text enabled state
        const newState = interaction.customId === 'exp-text-enable';
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.text.enabled': newState } }
        );

        // Send feature toggle log
        await sendFeatureToggleLog(
            interaction.guild.id,
            'EXP System',
            'text',
            newState,
            interaction.user.id,
            interaction.client
        );

        // Fetch updated data
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        // Build updated container with text subcomponent
        const container = await buildExpContainer({
            subcomponent: 'text',
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild
        });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    }

    // Handle global enable/disable for EXP feature
    if (interaction.customId === 'exp-global-disable' || interaction.customId === 'exp-global-enable') {
        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };

        // Toggle the enabled state
        const newState = interaction.customId === 'exp-global-enable';
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.enabled': newState } }
        );

        // Send feature toggle log
        await sendFeatureToggleLog(
            interaction.guild.id,
            'EXP System',
            null, // No subcomponent for global toggle
            newState,
            interaction.user.id,
            interaction.client
        );

        // Fetch updated data
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });

        // Determine current subcomponent from the message components
        let currentSubcomponent = 'levels'; // default
        try {
            const messageContent = JSON.stringify(interaction.message.components);
            if (messageContent.includes('exp-text-config')) {
                currentSubcomponent = 'text';
            } else if (messageContent.includes('exp-voice-config')) {
                currentSubcomponent = 'voice';
            }
        } catch (e) {
            currentSubcomponent = 'levels';
        }

        // Build updated container with correct enabled state and preserve subcomponent
        const container = await buildExpContainer({
            subcomponent: currentSubcomponent,
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild
        });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        // Global enable/disable button always enabled, label and color reflect state
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    }
    // Handle create level role select (ephemeral create level flow)
    if (interaction.customId === 'exp-create-level-role') {
        const selectedRole = interaction.values[0];
        const userId = interaction.user.id;
        const guildId = interaction.guild.id;

        // Check if role is already used by another level
        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const levels = guildData?.exp?.levels ?? [];
        const isRoleUsed = levels.some(level => level.roleId === selectedRole);

        if (isRoleUsed) {
            // Stay on current level creation page with status message
            const nextLevel = levels.length + 1;
            const minExp = levels.length > 0 ? levels[levels.length - 1].exp : 0;

            // Build UI with back button section and status message
            const backButton = new ButtonBuilder()
                .setCustomId('exp-create-level-back')
                .setLabel('back')
                .setStyle(ButtonStyle.Secondary);
            const backSection = new SectionBuilder()
                .addTextDisplayComponents(new TextDisplayBuilder().setContent(`# create level ${nextLevel}`))
                .setButtonAccessory(backButton);

            // Content section
            const quote = new TextDisplayBuilder().setContent('> select the role and exp required');

            // Role select (no default since role was invalid)
            const roleSelectBuilder = new (require('discord.js').RoleSelectMenuBuilder)()
                .setCustomId('exp-create-level-role')
                .setPlaceholder('select role')
                .setMinValues(1)
                .setMaxValues(1)
                .setDisabled(false);
            const roleRow = new ActionRowBuilder().addComponents(roleSelectBuilder);

            // Exp select (disabled since no valid role selected)
            let minExpForExp = minExp;
            let maxExpForExp = minExpForExp + 2500;
            const expOptions = generateLinearExpOptions(minExpForExp, maxExpForExp, undefined, 100);
            const expSelect = new StringSelectMenuBuilder()
                .setCustomId('exp-create-level-exp-select')
                .setPlaceholder('select exp value')
                .addOptions(expOptions)
                .setDisabled(true); // Disabled until valid role is selected
            const expRow = new ActionRowBuilder().addComponents(expSelect);

            // Status message
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ This role is already assigned to another level. Please select a different role.');

            // Container
            const container = new ContainerBuilder()
                .addSectionComponents(backSection)
                .addTextDisplayComponents(quote)
                .addActionRowComponents(roleRow)
                .addActionRowComponents(expRow)
                .addTextDisplayComponents(statusDisplay)
                .setAccentColor(OPERATION_COLORS.ADD);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        // Store the selected role in a temp collection
        await optimizedUpdateOne("exp_create_level_temp",
            { userId, guildId },
            { $set: { roleId: selectedRole } },
            { upsert: true }
        );
        const tempRole = await optimizedFindOne("exp_create_level_temp", { userId, guildId });
        console.log('[exp-create-level-role] temp after setting role:', tempRole);
        // Use the levels data we already fetched above
        // Allow level 0 creation when no levels exist, otherwise increment
        const nextLevel = levels.length === 0 ? 0 : levels.length;
        const minExp = levels.length > 0 ? levels[levels.length - 1].exp : 0;
        // Check if exp is already set
        const temp = await optimizedFindOne("exp_create_level_temp", { userId, guildId });
        const expSet = temp?.exp;
        // Build UI with back button section (like owner.js pattern)
        const backButton = new ButtonBuilder()
            .setCustomId('exp-create-level-back')
            .setLabel('back')
            .setStyle(ButtonStyle.Secondary);
        const backSection = new SectionBuilder()
            .addTextDisplayComponents(new TextDisplayBuilder().setContent(`# create level ${nextLevel}`))
            .setButtonAccessory(backButton);

        // Content section
        const quote = new TextDisplayBuilder().setContent(nextLevel === 0 ?
            '> select the role' :
            '> select the role and exp required');

        // Show selected role in the UI if present
        let roleText = null;
        if (selectedRole) {
            roleText = new TextDisplayBuilder().setContent(`<@&${selectedRole}>`);
        }

        // Role select, pre-selected and enabled
        const roleSelectBuilder = new (require('discord.js').RoleSelectMenuBuilder)()
            .setCustomId('exp-create-level-role')
            .setPlaceholder('select role')
            .setMinValues(1)
            .setMaxValues(1)
            .setDisabled(false);
        if (selectedRole) {
            roleSelectBuilder.setDefaultRoles(selectedRole);
        }
        const roleRow = new ActionRowBuilder().addComponents(roleSelectBuilder);

        // Exp select (enabled if role is set)
        let minExpForExp = minExp;
        let maxExpForExp = minExpForExp + 2500;
        const expOptions = generateLinearExpOptions(minExpForExp, maxExpForExp,
            (typeof expSet === 'number' && !isNaN(expSet)) ? expSet : undefined, 100);
        const expSelect = new StringSelectMenuBuilder()
            .setCustomId('exp-create-level-exp-select')
            .setPlaceholder('select exp value')
            .addOptions(expOptions)
            .setDisabled(false);
        const expRow = new ActionRowBuilder().addComponents(expSelect);

        // Container
        const container = new ContainerBuilder()
            .addSectionComponents(backSection)
            .addTextDisplayComponents(roleText ? [quote, roleText] : [quote])
            .addActionRowComponents(roleRow)
            .addActionRowComponents(expRow)
            .setAccentColor(OPERATION_COLORS.ADD);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }
    // Handle add exp value button (ephemeral create level flow)
    if (interaction.customId === 'exp-create-level-exp') {
        // Show modal for exp input
        const expInput = new TextInputBuilder()
            .setCustomId('exp-create-level-exp-input')
            .setLabel('exp required')
            .setPlaceholder('Enter exp required for this level')
            .setRequired(true)
            .setMinLength(1)
            .setMaxLength(6)
            .setStyle(TextInputStyle.Short);
        const row = new ActionRowBuilder().addComponents(expInput);
        const modal = new ModalBuilder()
            .setCustomId('exp-create-level-modal')
            .setTitle('Set Exp for Level')
            .addComponents(row);
        await interaction.showModal(modal);
        return; // CRITICAL: Do not fall through to any other logic
    }
    if (interaction.customId.startsWith('exp-edit-level-role-')) {
        await expEditLevelRoleSelect(interaction);
        return;
    }

    // Handle unified level interface buttons
    if (interaction.customId === 'exp-create-level-final') {
        await handleCreateLevelFinal(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-modify-level-final-')) {
        await handleModifyLevelFinal(interaction);
        return;
    }
    if (interaction.customId === 'exp-unified-level-back') {
        await handleUnifiedLevelBack(interaction);
        return;
    }
    if (interaction.customId === 'exp-unified-level-role') {
        await handleUnifiedLevelRole(interaction);
        return;
    }
    if (interaction.customId === 'exp-unified-level-exp') {
        await handleUnifiedLevelExp(interaction);
        return;
    }
    if (interaction.customId === 'exp-unified-level-icon') {
        await handleUnifiedLevelIcon(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-edit-level-role-')) {
        await handleEditLevelRole(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-edit-level-exp-select-')) {
        await handleEditLevelExp(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-unified-edit-back-')) {
        await handleUnifiedEditBack(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-unified-edit-role-')) {
        await handleUnifiedEditRole(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-unified-edit-exp-')) {
        await handleUnifiedEditExp(interaction);
        return;
    }
    if (interaction.customId.startsWith('exp-unified-edit-icon-')) {
        await handleUnifiedEditIcon(interaction);
        return;
    }
}

// Add modal submit handler for editing a level
async function modalSubmit(interaction) {
    console.log('[modalSubmit] customId:', interaction.customId);
    let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
    if (!guildData) guildData = { exp: { text: {} } };
    if (!guildData.exp) guildData.exp = { text: {} };
    if (!guildData.exp.text) guildData.exp.text = {};

    if (interaction.customId === 'exp-level-msg-template-modal') {
        const value = interaction.fields.getTextInputValue('level-msg-template-input');
        console.log('[modalSubmit] level-msg-template-input value:', value);
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.levelMsg': value } }
        );
        // Fetch updated guildData
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true, levels: [] } };
        if (!guildData.exp) guildData.exp = { enabled: true, levels: [] };
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            member: interaction.member,
            commandChannel: interaction.channel,
            user: interaction.user
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'text-exp-per-min-modal') {
        const value = parseInt(interaction.fields.getTextInputValue('text-exp-per-min-input'));
        if (isNaN(value) || value < 1) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                subcomponent: 'text',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                statusMessage: 'Please enter a valid number greater than 0'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(false);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.text.expPerMin': value } }
        );
        // Fetch updated guildData
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({ 
            subcomponent: 'text',  // Keep in text subcomponent
            enabled: guildData.exp.enabled, 
            guildData, 
            guild: interaction.guild 
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'text-cooldown-modal') {
        const value = parseInt(interaction.fields.getTextInputValue('text-cooldown-input'));
        if (isNaN(value) || value < 1) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                subcomponent: 'text',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                statusMessage: 'Please enter a valid number greater than 0'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(false);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.text.cooldown': value } }
        );
        // Fetch updated guildData
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({ 
            subcomponent: 'text',  // Keep in text subcomponent
            enabled: guildData.exp.enabled, 
            guildData, 
            guild: interaction.guild 
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'text-min-chars-modal') {
        const value = parseInt(interaction.fields.getTextInputValue('text-min-chars-input'));
        if (isNaN(value) || value < 1) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                subcomponent: 'text',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                statusMessage: 'Please enter a valid number greater than 0'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(false);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.text.minChars': value } }
        );
        // Fetch updated guildData
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        if (!guildData) guildData = { exp: { enabled: true } };
        if (!guildData.exp) guildData.exp = { enabled: true };
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({ 
            subcomponent: 'text',  // Keep in text subcomponent
            enabled: guildData.exp.enabled, 
            guildData, 
            guild: interaction.guild 
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'voice-exp-per-min-modal') {
        const value = parseInt(interaction.fields.getTextInputValue('voice-exp-per-min-input'));
        if (isNaN(value) || value < 1) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                subcomponent: 'voice',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                statusMessage: 'Please enter a valid number greater than 0'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(false);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.voice.expPerMin': value } }
        );
        // Fetch updated data and rebuild UI
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({
            subcomponent: 'voice',
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    } else if (interaction.customId === 'voice-cooldown-modal') {
        const value = parseInt(interaction.fields.getTextInputValue('voice-cooldown-input'));
        if (isNaN(value) || value < 1) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const container = await buildExpContainer({
                subcomponent: 'voice',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                statusMessage: 'Please enter a valid number greater than 0'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(false);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }
        await optimizedUpdateOne("guilds",
            { id: interaction.guild.id },
            { $set: { 'exp.voice.cooldown': value } }
        );
        // Fetch updated data and rebuild UI
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const container = await buildExpContainer({
            subcomponent: 'voice',
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(false);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;

    } else if (interaction.customId === 'voice-msg-template-modal') {
        // Only allow bot owner to edit voice message template
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild container with status message instead of ephemeral reply
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
            const hasPermission = hasExpPermission(interaction.member);
            const container = await buildExpContainer({
                subcomponent: 'voice',
                enabled: guildData.exp.enabled,
                guildData,
                guild: interaction.guild,
                hasPermission,
                member: interaction.member,
                commandChannel: interaction.channel,
                user: interaction.user,
                statusMessage: '❌ Only the bot owner can edit voice messages'
            });
            const globalExpButton = new ButtonBuilder()
                .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
                .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
                .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
                .setDisabled(!hasPermission);
            const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, globalButtonRow]
            });
            return;
        }

        const value = interaction.fields.getTextInputValue('voice-msg-template-input');

        // Update all guilds with the new voice message template (bot-wide setting)
        await optimizedUpdateMany("guilds",
            { 'exp.enabled': { $ne: false } }, // Only update guilds that have EXP enabled
            { $set: { 'exp.voice.msg': value } }
        );

        // Fetch updated data and rebuild UI
        guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
        const hasPermission = hasExpPermission(interaction.member);
        const container = await buildExpContainer({
            subcomponent: 'voice',
            enabled: guildData.exp.enabled,
            guildData,
            guild: interaction.guild,
            hasPermission,
            user: interaction.user
        });
        const globalExpButton = new ButtonBuilder()
            .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
            .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
            .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission);
        const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, globalButtonRow]
        });
        return;
    }
}

// At the end of the file, export a handler for exp-refresh-config (Enterprise-Grade Optimized)
async function handleRefreshConfig(interaction) {
    // OPTIMIZED: Use cached guild configuration function
    let guildData = await getCachedGuildExpConfig(interaction.guild.id);
    if (!guildData) guildData = { exp: { enabled: true } };

    // Track performance metrics
    expMetrics.configsProcessed++;
    expMetrics.uiBuildsProcessed++;
    if (!guildData.exp) guildData.exp = { enabled: true };
    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
    const container = await buildExpContainer({
        enabled: guildData.exp.enabled,
        guildData,
        guild: interaction.guild,
        member: interaction.member,
        commandChannel: interaction.channel,
        user: interaction.user
    });
    const globalExpButton = new ButtonBuilder()
        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(false);
    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);
    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [selectMenu, container, globalButtonRow]
    });
}

// Handler for unified level interface - Create Level Final button
async function handleCreateLevelFinal(interaction) {
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // FIXED: Use cached temp state instead of direct database query for consistency
    const temp = await getCachedTempState("exp_create_level_temp", userId, guildId);

    // Get current levels to check if this is level 0
    let guildData = await optimizedFindOne("guilds", { id: guildId });
    if (!guildData) {
        guildData = { id: guildId, exp: { levels: [] } };
        // FIXED: Use optimizedInsertOne instead of undefined 'col' reference
        await optimizedInsertOne("guilds", guildData);
    }
    if (!guildData.exp) {
        guildData.exp = { levels: [] };
    }
    if (!guildData.exp.levels) {
        guildData.exp.levels = [];
    }

    const isLevel0 = guildData.exp.levels.length === 0;

    // For level 0, only require role (EXP will be auto-set to 0)
    // For other levels, require both role and EXP
    if (!temp?.roleId || (!isLevel0 && !temp?.exp)) {
        const missingFields = isLevel0 ? 'role' : 'role or exp value';
        // Error - rebuild container with status message (like items.js)
        const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);

        // Add status message to container
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ❌ Missing ${missingFields}. Please select required fields.`);
        container.addTextDisplayComponents(statusDisplay);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }

    // Auto-set EXP to 0 for level 0
    const expValue = isLevel0 ? 0 : temp.exp;

    // Add new level with icon support
    const newLevel = {
        roleId: temp.roleId,
        exp: expValue
    };

    // Add icon if provided
    if (temp.levelIcon) {
        newLevel.levelIcon = temp.levelIcon;
    }

    guildData.exp.levels.push(newLevel);
    await optimizedUpdateOne("guilds",
        { id: guildId },
        { $set: { 'exp.levels': guildData.exp.levels } }
    );

    // Send log with correct level number and EXP value
    const levelNumber = isLevel0 ? 0 : guildData.exp.levels.length;
    await sendExpLevelCreatedLog(
        guildId,
        interaction.user.id,
        levelNumber,
        temp.roleId,
        expValue,
        interaction.client
    );

    // Clean up temp state
    await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

    // FIXED: Invalidate temp state cache after deletion to prevent stale data
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // FIXED: Invalidate guild exp config cache to ensure main interface shows new level
    invalidateGuildExpConfigCache(guildId);

    // Return to main EXP interface
    await returnToMainExpInterface(interaction);
}

// Handler for unified level interface - Modify Level Final button
async function handleModifyLevelFinal(interaction) {
    const levelIndex = parseInt(interaction.customId.split('-').pop());
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // FIXED: Use cached temp state instead of direct database query for consistency
    const temp = await getCachedTempState("exp_create_level_temp", userId, guildId);

    // Get current level data to check if this is level 0
    const guildData = await optimizedFindOne("guilds", { id: guildId });
    const currentLevel = guildData?.exp?.levels?.[levelIndex];
    const isLevel0 = levelIndex === 0 && currentLevel?.exp === 0;

    // For level 0, only require role (EXP is locked at 0)
    // For other levels, require both role and EXP
    if (!temp?.roleId || (!isLevel0 && !temp?.exp)) {
        const missingFields = isLevel0 ? 'role' : 'role or exp value';
        const selectBoth = isLevel0 ? 'role' : 'both';

        // Error - rebuild container with status message (like items.js)
        const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

        // Add status message to container
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ❌ Missing ${missingFields}. Please select ${selectBoth}.`);
        container.addTextDisplayComponents(statusDisplay);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }

    // Update the level (reuse guildData from above)

    if (!guildData?.exp?.levels?.[levelIndex]) {
        // Error - rebuild container with status message (like items.js)
        const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

        // Add status message to container
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ❌ Level not found.`);
        container.addTextDisplayComponents(statusDisplay);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }

    const oldLevel = { ...guildData.exp.levels[levelIndex] };

    // Update level with icon support
    // For level 0, always force EXP to 0
    const updatedLevel = {
        roleId: temp.roleId,
        exp: isLevel0 ? 0 : temp.exp
    };

    // Add icon if provided
    if (temp.levelIcon) {
        updatedLevel.levelIcon = temp.levelIcon;
    }

    guildData.exp.levels[levelIndex] = updatedLevel;

    await optimizedUpdateOne("guilds",
        { id: guildId },
        { $set: { 'exp.levels': guildData.exp.levels } }
    );

    // Delete old emote if icon changed
    if (oldLevel.levelIcon && oldLevel.levelIcon !== temp.levelIcon) {
        await deleteOldEmote(oldLevel.levelIcon, interaction.client);
    }

    // Send log
    const changes = {};
    if (oldLevel.roleId !== temp.roleId) {
        changes.role = { old: oldLevel.roleId, new: temp.roleId };
    }
    if (oldLevel.exp !== temp.exp) {
        changes.exp = { old: oldLevel.exp, new: temp.exp };
    }

    await sendExpLevelEditedLog(
        guildId,
        interaction.user.id,
        levelIndex + 1,
        changes,
        interaction.client
    );

    // Clean up temp state
    await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

    // FIXED: Invalidate temp state cache after deletion to prevent stale data
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // FIXED: Invalidate guild exp config cache to ensure main interface shows updated level
    invalidateGuildExpConfigCache(guildId);

    // Return to main EXP interface
    await returnToMainExpInterface(interaction);
}

// Handler for unified level interface - Back button
async function handleUnifiedLevelBack(interaction) {
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // Clean up temp state
    await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

    // FIXED: Invalidate temp state cache after deletion to prevent stale data
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // Return to main EXP interface
    await returnToMainExpInterface(interaction);
}

// Handler for unified level interface - Role select
async function handleUnifiedLevelRole(interaction) {
    const selectedRole = interaction.values[0];
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;



    // Check if role is already used by another level
    const guildData = await optimizedFindOne("guilds", { id: guildId });
    const levels = guildData?.exp?.levels ?? [];
    const isRoleUsed = levels.some(level => level.roleId === selectedRole);

    if (isRoleUsed) {
        // Error - rebuild container with status message (like items.js)
        const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);

        // Add status message to container
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ❌ This role is already assigned to another level. Please select a different role.`);
        container.addTextDisplayComponents(statusDisplay);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }

    // Store the selected role in temp
    const updateData = { roleId: selectedRole };

    // Auto-set EXP to 0 for level 0
    const isLevel0 = levels.length === 0;
    if (isLevel0) {
        updateData.exp = 0;
    }

    await optimizedUpdateOne("exp_create_level_temp",
        { userId, guildId },
        { $set: updateData },
        { upsert: true }
    );

    // FIXED: Invalidate temp state cache to ensure fresh data is loaded
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // Rebuild unified container
    const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [container]
    });
}

// Handler for unified level interface - Exp select
async function handleUnifiedLevelExp(interaction) {
    const selectedExp = parseInt(interaction.values[0]);
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;



    // Store the selected exp in temp
    await optimizedUpdateOne("exp_create_level_temp",
        { userId, guildId },
        { $set: { exp: selectedExp } },
        { upsert: true }
    );

    // FIXED: Invalidate temp state cache to ensure fresh data is loaded
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // Rebuild unified container
    const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [container]
    });
}

// Handler for unified level interface - Icon select
async function handleUnifiedLevelIcon(interaction) {
    const value = interaction.values[0];
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // Handle "no-images" selection - just do nothing
    if (value === 'no-images') {
        // Just rebuild containers normally (no error message)
        const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }

    try {
        const selectedImageIndex = parseInt(value.replace('image-', ''));

        // Get fresh images
        const recentImages = await getRecentImagesFromChannel(interaction);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            // Rebuild container with status message instead of ephemeral reply
            const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Selected image not found. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        // UPDATED: Upload image as custom emote (application emote for consistency)
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            { useApplicationEmote: true } // Explicitly use application emojis
        );

        if (!emoteData) {
            // Rebuild container with status message instead of ephemeral reply
            const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Failed to upload image as emote. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        // CRITICAL FIX: Invalidate image cache after upload to ensure newly uploaded images appear in selector
        try {
            const { invalidateImageCache } = require('../../utils/imageUploader.js');
            await invalidateImageCache(interaction.user.id, interaction.guild.id);
            console.log(`[exp] 🔄 Invalidated image cache after level icon upload for user ${interaction.user.id}`);
        } catch (cacheError) {
            console.error('[exp] Error invalidating image cache after level icon upload:', cacheError);
        }

        // Store the emote string in temp
        await optimizedUpdateOne("exp_create_level_temp",
            { userId, guildId },
            { $set: { levelIcon: emoteData.string } },
            { upsert: true }
        );

        // FIXED: Invalidate temp state cache to ensure fresh data is loaded
        invalidateTempStateCache("exp_create_level_temp", userId, guildId);

        // Rebuild unified container (exactly like global levels)
        const container = await buildUnifiedLevelContainer(userId, guildId, false, null, interaction);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });

    } catch (error) {
        console.error('[handleUnifiedLevelIcon] Error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ An error occurred while processing the image. Please try again.',
                flags: MessageFlags.Ephemeral
            });
        }
    }
}

// Handler for editing level role select (unified interface)
async function handleEditLevelRole(interaction) {
    const levelIndex = parseInt(interaction.customId.split('-').pop());
    const selectedRole = interaction.values[0];
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // Check if role is already used by another level
    const guildData = await optimizedFindOne("guilds", { id: guildId });
    const levels = guildData?.exp?.levels ?? [];
    const isRoleUsed = levels.some((level, index) => level.roleId === selectedRole && index !== levelIndex);

    if (isRoleUsed) {
        // Error - rebuild container with status message (like items.js)
        // FIXED: Use true for isEditing to stay in edit mode
        const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

        // Add status message to container
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ❌ This role is already assigned to another level. Please select a different role.`);
        container.addTextDisplayComponents(statusDisplay);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
        return;
    }

    // Store the selected role in temp
    await optimizedUpdateOne("exp_create_level_temp",
        { userId, guildId },
        { $set: { roleId: selectedRole } },
        { upsert: true }
    );

    // FIXED: Invalidate temp state cache to ensure fresh data is loaded
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // Rebuild unified container for editing
    const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [container]
    });
}

// Handler for unified edit back button (doesn't save data)
async function handleUnifiedEditBack(interaction) {
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // Clean up temp state without saving
    await optimizedDeleteOne("exp_create_level_temp", { userId, guildId });

    // Return to main EXP interface
    await returnToMainExpInterface(interaction);
}

// Handler for unified edit role select (new custom ID)
async function handleUnifiedEditRole(interaction) {
    try {
        const levelIndex = parseInt(interaction.customId.split('-').pop());
        const selectedRole = interaction.values[0];
        const userId = interaction.user.id;
        const guildId = interaction.guild.id;

        console.log('[handleUnifiedEditRole] Processing:', { levelIndex, selectedRole, userId, guildId });

        // Check if role is already used by another level
        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const levels = guildData?.exp?.levels ?? [];
        const isRoleUsed = levels.some((level, index) => level.roleId === selectedRole && index !== levelIndex);

        if (isRoleUsed) {
            // Error - rebuild container with status message (like items.js)
            const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

            // Add status message to container
            const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ❌ This role is already assigned to another level. Please select a different role.`);
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        // Store the selected role in temp
        await optimizedUpdateOne("exp_create_level_temp",
            { userId, guildId },
            { $set: { roleId: selectedRole } },
            { upsert: true }
        );

        // FIXED: Invalidate temp state cache to ensure fresh data is loaded
        invalidateTempStateCache("exp_create_level_temp", userId, guildId);

        // Rebuild unified container for editing
        const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
    } catch (error) {
        console.error('[handleUnifiedEditRole] Error:', error);
        try {
            // Try to rebuild container with error status message
            const levelIndex = parseInt(interaction.customId.split('-').pop());
            const userId = interaction.user.id;
            const guildId = interaction.guild.id;

            const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ An error occurred while updating the role.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
        } catch (fallbackErr) {
            console.error('[handleUnifiedEditRole] Fallback error:', fallbackErr);
            await interaction.reply({ content: '❌ An error occurred while updating the role.', flags: MessageFlags.Ephemeral });
        }
    }
}

// Handler for unified edit exp select (new custom ID)
async function handleUnifiedEditExp(interaction) {
    try {
        const levelIndex = parseInt(interaction.customId.split('-').pop());
        const selectedExp = parseInt(interaction.values[0]);
        const userId = interaction.user.id;
        const guildId = interaction.guild.id;

        console.log('[handleUnifiedEditExp] Processing:', { levelIndex, selectedExp, userId, guildId });

        // Store the selected exp in temp
        await optimizedUpdateOne("exp_create_level_temp",
            { userId, guildId },
            { $set: { exp: selectedExp } },
            { upsert: true }
        );

        // FIXED: Invalidate temp state cache to ensure fresh data is loaded
        invalidateTempStateCache("exp_create_level_temp", userId, guildId);

        // Rebuild unified container for editing
        const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
    } catch (error) {
        console.error('[handleUnifiedEditExp] Error:', error);
        try {
            // Try to rebuild container with error status message
            const levelIndex = parseInt(interaction.customId.split('-').pop());
            const userId = interaction.user.id;
            const guildId = interaction.guild.id;

            const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ An error occurred while updating the EXP value.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
        } catch (fallbackErr) {
            console.error('[handleUnifiedEditExp] Fallback error:', fallbackErr);
            await interaction.reply({ content: '❌ An error occurred while updating the EXP value.', flags: MessageFlags.Ephemeral });
        }
    }
}

// Handler for unified edit icon select
async function handleUnifiedEditIcon(interaction) {
    try {
        const levelIndex = parseInt(interaction.customId.split('-').pop());
        const value = interaction.values[0];
        const userId = interaction.user.id;
        const guildId = interaction.guild.id;

        // Handle "no-images" selection - just do nothing
        if (value === 'no-images') {
            // FIXED: Use true for isEditing to stay in edit mode instead of switching to creation mode
            const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        const selectedImageIndex = parseInt(value.replace('image-', ''));

        // Get fresh images
        const recentImages = await getRecentImagesFromChannel(interaction);
        const selectedImage = recentImages[selectedImageIndex];

        if (!selectedImage) {
            // Rebuild container with status message instead of ephemeral reply
            const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Selected image not found. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        // UPDATED: Upload image as custom emote (application emote for consistency)
        const emoteData = await uploadImageAsEmote(
            selectedImage.url,
            selectedImage.filename,
            interaction.guild.id,
            interaction.client,
            { useApplicationEmote: true } // Explicitly use application emojis
        );

        if (!emoteData) {
            // Rebuild container with status message instead of ephemeral reply
            const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** ❌ Failed to upload image as emote. Please try again.');
            container.addTextDisplayComponents(statusDisplay);

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }

        // CRITICAL FIX: Invalidate image cache after upload to ensure newly uploaded images appear in selector
        try {
            const { invalidateImageCache } = require('../../utils/imageUploader.js');
            await invalidateImageCache(interaction.user.id, interaction.guild.id);
            console.log(`[exp] 🔄 Invalidated image cache after edit level icon upload for user ${interaction.user.id}`);
        } catch (cacheError) {
            console.error('[exp] Error invalidating image cache after edit level icon upload:', cacheError);
        }

        // Store the emote string in temp
        await optimizedUpdateOne("exp_create_level_temp",
            { userId, guildId },
            { $set: { levelIcon: emoteData.string } },
            { upsert: true }
        );

        // FIXED: Invalidate temp state cache to ensure fresh data is loaded
        invalidateTempStateCache("exp_create_level_temp", userId, guildId);

        // Rebuild unified container for editing (exactly like global levels)
        const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
    } catch (error) {
        console.error('[handleUnifiedEditIcon] Error:', error);
        if (!interaction.replied && !interaction.deferred) {
            await interaction.reply({
                content: '❌ An error occurred while processing the image. Please try again.',
                flags: MessageFlags.Ephemeral
            });
        }
    }
}

// Handler for editing level exp select (unified interface)
async function handleEditLevelExp(interaction) {
    const levelIndex = parseInt(interaction.customId.split('-').pop());
    const selectedExp = parseInt(interaction.values[0]);
    const userId = interaction.user.id;
    const guildId = interaction.guild.id;

    // Store the selected exp in temp
    await optimizedUpdateOne("exp_create_level_temp",
        { userId, guildId },
        { $set: { exp: selectedExp } },
        { upsert: true }
    );

    // FIXED: Invalidate temp state cache to ensure fresh data is loaded
    invalidateTempStateCache("exp_create_level_temp", userId, guildId);

    // Rebuild unified container for editing
    const container = await buildUnifiedLevelContainer(userId, guildId, true, levelIndex, interaction);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [container]
    });
}

// Helper function to return to main EXP interface (Enterprise-Grade Optimized)
async function returnToMainExpInterface(interaction) {
    // OPTIMIZED: Use cached guild configuration function
    let guildData = await getCachedGuildExpConfig(interaction.guild.id);
    if (!guildData) guildData = { exp: { enabled: true, levels: [] } };
    if (!guildData.exp) guildData.exp = { enabled: true, levels: [] };

    // Track performance metrics
    expMetrics.uiBuildsProcessed++;

    const selectMenu = buildSelectMenu(true, interaction.user.id, 'exp');
    const hasPermission = hasExpPermission(interaction.member);
    const container = await buildExpContainer({
        enabled: guildData.exp.enabled,
        guildData,
        guild: interaction.guild,
        hasPermission,
        member: interaction.member,
        commandChannel: interaction.channel,
        user: interaction.user
    });
    const globalExpButton = new ButtonBuilder()
        .setCustomId(guildData.exp.enabled ? 'exp-global-disable' : 'exp-global-enable')
        .setLabel(guildData.exp.enabled ? 'disable' : 'enable')
        .setStyle(guildData.exp.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
        .setDisabled(!hasPermission);
    const globalButtonRow = new ActionRowBuilder().addComponents(globalExpButton);

    await interaction.update({
        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
        components: [selectMenu, container, globalButtonRow]
    });
}

module.exports = {
    buildExpContainer,
    select,
    execute,
    buttons,
    modalSubmit,
    expCreateLevelExpSelect,
    expEditLevelExpSelect,
    expCreateLevelBack,
    expEditLevelBack,
    buildBackButton,
    expLevelChannelBack,
    expEditLevelRoleSelect,
    handleRefreshConfig,
    buildUnifiedLevelContainer,
    handleCreateLevelFinal,
    handleModifyLevelFinal,
    handleUnifiedLevelBack,
    handleUnifiedLevelRole,
    handleUnifiedLevelExp,
    handleUnifiedLevelIcon,
    handleEditLevelRole,
    handleEditLevelExp,
    handleUnifiedEditBack,
    handleUnifiedEditRole,
    handleUnifiedEditExp,
    handleUnifiedEditIcon,
    returnToMainExpInterface,
    MAX_LEVELS, // Export MAX_LEVELS constant for use in other files
};
