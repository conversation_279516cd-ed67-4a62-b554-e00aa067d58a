const { Container<PERSON><PERSON>er, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, ModalBuilder, TextInputBuilder, TextInputStyle, ActivityType, PresenceUpdateStatus } = require('discord.js');
const { mongoClient } = require('../../mongo/client.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');

/**
 * Owner Status Management Module
 * Extracted from owner.js to reduce file size and improve maintainability
 * Handles bot status and presence management functionality
 */

/**
 * Build status management container - EXACT copy of original status.js execute function
 * @param {Object} interaction - Discord interaction (mimicking original)
 * @returns {ContainerBuilder} Status container
 */
async function buildStatusContainer(interaction) {
    var clientData = await optimizedFindOne("clients", { id: interaction.client.user.id });

    const CUSTOM_ACTIVITY_TYPE = 4; // Custom
    const ROTATION_OPTIONS = [
        { label: '1 minute', value: '60', seconds: 60 },
        { label: '10 minutes', value: '600', seconds: 600 },
        { label: '30 minutes', value: '1800', seconds: 1800 },
        { label: '1 hour', value: '3600', seconds: 3600 },
        { label: '2 hours', value: '7200', seconds: 7200 },
        { label: '3 hours', value: '10800', seconds: 10800 }
    ];

    if (clientData == null) {
        await optimizedInsertOne("clients",
            {
                id: interaction.client.user.id,
                presence: { type: CUSTOM_ACTIVITY_TYPE, status: 'online', descriptions: [], rotation: 60, lastRotate: Date.now() },
            }
        )
        clientData = await optimizedFindOne("clients", { id: interaction.client.user.id });
    }

    let data = clientData.presence;
    // Always use custom activity type
    if (data.type !== CUSTOM_ACTIVITY_TYPE) {
        data.type = CUSTOM_ACTIVITY_TYPE;
        await optimizedUpdateOne("clients", { id: interaction.client.user.id }, { $set: { "presence.type": CUSTOM_ACTIVITY_TYPE } });
    }
    if (!data.rotation) data.rotation = 60;
    if (!data.lastRotate) data.lastRotate = Date.now();
    if (!Array.isArray(data.descriptions)) data.descriptions = [];

    // Section with back button
    const heading = new TextDisplayBuilder().setContent('# status');
    const quote = new TextDisplayBuilder().setContent('> only for my eyes');

    const backButton = new ButtonBuilder()
        .setCustomId('owner-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);

    // Status info (all in one text display)
    const rotationLabel = ROTATION_OPTIONS.find(opt => Number(opt.value) === Number(data.rotation))?.label || `${data.rotation} seconds`;
    const statusInfo = `**status:** ${data.status}\n**rotation time:** ${rotationLabel} **last rotate:** <t:${Math.floor((data.lastRotate || Date.now())/1000)}:R>\n**status message(s):**\n${data.descriptions.length ? data.descriptions.join('\n') : "`none`"}`;
    const statusText = new TextDisplayBuilder().setContent(statusInfo);

    // Status select menu
    const statusSelect = new StringSelectMenuBuilder()
            .setCustomId("status-status")
            .setPlaceholder(data.status)
            .setOptions(...Object.keys(PresenceUpdateStatus)
            .filter(s => !["Invisible", data.status].includes(s))
            .map(stat => { return { label: stat.split(/(?=[A-Z])/).join(' '), value: PresenceUpdateStatus[stat] } }));
    const statusRow = new ActionRowBuilder().addComponents(statusSelect);

    // Rotation time select menu
    const rotationSelect = new StringSelectMenuBuilder()
        .setCustomId("status-rotation")
        .setPlaceholder(rotationLabel)
        .setOptions(...ROTATION_OPTIONS.map(opt => ({ label: opt.label, value: opt.value, default: Number(opt.value) === Number(data.rotation) })));
    const rotationRow = new ActionRowBuilder().addComponents(rotationSelect);

    // Status messages select menu
    const statusMsgOptions = [
        { label: '➕ New status message', value: 'new' },
        ...data.descriptions.map((desc, i) => {
            // Try to parse emoji and message
            let emoji = '';
            let msg = desc;
            const match = desc.match(/^(.{1,2})\s(.+)/);
            if (match) {
                emoji = match[1];
                msg = match[2];
            }
            return {
                label: `${emoji} ${msg.substring(0, 32)}`.trim(),
                value: String(i),
                description: msg.length > 32 ? msg.substring(0, 32) + '...' : undefined
            };
        })
    ];
    const statusMsgSelect = new StringSelectMenuBuilder()
        .setCustomId('status-msg')
        .setPlaceholder('status messages')
        .setOptions(...statusMsgOptions);
    const statusMsgRow = new ActionRowBuilder().addComponents(statusMsgSelect);

    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

    // Build container
    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(quote, statusText)
        .addSeparatorComponents(separator)
        .addActionRowComponents(statusRow, rotationRow, statusMsgRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    return container;
}

/**
 * Handle status select menu interactions for owner interface
 * @param {Object} interaction - Discord select menu interaction
 * @returns {ContainerBuilder|null} Updated status container or null if modal was shown
 */
async function handleStatusSelect(interaction) {
    var clientData = await optimizedFindOne("clients", { id: interaction.client.user.id });
    let data = clientData.presence;
    let updated = false;

    if (interaction.customId === 'status-status') {
        const value = interaction.values[0];
        data.status = value;
        updated = true;
    } else if (interaction.customId === 'status-rotation') {
        const value = interaction.values[0];
        data.rotation = Number(value);
        data.lastRotate = Date.now();
        updated = true;
        // Immediately re-evaluate the rotation worker (lazy global access)
        if (global.dynamicRotateStatus && global.discordClient) {
            global.dynamicRotateStatus(global.discordClient);
        }
    } else if (interaction.customId === 'status-msg') {
        const value = interaction.values[0];
        if (value === 'new') {
            // Show modal for new status message with owner-specific customId
            const modal = new ModalBuilder()
                .setCustomId('owner-status-msg-modal-new')
                .setTitle('New Status Message');
            const emojiInput = new TextInputBuilder()
                .setCustomId('emoji')
                .setLabel('Emoji (optional)')
                .setStyle(TextInputStyle.Short)
                .setRequired(false)
                .setMaxLength(2);
            const msgInput = new TextInputBuilder()
                .setCustomId('msg')
                .setLabel('Message')
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(true)
                .setMaxLength(128);
            modal.addComponents(
                new ActionRowBuilder().addComponents(emojiInput),
                new ActionRowBuilder().addComponents(msgInput)
            );
            await interaction.showModal(modal);
            return null;
        } else {
            // Edit existing message
            const idx = Number(value);
            const desc = data.descriptions[idx];
            let emoji = '';
            let msg = desc;
            const match = desc.match(/^(.{1,2})\s(.+)/);
            if (match) {
                emoji = match[1];
                msg = match[2];
            }

            const modal = new ModalBuilder()
                .setCustomId(`owner-status-msg-modal-edit-${idx}`)
                .setTitle('Edit Status Message');
            const emojiInput = new TextInputBuilder()
                .setCustomId('emoji')
                .setLabel('Emoji (optional)')
                .setStyle(TextInputStyle.Short)
                .setRequired(false)
                .setMaxLength(2)
                .setValue(emoji);
            const msgInput = new TextInputBuilder()
                .setCustomId('msg')
                .setLabel('Message')
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(true)
                .setMaxLength(128)
                .setValue(msg);
            modal.addComponents(
                new ActionRowBuilder().addComponents(emojiInput),
                new ActionRowBuilder().addComponents(msgInput)
            );
            await interaction.showModal(modal);
            return null;
        }
    }

    if (updated) {
        await optimizedUpdateOne("clients", { id: interaction.client.user.id }, { $set: { presence: data } });
    }

    return await buildStatusContainer(interaction);
}

/**
 * Handle status modal submissions for owner interface
 * @param {Object} interaction - Discord modal interaction
 * @returns {ContainerBuilder} Updated status container
 */
async function handleStatusModal(interaction) {
    try {
        console.log('[owner-status] handleStatusModal called with customId:', interaction.customId);

        var clientData = await optimizedFindOne("clients", { id: interaction.client.user.id });

        const CUSTOM_ACTIVITY_TYPE = 4; // Custom

        // Initialize client data if it doesn't exist (same logic as buildStatusContainer)
        if (clientData == null) {
            console.log('[owner-status] Creating new client data');
            await optimizedInsertOne("clients",
                {
                    id: interaction.client.user.id,
                    presence: { type: CUSTOM_ACTIVITY_TYPE, status: 'online', descriptions: [], rotation: 60, lastRotate: Date.now() },
                }
            )
            clientData = await optimizedFindOne("clients", { id: interaction.client.user.id });
        }

        let data = clientData.presence;

        // Ensure data has all required fields (same logic as buildStatusContainer)
        if (!data.rotation) data.rotation = 60;
        if (!data.lastRotate) data.lastRotate = Date.now();
        if (!Array.isArray(data.descriptions)) data.descriptions = [];

        if (interaction.customId === 'owner-status-msg-modal-new') {
            console.log('[owner-status] Processing new status message modal');
            const emoji = interaction.fields.getTextInputValue('emoji') || '';
            const msg = interaction.fields.getTextInputValue('msg');
            const newMsg = emoji ? `${emoji} ${msg}` : msg;
            console.log('[owner-status] New message:', newMsg);
            data.descriptions.push(newMsg);
            await optimizedUpdateOne("clients", { id: interaction.client.user.id }, { $set: { 'presence.descriptions': data.descriptions } });
            console.log('[owner-status] Database updated, new descriptions count:', data.descriptions.length);
        } else if (interaction.customId.startsWith('owner-status-msg-modal-edit-')) {
            console.log('[owner-status] Processing edit status message modal');
            const idx = Number(interaction.customId.replace('owner-status-msg-modal-edit-', ''));
            const emoji = interaction.fields.getTextInputValue('emoji') || '';
            const msg = interaction.fields.getTextInputValue('msg');
            const newMsg = emoji ? `${emoji} ${msg}` : msg;
            console.log('[owner-status] Editing message at index', idx, 'to:', newMsg);
            data.descriptions[idx] = newMsg;
            await optimizedUpdateOne("clients", { id: interaction.client.user.id }, { $set: { 'presence.descriptions': data.descriptions } });
            console.log('[owner-status] Database updated');
        }

        // Return updated status container for owner interface
        console.log('[owner-status] Building status container');
        const container = await buildStatusContainer(interaction);
        console.log('[owner-status] Container built successfully');
        return container;
    } catch (error) {
        console.error('[owner-status] Error in handleStatusModal:', error);
        throw error;
    }
}

module.exports = {
    buildStatusContainer,
    handleStatusSelect,
    handleStatusModal
};
