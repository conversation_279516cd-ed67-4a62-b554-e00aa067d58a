const { ContextMenuCommandBuilder, ApplicationCommandType, PermissionFlagsBits, ButtonBuilder, ButtonStyle, ContainerBuilder, SectionBuilder, TextDisplayBuilder, ThumbnailBuilder, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, MessageFlags, ActivityType } = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { optimizedFindOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');
const { incrementCommandUsage } = require("../../utils/commandUsage.js");
const { CacheFactory, registerCache } = require('../../utils/LRUCache.js');

/**
 * User Lookup System (Enterprise-Grade Performance Optimized)
 * Handles user information lookups with comprehensive optimization and performance monitoring
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance analytics
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const lookupMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    lookupsProcessed: 0,
    expCalculationsProcessed: 0,
    levelCalculationsProcessed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildExpConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild exp configurations
const memberDataCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for member data
const expCalculationCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for exp calculations
const levelDataCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for level data

// Register caches for global cleanup
registerCache(guildExpConfigCache);
registerCache(memberDataCache);
registerCache(expCalculationCache);
registerCache(levelDataCache);

/**
 * Get cached guild experience configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild experience configuration data
 */
async function getCachedGuildExpConfig(guildId) {
    const startTime = Date.now();
    const cacheKey = `guild_exp_config_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildExpConfigCache.get(cacheKey);
        if (cached) {
            lookupMetrics.cacheHits++;
            if (lookupMetrics.verboseLogging) {
                console.log(`[lookup] ⚡ Guild exp config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        lookupMetrics.cacheMisses++;
        lookupMetrics.databaseQueries++;

        // Get guild data
        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const config = guildData || { exp: { enabled: false } };

        // Ensure exp structure exists
        if (!config.exp) {
            config.exp = { enabled: false };
        }

        // Cache the result
        guildExpConfigCache.set(cacheKey, config);

        const duration = Date.now() - startTime;
        lookupMetrics.averageQueryTime =
            (lookupMetrics.averageQueryTime * (lookupMetrics.databaseQueries - 1) + duration) /
            lookupMetrics.databaseQueries;

        if (lookupMetrics.verboseLogging || duration > 100) {
            console.log(`[lookup] ✅ Guild exp config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        return config;
    } catch (error) {
        console.error(`[lookup] ❌ Error getting guild exp config for ${guildId}:`, error);
        return { exp: { enabled: false } };
    }
}

/**
 * Get cached member data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for member data lookups
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Member data
 */
async function getCachedMemberData(userId, guildId) {
    const startTime = Date.now();
    const cacheKey = `member_${userId}_${guildId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = memberDataCache.get(cacheKey);
        if (cached) {
            lookupMetrics.cacheHits++;
            if (lookupMetrics.verboseLogging) {
                console.log(`[lookup] ⚡ Member data cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        lookupMetrics.cacheMisses++;
        lookupMetrics.databaseQueries++;

        // Get member data
        const memberData = await optimizedFindOne("member", {
            guildId: guildId,
            userId: userId
        });

        // Cache the result (including null results to avoid repeated queries)
        memberDataCache.set(cacheKey, memberData);

        const duration = Date.now() - startTime;
        lookupMetrics.averageQueryTime =
            (lookupMetrics.averageQueryTime * (lookupMetrics.databaseQueries - 1) + duration) /
            lookupMetrics.databaseQueries;

        if (lookupMetrics.verboseLogging || duration > 50) {
            console.log(`[lookup] ✅ Member data fetched for ${userId}: ${duration}ms - cached for future access`);
        }

        return memberData;
    } catch (error) {
        console.error(`[lookup] ❌ Error getting member data for ${userId}:`, error);
        return null;
    }
}

/**
 * Calculate cached experience level data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring for level calculations
 * @param {number} totalExp - Total experience points
 * @param {Array} levels - Guild level configuration
 * @param {string} cacheKey - Cache key for this calculation
 * @returns {Object} Level calculation results
 */
function getCachedExpLevelData(totalExp, levels, cacheKey) {
    const startTime = Date.now();

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = expCalculationCache.get(cacheKey);
        if (cached) {
            lookupMetrics.cacheHits++;
            if (lookupMetrics.verboseLogging) {
                console.log(`[lookup] ⚡ Exp calculation cache hit (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        lookupMetrics.cacheMisses++;
        lookupMetrics.expCalculationsProcessed++;
        lookupMetrics.levelCalculationsProcessed++;

        // Calculate current level
        let levelIndex = -1;
        for (let i = levels.length - 1; i >= 0; i--) {
            if (totalExp >= levels[i].exp) {
                levelIndex = i;
                break;
            }
        }

        let currentLevel, nextLevelExp;
        if (levelIndex >= 0) {
            currentLevel = levelIndex + 1;
            // Find next level EXP requirement
            if (levelIndex + 1 < levels.length) {
                nextLevelExp = levels[levelIndex + 1].exp;
            } else {
                nextLevelExp = 'MAX';
            }
        } else {
            currentLevel = 0;
            if (levels.length > 0) {
                nextLevelExp = levels[0].exp;
            } else {
                nextLevelExp = 'N/A';
            }
        }

        const result = {
            currentLevel,
            nextLevelExp,
            totalExp
        };

        // Cache the result
        expCalculationCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        if (lookupMetrics.verboseLogging || duration > 10) {
            console.log(`[lookup] ✅ Exp level calculated: ${duration}ms - cached for future access`);
        }

        return result;
    } catch (error) {
        console.error(`[lookup] ❌ Error calculating exp level:`, error);
        return {
            currentLevel: 'N/A',
            nextLevelExp: 'N/A',
            totalExp: totalExp || 0
        };
    }
}

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('who r u?')
        .setType(ApplicationCommandType.User)
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    async execute(interaction) {
        try {
            // Track command usage
            await incrementCommandUsage('lookup');

            console.log('[lookup] Context menu command triggered for user:', interaction.targetUser?.tag);

            const targetUser = interaction.targetUser;
            const targetMember = interaction.targetMember;

            // If user tries to look up themselves, suggest using /you instead
            if (targetUser.id === interaction.user.id) {
                await interaction.reply({
                    content: 'no </you:1161326973663060030>',
                    ephemeral: true
                });
                return;
            }

        await targetUser.fetch();
        if (targetMember) await targetMember.fetch();

        // User/server info
        let serverInfo = '';
        if (targetMember) {
            serverInfo = `joined: <t:${Math.floor(targetMember.joinedTimestamp / 1000)}:D>`;
            if (targetMember.roles.cache.size > 1) serverInfo += `\nroles: ${targetMember.roles.cache.filter(r => interaction.guild.id != r.id).map(r => r).join(" ")}`;
            if (targetMember.nickname) serverInfo += `\nnickname: ${targetMember.nickname}`;
            if (targetMember.premiumSince) serverInfo += `\nboosting since: <t:${Math.floor(targetMember.premiumSince / 1000)}:D>`;
        } else {
            serverInfo = 'not in server';
        }

        // User info
        const userFlags = await targetUser.fetchFlags();
        const badges = userFlags.toArray().map(f => f.replace(/([a-z])([A-Z])/g, '$1 $2')).join(', ');
        let userInfo = `id: ${targetUser.id}\nname: ${targetUser.tag}\nmention: ${targetUser}\ncreated: <t:${Math.floor(targetUser.createdTimestamp / 1000)}:D>`;
        if (badges) userInfo += `\nbadges: ${badges}`;

        // OPTIMIZED: Fetch EXP data with parallel processing and caching
        let exp = 'N/A';
        let currentLevel = 'N/A';
        let nextLevelExp = 'N/A';

        try {
            // OPTIMIZED: Parallel data fetching with Promise.allSettled
            const [guildDataResult, memberDataResult] = await Promise.allSettled([
                getCachedGuildExpConfig(interaction.guild.id),
                getCachedMemberData(targetUser.id, interaction.guild.id)
            ]);

            // Track parallel operation
            lookupMetrics.parallelOperations++;

            // Handle results with graceful fallbacks
            const guildData = guildDataResult.status === 'fulfilled' ? guildDataResult.value : { exp: { enabled: false } };
            const memberData = memberDataResult.status === 'fulfilled' ? memberDataResult.value : null;

            // Track partial failures
            if (guildDataResult.status === 'rejected' || memberDataResult.status === 'rejected') {
                lookupMetrics.partialFailures++;
                if (lookupMetrics.verboseLogging) {
                    console.log(`[lookup] ⚠️ Partial failure in parallel data fetch`);
                }
            }

            if (guildData?.exp?.enabled) {
                if (memberData?.exp?.total !== undefined) {
                    const totalExp = memberData.exp.total;
                    exp = totalExp;

                    // OPTIMIZED: Use cached level calculation
                    const levels = guildData.exp.levels ?? [];
                    const cacheKey = `exp_level_${totalExp}_${levels.length}_${guildData.id || interaction.guild.id}`;
                    const levelData = getCachedExpLevelData(totalExp, levels, cacheKey);

                    currentLevel = levelData.currentLevel;
                    nextLevelExp = levelData.nextLevelExp;
                } else {
                    exp = 0;
                    currentLevel = 0;
                    const levels = guildData.exp.levels ?? [];
                    if (levels.length > 0) {
                        nextLevelExp = levels[0].exp;
                    }
                }
            }

            // Track performance metrics
            lookupMetrics.lookupsProcessed++;

        } catch (error) {
            console.error('[lookup] Error fetching EXP data:', error);
            lookupMetrics.partialFailures++;
        }

        // Bot specifics
        let botSpecifics = '';
        if (targetMember) {
            // Status
            const status = targetMember.presence?.status;
            if (status) botSpecifics += `status: ${status}\n`;
            // Client
            const clientStatus = targetMember.presence?.clientStatus ? Object.keys(targetMember.presence.clientStatus).join(', ') : null;
            if (clientStatus) botSpecifics += `client: ${clientStatus}\n`;
            // Custom Status
            const activities = targetMember.presence?.activities || [];
            const customStatus = activities.find(a => a.type === ActivityType.Custom);
            const customStatusText = customStatus ? `${customStatus.emoji?.name || ''} ${customStatus.state || ''}`.trim() : null;
            if (customStatusText) botSpecifics += `custom status: ${customStatusText}\n`;
        }
        
        // exp
        if (exp !== 'N/A') {
            botSpecifics += `exp: ${exp}`;
            if (currentLevel !== 'N/A') {
                botSpecifics += ` | level: ${currentLevel}`;
                if (nextLevelExp !== 'N/A' && nextLevelExp !== 'MAX') {
                    const remaining = nextLevelExp - exp;
                    botSpecifics += ` | next: ${remaining} EXP to level ${parseInt(currentLevel) + 1}`;
                } else if (nextLevelExp === 'MAX') {
                    botSpecifics += ` | MAX LEVEL`;
                }
            }
        } else {
            botSpecifics += `exp: ${exp}`;
        }

        // Color: use 17's yellow
        const accentColor = OPERATION_COLORS.ENTITY;

        // Profile image as thumbnail
        const profileImage = new ThumbnailBuilder({
            media: { url: targetUser.displayAvatarURL({ forceStatic: false }) }
        });

        // Section: image + header (different title for admin lookup)
        const heading = new TextDisplayBuilder().setContent(`# lookup ${targetUser}`);
        const sectionOne = new SectionBuilder()
            .setThumbnailAccessory(profileImage)
            .addTextDisplayComponents(heading);

        // Info section
        const userInfoDisplay = new TextDisplayBuilder().setContent(`**user info**\n${userInfo}`);
        const serverInfoDisplay = new TextDisplayBuilder().setContent(`**server info**\n${serverInfo}`);
        // Bot specifics header and details
        const botSpecificsHeader = new TextDisplayBuilder().setContent(`**bot specifics**`);
        const botSpecificsDisplay = new TextDisplayBuilder().setContent(botSpecifics.trim());

        // Separator
        const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

        // Button row (outside container) - shows "lookup" instead of "you"
        const lookupButton = new ButtonBuilder({ 
            customId: `profile-lookup-${targetUser.id}`, 
            label: "lookup", 
            style: ButtonStyle.Secondary, 
            disabled: true 
        });
        const buttonRow = new ActionRowBuilder().addComponents(lookupButton);

        // Container
        const container = new ContainerBuilder()
            .addSectionComponents(sectionOne)
            .addSeparatorComponents(separator)
            .addTextDisplayComponents(userInfoDisplay, serverInfoDisplay, botSpecificsHeader, botSpecificsDisplay)
            .setAccentColor(accentColor);

        await interaction.reply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container, buttonRow]
        });

        console.log('[lookup] Successfully responded to context menu command');
        } catch (error) {
            console.error('[lookup] Error in context menu command:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while looking up the user.',
                    ephemeral: true
                });
            }
        }
    },
};

/**
 * Get comprehensive lookup system performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive lookup system performance data
 */
function getLookupSystemStats() {
    const cacheHitRate = lookupMetrics.cacheHits + lookupMetrics.cacheMisses > 0 ?
        (lookupMetrics.cacheHits / (lookupMetrics.cacheHits + lookupMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: lookupMetrics.cacheHits,
            cacheMisses: lookupMetrics.cacheMisses,
            databaseQueries: lookupMetrics.databaseQueries,
            averageQueryTime: `${lookupMetrics.averageQueryTime.toFixed(2)}ms`,
            lookupsProcessed: lookupMetrics.lookupsProcessed,
            expCalculationsProcessed: lookupMetrics.expCalculationsProcessed,
            levelCalculationsProcessed: lookupMetrics.levelCalculationsProcessed,
            parallelOperations: lookupMetrics.parallelOperations,
            partialFailures: lookupMetrics.partialFailures,
            lastOptimization: new Date(lookupMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildExpConfig: guildExpConfigCache.getStats(),
            memberData: memberDataCache.getStats(),
            expCalculation: expCalculationCache.getStats(),
            levelData: levelDataCache.getStats()
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 70 ? 'excellent' : cacheHitRate > 50 ? 'good' : 'needs optimization',
            parallelEfficiency: lookupMetrics.parallelOperations > 0 ?
                ((lookupMetrics.parallelOperations - lookupMetrics.partialFailures) / lookupMetrics.parallelOperations * 100).toFixed(2) + '%' : 'N/A'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    lookupMetrics.lastOptimization = Date.now();

    const stats = getLookupSystemStats();
    if (lookupMetrics.verboseLogging) {
        console.log(`[lookup] 📊 Performance Report:`);
        console.log(`[lookup]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[lookup]   Lookups Processed: ${stats.performance.lookupsProcessed}`);
        console.log(`[lookup]   Exp Calculations Processed: ${stats.performance.expCalculationsProcessed}`);
        console.log(`[lookup]   Level Calculations Processed: ${stats.performance.levelCalculationsProcessed}`);
        console.log(`[lookup]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[lookup]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[lookup]   Parallel Efficiency: ${stats.systemHealth.parallelEfficiency}`);
        console.log(`[lookup]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[lookup]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Invalidate guild experience configuration cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildExpConfigCache(guildId) {
    const cacheKey = `guild_exp_config_${guildId}`;
    guildExpConfigCache.delete(cacheKey);

    if (lookupMetrics.verboseLogging) {
        console.log(`[lookup] 🗑️ Invalidated guild exp config cache for ${guildId}`);
    }
}

/**
 * Invalidate member data cache
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 */
function invalidateMemberDataCache(userId, guildId) {
    const cacheKey = `member_${userId}_${guildId}`;
    memberDataCache.delete(cacheKey);

    if (lookupMetrics.verboseLogging) {
        console.log(`[lookup] 🗑️ Invalidated member data cache for ${userId}`);
    }
}

/**
 * Invalidate experience calculation cache for a guild
 * @param {string} guildId - Guild ID
 */
function invalidateExpCalculationCache(guildId) {
    // Clear all exp calculations for this guild
    const keys = Array.from(expCalculationCache.keys());
    const guildKeys = keys.filter(key => key.includes(guildId));

    guildKeys.forEach(key => {
        expCalculationCache.delete(key);
    });

    if (lookupMetrics.verboseLogging) {
        console.log(`[lookup] 🗑️ Invalidated ${guildKeys.length} exp calculation cache entries for ${guildId}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, lookupMetrics.performanceReportInterval);

// Export performance functions for external monitoring
module.exports.getLookupSystemStats = getLookupSystemStats;
module.exports.invalidateGuildExpConfigCache = invalidateGuildExpConfigCache;
module.exports.invalidateMemberDataCache = invalidateMemberDataCache;
module.exports.invalidateExpCalculationCache = invalidateExpCalculationCache;
