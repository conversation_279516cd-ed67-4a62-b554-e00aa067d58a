const { Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, AuditLogEvent } = require('discord.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');

// --- JOIN NOTIFICATION FEATURE HELPERS ---
const JOIN_NOTIFICATION_DB_KEY = 'global';
const DEFAULT_JOIN_MESSAGE = 'Thanks for adding me to **{serverName}**! 17 is a unique multi-purpose bot with a one-of-a-kind feature set. Use (click) the </17:1380491293465116703> command to browse and setup the various features there are to configure.';
const BOT_NAME = '17';
const BOT_LINK = 'https://itwascarryingusquietlytowardswhatwillbenotwhatwas.com/';

// Get join notification config from DB (optimized to use shared connection)
async function getJoinNotificationConfig() {
    let doc = await optimizedFindOne('join_notifications', { key: JOIN_NOTIFICATION_DB_KEY });
    if (!doc) {
        doc = { key: JOIN_NOTIFICATION_DB_KEY, enabled: true, message: DEFAULT_JOIN_MESSAGE };
        await optimizedInsertOne('join_notifications', doc);
    }
    return doc;
}

// Set join notification config in DB (optimized to use shared connection)
async function setJoinNotificationConfig({ enabled, message }) {
    await optimizedUpdateOne('join_notifications',
        { key: JOIN_NOTIFICATION_DB_KEY },
        { $set: { enabled, message } },
        { upsert: true }
    );
}

// Send join notification to the appropriate channel/user
async function sendJoinNotification(guild) {
    try {
        // Get config from DB
        const config = await getJoinNotificationConfig();
        if (!config.enabled) return;

        // Build the container with the actual guild name
        const container = buildJoinNotificationPreview({
            message: config.message,
            enabled: config.enabled,
            botUser: guild.client.user,
            guildName: guild.name
        });

        // Try to find the inviter first
        const auditLogs = await guild.fetchAuditLogs({ type: AuditLogEvent.BotAdd, limit: 1 }).catch(() => null);
        const inviter = auditLogs?.entries.first()?.executor;

        if (inviter) {
            // Try to DM the inviter
            try {
                const dmChannel = await inviter.createDM();
                await dmChannel.send({
                    flags: MessageFlags.IsComponentsV2,
                    components: [container]
                });
                return;
            } catch (e) {
                // Could not DM inviter, will try channel instead
            }
        }

        // If we couldn't DM the inviter, try to find the best channel
        const channels = guild.channels.cache
            .filter(c => c.type === 0 && c.permissionsFor(guild.members.me).has('SendMessages'))
            .sort((a, b) => {
                // Prioritize by name keywords
                function getPriority(name) {
                    const n = name.toLowerCase();
                    if (n.includes('bot')) return 1;
                    if (n.includes('spam')) return 2;
                    if (n.includes('welcome')) return 3;
                    if (n.includes('general')) return 4;
                    if (n.includes('announcements')) return 5;
                    if (n.includes('chat')) return 6;
                    return 7;
                }
                const pa = getPriority(a.name);
                const pb = getPriority(b.name);
                if (pa !== pb) return pa - pb;
                // Then sort by position
                return a.position - b.position;
            });

        const channel = channels.first();
        if (channel) {
            await channel.send({
                flags: MessageFlags.IsComponentsV2,
                components: [container]
            });
        }
    } catch (e) {
        console.error('[sendJoinNotification] Error:', e);
    }
}

// Build the join notification preview container
function buildJoinNotificationPreview({ message, botUser, guildName = null }) {
    // Replace placeholders for preview
    let processedMessage = message;
    if (guildName) {
        processedMessage = message.replace(/{serverName}/g, guildName);
    }

    const heading = new TextDisplayBuilder().setContent(`# [${BOT_NAME}](${BOT_LINK})`);
    const quote = new TextDisplayBuilder().setContent(`> ${processedMessage}`);
    const section = new SectionBuilder()
        .setThumbnailAccessory({ media: { url: botUser.displayAvatarURL({ forceStatic: false }) } })
        .addTextDisplayComponents(heading, quote);
    const container = new ContainerBuilder()
        .addSectionComponents(section)
        .setAccentColor(OPERATION_COLORS.ENTITY);
    return container;
}

// Build the join notification setup container (with back button, static quote, separator, and select menu inside)
function buildJoinNotificationSetupContainer({ enabled }) {
    // Section with back button (like buildServersContainer)
    const heading = new TextDisplayBuilder().setContent('# join notification');
    const backButton = new ButtonBuilder()
        .setCustomId('owner-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);
    // Static description of the feature
    const quote = new TextDisplayBuilder().setContent('> hi hello its me');
    const status = new TextDisplayBuilder().setContent(`**send message:** ${enabled ? 'enabled' : 'disabled'}`);
    // Separator
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    // Select menu for join notification config (inside container)
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('owner-joinnotification')
        .setPlaceholder('join notification')
        .addOptions([
            {
                label: enabled ? 'disable' : 'enable',
                value: 'toggle',
                description: enabled ? 'Currently enabled' : 'Currently disabled',
                default: false
            },
            {
                label: 'join message',
                value: 'edit',
                description: 'Edit the join notification message',
                default: false
            }
        ]);
    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    // Build container
    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(quote, status)
        .addSeparatorComponents(separator)
        .addActionRowComponents(selectRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    getJoinNotificationConfig,
    setJoinNotificationConfig,
    sendJoinNotification,
    buildJoinNotificationPreview,
    buildJoinNotificationSetupContainer
};
