const { Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const { buildSelectMenu } = require('./featuresMenu');
// Import modular owner components
const { buildServersContainer } = require('./owner-servers.js');
const { buildStatusContainer } = require('./owner-status.js');
const { buildGlobalLevelsContainer, handleGlobalLevelsAction } = require('./owner-global-levels.js');
const { handleGlobalLevelConfigSelect, handleGlobalLevelEditConfigSelect, handleGlobalLevelExpSelect, handleGlobalLevelEditExpSelect, handleGlobalLevelIconSelect, handleGlobalLevelEditIconSelect, handleGlobalPrestigeIconSelect, handleGlobalLevelEditPrestigeIconSelect, handleGlobalLevelXpBoosterSelect, handleGlobalLevelEditXpBoosterSelect, handleGlobalLevelDropBoosterSelect, handleGlobalLevelEditDropBoosterSelect, handleGlobalLevelStarsSelect, handleGlobalLevelEditStarsSelect, createGlobalLevel, updateGlobalLevel } = require('./owner-global-levels-handlers.js');
const { getJoinNotificationConfig, setJoinNotificationConfig, buildJoinNotificationPreview, buildJoinNotificationSetupContainer } = require('./owner-join-notification.js');
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');

async function buildOwnerContainer(client, user) {
    if (!user || user.id !== process.env.OWNER) {
        return new ContainerBuilder()
            .addTextDisplayComponents(
                new TextDisplayBuilder().setContent('who r u? no.')
            )
            .setAccentColor(OPERATION_COLORS.NEUTRAL);
    }
    const heading = new TextDisplayBuilder().setContent('# owner');
    const info = new TextDisplayBuilder().setContent('> lol hey you');

    // Use only slash command mentions (context commands can't be linked)
    const syncText = new TextDisplayBuilder().setContent(`**cmd(s):** ${client._slashCommandMentions || ''}`);

    // Fetch last reload timestamp from DB
    let reloadText = '**reload:** never';
    let syncStatusText = '**synced:** never';
    try {
        const reloadDoc = await optimizedFindOne("stats", { type: "last_reload" });
        if (reloadDoc && reloadDoc.timestamp) {
            reloadText = `**reload:** <t:${reloadDoc.timestamp}:R>`;
        }
        // Fetch last sync status
        const syncDoc = await optimizedFindOne("stats", { type: "last_sync" });
        if (syncDoc && syncDoc.timestamp) {
            syncStatusText = `**synced:** <t:${syncDoc.timestamp}:R>`;
        }
    } catch (e) {
        // fallback: do nothing
    }
    const reloadDisplay = new TextDisplayBuilder().setContent(reloadText);
    const syncStatusDisplay = new TextDisplayBuilder().setContent(syncStatusText);

    // Features select menu
    const featuresSelect = new StringSelectMenuBuilder()
        .setCustomId('owner-features')
        .setPlaceholder('features')
        .addOptions(
            { label: 'servers', value: 'servers', description: 'list of servers the bot is in' },
            { label: 'join notification', value: 'join_notification', description: 'setup join notification' },
            { label: 'status', value: 'status', description: 'update bot status and presence' },
            { label: 'items', value: 'items', description: 'manage bot-wide custom items' },
            { label: 'global levels', value: 'global_levels', description: 'manage global levels system', emoji: '🌟' },

            { label: 'clear data', value: 'clear_data', description: '⚠️ reset all guild data for fresh start' }
        );
    const featuresRow = new ActionRowBuilder().addComponents(featuresSelect);

    const syncButton = new ButtonBuilder()
        .setCustomId('owner-sync')
        .setLabel('sync')
        .setStyle(ButtonStyle.Secondary);

    const reloadButton = new ButtonBuilder()
        .setCustomId('owner-reload')
        .setLabel('reload')
        .setStyle(ButtonStyle.Secondary);

    const buttonRow = new ActionRowBuilder().addComponents(syncButton, reloadButton);

    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, info, syncText, syncStatusDisplay, reloadDisplay)
        .addSeparatorComponents(separator)
        .addActionRowComponents(featuresRow)
        .addActionRowComponents(buttonRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);

    return container;
}

// Old buildServersContainer function removed - now using modular owner-servers.js

// Join notification constants moved to owner-join-notification.js

// --- ITEM NOTIFICATION FEATURE HELPERS ---
const ITEM_NOTIFICATION_DB_KEY = 'global';
const DEFAULT_ITEM_DM_MESSAGE = '🎁 You found {items}! Server: {server} | From: {location}';

// Join notification functions moved to owner-join-notification.js

// Get item notification config from DB (optimized to use shared connection)
async function getItemNotificationConfig() {
    let doc = await optimizedFindOne('item_notifications', { key: ITEM_NOTIFICATION_DB_KEY });
    if (!doc) {
        doc = { key: ITEM_NOTIFICATION_DB_KEY, enabled: true, dmMessage: DEFAULT_ITEM_DM_MESSAGE };
        await optimizedInsertOne('item_notifications', doc);
    }
    return doc;
}

// Set item notification config in DB (optimized to use shared connection)
async function setItemNotificationConfig({ enabled, dmMessage }) {
    await optimizedUpdateOne('item_notifications',
        { key: ITEM_NOTIFICATION_DB_KEY },
        { $set: { enabled, dmMessage } },
        { upsert: true }
    );
}

// sendJoinNotification function moved to owner-join-notification.js

// buildJoinNotificationPreview function moved to owner-join-notification.js

// Build the item notification preview container
function buildItemNotificationPreview({ dmMessage, enabled, botUser, guildName = null }) {
    // Replace placeholders for preview
    let processedMessage = dmMessage;
    if (guildName) {
        processedMessage = processedMessage
            .replace(/{server}/g, guildName)
            .replace(/{items}/g, '🐟 **Rare Fish** (Rare)')
            .replace(/{location}/g, 'Text Chat');
    }

    // ContainerBuilder and TextDisplayBuilder already imported at top
    const heading = new TextDisplayBuilder().setContent('## item drop notification');
    const quote = new TextDisplayBuilder().setContent(`> ${processedMessage}`);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, quote)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

// buildJoinNotificationSetupContainer function moved to owner-join-notification.js

// Build the item notification setup container (with back button, static quote, separator, and select menu inside)
function buildItemNotificationSetupContainer({ dmMessage, enabled }, interaction) {
    // Section with back button (like buildServersContainer)
    const heading = new TextDisplayBuilder().setContent('# item notifications');
    const backButton = new ButtonBuilder()
        .setCustomId('owner-itemnotification-back')
        .setLabel('back')
        .setStyle(ButtonStyle.Secondary);
    const backSection = new SectionBuilder()
        .addTextDisplayComponents(heading)
        .setButtonAccessory(backButton);
    // Static description of the feature
    const quote = new TextDisplayBuilder().setContent('> configure item drop DM messages');
    const status = new TextDisplayBuilder().setContent(`**send DMs:** ${enabled ? 'enabled' : 'disabled'}`);
    // Separator
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    // Select menu for item notification config (inside container)
    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('owner-itemnotification')
        .setPlaceholder('item notifications')
        .addOptions([
            {
                label: enabled ? 'disable' : 'enable',
                value: 'toggle',
                description: enabled ? 'Currently enabled' : 'Currently disabled',
                default: false
            },
            {
                label: 'DM message',
                value: 'edit',
                description: 'Edit the item drop DM message template',
                default: false
            }
        ]);
    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    // Build container
    const container = new ContainerBuilder()
        .addSectionComponents(backSection)
        .addTextDisplayComponents(quote, status)
        .addSeparatorComponents(separator)
        .addActionRowComponents(selectRow)
        .setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    async execute(interaction) {
        // This command is not meant to be used as a slash command directly
        // Build basic owner container with status message instead of ephemeral reply
        const container = await buildOwnerContainer(interaction.client, interaction.user);
        const statusDisplay = new TextDisplayBuilder().setContent('**status:** Owner panel is only accessible via the select menu.');
        container.addTextDisplayComponents(statusDisplay);

        await interaction.reply({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [container]
        });
    },
    buildOwnerContainer,
    async select(interaction) {
        const selectedValue = interaction.values[0];

        // Handle global levels actions
        if (interaction.customId === 'global-levels-action') {
            await handleGlobalLevelsAction(interaction, selectedValue);
            return;
        }

        // Handle global level creation
        if (interaction.customId === 'global-level-config-select') {
            await handleGlobalLevelConfigSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-exp-select') {
            await handleGlobalLevelExpSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-icon-select') {
            await handleGlobalLevelIconSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-prestige-icon-select') {
            await handleGlobalPrestigeIconSelect(interaction, selectedValue);
            return;
        }

        if (interaction.customId === 'global-level-edit-config-select') {
            await handleGlobalLevelEditConfigSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-edit-exp-select') {
            await handleGlobalLevelEditExpSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-edit-icon-select') {
            await handleGlobalLevelEditIconSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-edit-prestige-icon-select') {
            await handleGlobalLevelEditPrestigeIconSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-xp-booster-select') {
            await handleGlobalLevelXpBoosterSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-edit-xp-booster-select') {
            await handleGlobalLevelEditXpBoosterSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-drop-booster-select') {
            await handleGlobalLevelDropBoosterSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-edit-drop-booster-select') {
            await handleGlobalLevelEditDropBoosterSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-stars-select') {
            await handleGlobalLevelStarsSelect(interaction, selectedValue);
            return;
        }
        if (interaction.customId === 'global-level-edit-stars-select') {
            await handleGlobalLevelEditStarsSelect(interaction, selectedValue);
            return;
        }

        if (selectedValue === 'servers') {
            const container = await buildServersContainer(interaction.client);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        } else if (selectedValue === 'join_notification') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container]
                });
                return;
            }
            const config = await getJoinNotificationConfig();
            const setupContainer = buildJoinNotificationSetupContainer(config, interaction);
            const preview = buildJoinNotificationPreview({
                message: config.message,
                enabled: config.enabled,
                botUser: interaction.client.user,
                guildName: interaction.guild.name
            });
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [setupContainer, preview]
            });
            return;

        } else if (selectedValue === 'status') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container]
                });
                return;
            }
            const container = await buildStatusContainer(interaction);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        } else if (selectedValue === 'items') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container]
                });
                return;
            }
            const items = require('./items.js');
            const container = await items.buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items (not guild-specific)
                page: 0,
                client: interaction.client,
                member: null, // No member context for bot-wide items
                showBackButton: true // Show back button in owner panel context
            });

            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: Array.isArray(container) ? container : [container]
            });
            return;

        } else if (selectedValue === 'global_levels') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container]
                });
                return;
            }
            const container = await buildGlobalLevelsContainer(interaction.client, interaction.user);
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;

        } else if (selectedValue === 'clear_data') {
            if (interaction.user.id !== process.env.OWNER) {
                // Rebuild main owner container with status message instead of ephemeral reply
                const container = await buildOwnerContainer(interaction.client, interaction.user);
                const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
                container.addTextDisplayComponents(statusDisplay);
                const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [selectMenu, container]
                });
                return;
            }
            const clearData = require('./clearData.js');
            await clearData.execute(interaction);
            return;
        }
        // Main owner container: show featuresMenu
        const container = await buildOwnerContainer(interaction.client, interaction.user);
        const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container]
        });
    },
    async buttons(interaction) {
        if (interaction.customId === 'owner-back') {
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
            return;

        } else if (interaction.customId === 'global-level-create-final') {
            await createGlobalLevel(interaction);
            return;
        } else if (interaction.customId === 'global-level-update-final') {
            await updateGlobalLevel(interaction);
            return;
        } else if (interaction.customId === 'global-levels-back') {
            const container = await buildGlobalLevelsContainer();
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [container]
            });
            return;
        }
        // ...other button handlers
    },
    async joinNotificationSelect(interaction) {
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
            return;
        }
        const config = await getJoinNotificationConfig();
        const selected = interaction.values[0];
        if (selected === 'toggle') {
            await setJoinNotificationConfig({ enabled: !config.enabled, message: config.message });
            const newConfig = await getJoinNotificationConfig();
            const setupContainer = buildJoinNotificationSetupContainer(newConfig, interaction);
            const preview = buildJoinNotificationPreview({
                message: newConfig.message,
                enabled: newConfig.enabled,
                botUser: interaction.client.user,
                guildName: interaction.guild.name
            });
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [setupContainer, preview]
            });
            return;
        } else if (selected === 'edit') {
            // Show modal to edit message
            const modal = new ModalBuilder()
                .setCustomId('owner-joinnotification-modal')
                .setTitle('Edit Join Notification Message')
                .addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('message')
                            .setLabel('Join Message')
                            .setPlaceholder('Available placeholders: {serverName}')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(config.message)
                    )
                );
            await interaction.showModal(modal);
            return;
        }
    },
    async joinNotificationModal(interaction) {
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
            return;
        }
        const message = interaction.fields.getTextInputValue('message');
        const config = await getJoinNotificationConfig();
        await setJoinNotificationConfig({ enabled: config.enabled, message });
        const newConfig = await getJoinNotificationConfig();
        const setupContainer = buildJoinNotificationSetupContainer(newConfig, interaction);
        const preview = buildJoinNotificationPreview({
            message: newConfig.message,
            enabled: newConfig.enabled,
            botUser: interaction.client.user,
            guildName: interaction.guild.name
        });
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [setupContainer, preview]
        });
    },
    async itemNotificationSelect(interaction) {
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
            return;
        }
        const config = await getItemNotificationConfig();
        const selected = interaction.values[0];
        if (selected === 'toggle') {
            await setItemNotificationConfig({ enabled: !config.enabled, dmMessage: config.dmMessage });

            // Return to items interface
            const items = require('./items.js');
            const container = await items.buildItemsContainer({
                isOwner: true,
                guildId: null, // Bot-wide items (not guild-specific)
                page: 0,
                client: interaction.client,
                member: null, // No member context for bot-wide items
                showBackButton: true // Show back button in owner panel context
            });
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: Array.isArray(container) ? container : [container]
            });
            return;
        } else if (selected === 'edit') {
            // Show modal to edit message
            const modal = new ModalBuilder()
                .setCustomId('owner-itemnotification-modal')
                .setTitle('Edit Item Drop DM Message')
                .addComponents(
                    new ActionRowBuilder().addComponents(
                        new TextInputBuilder()
                            .setCustomId('dmMessage')
                            .setLabel('Item Drop DM Message')
                            .setPlaceholder('Available placeholders: {items}, {server}, {location}')
                            .setStyle(TextInputStyle.Paragraph)
                            .setValue(config.dmMessage)
                    )
                );
            await interaction.showModal(modal);
            return;
        }
    },
    async itemNotificationModal(interaction) {
        if (interaction.user.id !== process.env.OWNER) {
            // Rebuild main owner container with status message instead of ephemeral reply
            const container = await buildOwnerContainer(interaction.client, interaction.user);
            const statusDisplay = new TextDisplayBuilder().setContent('**status:** who r u? no.');
            container.addTextDisplayComponents(statusDisplay);
            const selectMenu = buildSelectMenu(true, interaction.user.id, '1');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container]
            });
            return;
        }
        const dmMessage = interaction.fields.getTextInputValue('dmMessage');
        const config = await getItemNotificationConfig();
        await setItemNotificationConfig({ enabled: config.enabled, dmMessage });

        // Return to items interface
        const items = require('./items.js');
        const container = await items.buildItemsContainer({
            isOwner: true,
            guildId: null, // Bot-wide items (not guild-specific)
            page: 0,
            client: interaction.client,
            member: null, // No member context for bot-wide items
            showBackButton: true // Show back button in owner panel context
        });
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: Array.isArray(container) ? container : [container]
        });
    },
    async modalSubmit(interaction) {
        // Handle global level modals
        if (interaction.customId === 'global-level-name-modal') {
            const levelName = interaction.fields.getTextInputValue('level-name');
            const stateKey = `${interaction.user.id}_create`;
            const { tempGlobalLevelState } = require('./owner-global-levels.js');
            const state = tempGlobalLevelState.get(stateKey);

            if (state) {
                state.name = levelName;
                state.currentConfig = null; // Clear current config
                tempGlobalLevelState.set(stateKey, state);

                const { showGlobalLevelCreationInterface } = require('./owner-global-levels.js');
                await showGlobalLevelCreationInterface(interaction);
            } else {
                const { buildGlobalLevelsContainer } = require('./owner-global-levels.js');
                const container = await buildGlobalLevelsContainer();
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
            }
            return;
        }

        if (interaction.customId === 'global-level-edit-name-modal') {
            const levelName = interaction.fields.getTextInputValue('level-name');
            const stateKey = `${interaction.user.id}_edit`;
            const { tempGlobalLevelState } = require('./owner-global-levels.js');
            const state = tempGlobalLevelState.get(stateKey);

            if (state) {
                state.name = levelName;
                state.currentConfig = null; // Clear current config
                tempGlobalLevelState.set(stateKey, state);

                const { showGlobalLevelEditInterface } = require('./owner-global-levels.js');
                await showGlobalLevelEditInterface(interaction);
            } else {
                const { buildGlobalLevelsContainer } = require('./owner-global-levels.js');
                const container = await buildGlobalLevelsContainer();
                await interaction.update({
                    flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                    components: [container]
                });
            }
            return;
        }

        // Handle other modals...
        if (interaction.customId === 'join-notification-modal') {
            const message = interaction.fields.getTextInputValue('joinMessage');
            const config = await getJoinNotificationConfig();
            await setJoinNotificationConfig({ enabled: config.enabled, message });

            // Return to join notification interface
            const setupContainer = buildJoinNotificationSetupContainer(config, interaction);
            const preview = buildJoinNotificationPreview({
                message: message,
                enabled: config.enabled,
                botUser: interaction.client.user,
                guildName: interaction.guild.name
            });
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [setupContainer, preview]
            });
            return;
        }

        if (interaction.customId === 'item-notification-modal') {
            await this.itemNotificationModal(interaction);
            return;
        }
    },
    buildItemNotificationSetupContainer,
    buildItemNotificationPreview,
    getItemNotificationConfig,
    setItemNotificationConfig
};