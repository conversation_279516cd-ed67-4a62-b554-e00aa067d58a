const { Con<PERSON>er<PERSON><PERSON><PERSON>, SectionBuilder, TextDisplayBuilder, ButtonBuilder, ButtonStyle, SeparatorBuilder, SeparatorSpacingSize, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags, RoleSelectMenuBuilder } = require('discord.js');
const { mongoClient } = require("../../mongo/client.js");
const { optimizedFindOne, optimizedInsertOne, optimizedUpdateOne } = require("../../utils/database-optimizer.js");
const { defaults } = require("../../utils/default_db_structures.js");
const config = require("../../config.js");
const { buildSelectMenu } = require('./featuresMenu');
const { sendFeatureToggleLog } = require("../../utils/sendLog.js");
const { getStickyDemoData } = require("../../utils/demoData.js");
const { OPERATION_COLORS } = require('../../utils/colors.js');

// This function is now replaced by the unified demo data system

function buildStickyContainer({ sticky, guild, roles, enabled = true, hasPermission = true, member = null, commandChannel = null, statusMessage = null }) {


    // Use demo data if user doesn't have permission
    if (!hasPermission && guild && member) {
        sticky = getStickyDemoData(guild, member, commandChannel);
        enabled = true; // Show demo as enabled
    }
    const heading = new TextDisplayBuilder().setContent('# sticky');
    const description = new TextDisplayBuilder().setContent('> give back roles and nicknames when a member rejoins');
    const status = new TextDisplayBuilder().setContent(`**nicknames:** ${sticky.nick ? 'sticky' : 'not sticky'}\n**sticky role(s):** ${sticky.roles.length ? sticky.roles.map(id => `<@&${id}>`).join(' ') : '`none`'}`);

    const nickSelect = new StringSelectMenuBuilder()
        .setCustomId('sticky-nick-select')
        .setPlaceholder('nickname')
        .setDisabled(!enabled || !hasPermission)
        .addOptions([
            {
                label: sticky.nick ? 'disable' : 'enable',
                value: sticky.nick ? 'disable' : 'enable',
                description: sticky.nick ? 'Currently enabled' : 'Currently disabled',
                default: false
            }
        ]);
    const nickRow = new ActionRowBuilder().addComponents(nickSelect);

    let roleRow = null;
    if (roles && roles.size > 0) {
        const validRoleIds = sticky.roles.filter(id => roles.has(id));
        const roleSelect = new RoleSelectMenuBuilder()
            .setCustomId('sticky-role-select')
            .setPlaceholder('role(s)')
            .setMinValues(0)
            .setMaxValues(roles.size)
            .setDefaultRoles(...validRoleIds.map(String))
            .setDisabled(!enabled || !hasPermission);

        roleRow = new ActionRowBuilder().addComponents(roleSelect);
    }

    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);

    const container = new ContainerBuilder()
        .addTextDisplayComponents(heading, description, status)
        .addSeparatorComponents(separator)
        .addActionRowComponents(nickRow);
    if (roleRow) container.addActionRowComponents(roleRow);

    // Check for roles higher than bot and create persistent status message
    let persistentStatusMessage = statusMessage; // Keep any temporary status message
    if (guild && sticky.roles.length > 0 && hasPermission) {
        const { getCachedRoleValidation } = require('../../utils/stickyCache.js');
        const validation = getCachedRoleValidation(guild.id, guild);

        const invalidRoles = [];
        sticky.roles.forEach(roleId => {
            const roleData = validation.validRoles.get(roleId);
            if (roleData && !roleData.valid) {
                invalidRoles.push(`<@&${roleId}>`);
            }
        });

        if (invalidRoles.length > 0) {
            const verb = invalidRoles.length === 1 ? 'is' : 'are';
            const botRole = guild.members.me?.roles?.highest;
            const botRoleMention = botRole ? `<@&${botRole.id}>` : 'bot role';
            persistentStatusMessage = `${invalidRoles.join(' ')} ${verb} higher than ${botRoleMention} and won't be sticky upon rejoin`;
        }
    }

    // Add status message at the bottom if present
    if (persistentStatusMessage) {
        const statusDisplay = new TextDisplayBuilder().setContent(`**status:** ${persistentStatusMessage}`);
        container.addTextDisplayComponents(statusDisplay);
    }

    container.setAccentColor(OPERATION_COLORS.NEUTRAL);
    return container;
}

module.exports = {
    buildStickyContainer,
    // data: new SlashCommandBuilder()
    //     .setName("sticky")
    //     // .setDMPermission(false)
    //     .setDescription("sticky stuff")
    //     .setDefaultMemberPermissions(config.permissions.sticky),
    async execute(interaction) {
        // Check permissions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'sticky');



        // Use cached sticky configuration for better performance
        const { getCachedGuildStickyConfig } = require('../../utils/stickyCache.js');
        let sticky = await getCachedGuildStickyConfig(interaction.guild.id);

        // Ensure the configuration exists in database if it's using defaults
        if (!sticky.enabled && sticky.roles.length === 0 && !sticky.nick) {
            const guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });

            if (!guildData) {
                await optimizedInsertOne("guilds", defaults.guild(interaction.guild.id));
                sticky = { enabled: true, roles: [], nick: false };
            }
        }
        // Ensure sticky configuration has all required fields (fix for partial database objects)
        if (typeof sticky.enabled === 'undefined') sticky.enabled = true;
        if (typeof sticky.nick === 'undefined') sticky.nick = false;
        if (!Array.isArray(sticky.roles)) sticky.roles = [];



        const roles = interaction.guild.roles.cache;
        const container = buildStickyContainer({
            sticky,
            guild: interaction.guild,
            roles,
            enabled: sticky.enabled,
            hasPermission,
            member: interaction.member,
            commandChannel: interaction.channel
        });
        const disableButton = new ButtonBuilder()
            .setCustomId(sticky.enabled ? 'sticky-disable' : 'sticky-enable')
            .setLabel(sticky.enabled ? 'disable' : 'enable')
            .setStyle(sticky.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            .setDisabled(!hasPermission);
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
        await interaction.update({
            flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
            components: [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)]
        });
    },
    async select(interaction) {
        try {
            // Check permissions
            const hasPermission = global.hasFeaturePermission(interaction.member, 'sticky');

            if (!hasPermission) {
            // Show demo mode if no permission
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky: {}, // Will be replaced by demo data
                guild: interaction.guild,
                roles,
                enabled: true,
                hasPermission: false,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('sticky-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)]
            });
            return;
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        let sticky = guildData?.sticky || { roles: [], nick: false, enabled: true };

        // Ensure sticky configuration has all required fields (fix for partial database objects)
        if (typeof sticky.enabled === 'undefined') sticky.enabled = true;
        if (typeof sticky.nick === 'undefined') sticky.nick = false;
        if (!Array.isArray(sticky.roles)) sticky.roles = [];

        // Check if sticky is enabled for this guild
        if (!sticky.enabled) {
            // Feature is disabled, don't process the selection
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky,
                guild: interaction.guild,
                roles,
                enabled: false,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const enableButton = new ButtonBuilder()
                .setCustomId('sticky-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        }



        if (interaction.customId === 'sticky-nick-select') {
            const value = interaction.values[0];
            sticky.nick = value === 'enable';
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.nick': sticky.nick } });

            // Invalidate cache after update
            const { invalidateGuildStickyConfig } = require('../../utils/stickyCache.js');
            invalidateGuildStickyConfig(interaction.guild.id);
        } else if (interaction.customId === 'sticky-role-select') {
            const roles = interaction.values;
            // Save all selected roles (no filtering)
            sticky.roles = roles;

            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.roles': sticky.roles } });

            // Invalidate cache after update
            const { invalidateGuildStickyConfig } = require('../../utils/stickyCache.js');
            invalidateGuildStickyConfig(interaction.guild.id);

            // Note: New sticky roles will be captured by lazy sync as users become active
        }

        // Rebuild UI
        const rolesCache = interaction.guild.roles.cache;
        const validRoleIds = sticky.roles.filter(id => rolesCache.has(id));

        const container = buildStickyContainer({
            sticky,
            guild: interaction.guild,
            roles: rolesCache,
            enabled: sticky.enabled,
            member: interaction.member,
            commandChannel: interaction.channel
        });
        const toggleButton = new ButtonBuilder()
            .setCustomId(sticky.enabled ? 'sticky-disable' : 'sticky-enable')
            .setLabel(sticky.enabled ? 'disable' : 'enable')
            .setStyle(sticky.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
        const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
        try {
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)]
            });
        } catch (err) {
            console.error('[sticky.select] Error updating interaction:', err);
            if (!interaction.replied && !interaction.deferred) {
                try {
                    // Try to rebuild basic sticky container with error status message
                    const { mongoClient } = require('../../mongo/client.js');
                    let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
                    if (!guildData) guildData = { sticky: { enabled: true, roles: [] } };
                    if (!guildData.sticky) guildData.sticky = { enabled: true, roles: [] };

                    const container = buildStickyContainer({
                        sticky: guildData.sticky,
                        guild: interaction.guild,
                        roles: interaction.guild.roles.cache,
                        enabled: guildData.sticky.enabled,
                        member: interaction.member,
                        commandChannel: interaction.channel,
                        statusMessage: 'there was an error updating the sticky UI.'
                    });
                    const toggleButton = new ButtonBuilder()
                        .setCustomId(guildData.sticky.enabled ? 'sticky-disable' : 'sticky-enable')
                        .setLabel(guildData.sticky.enabled ? 'disable' : 'enable')
                        .setStyle(guildData.sticky.enabled ? ButtonStyle.Danger : ButtonStyle.Success);
                    const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');

                    await interaction.update({
                        flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                        components: [selectMenu, container, new ActionRowBuilder().addComponents(toggleButton)]
                    });
                } catch (fallbackErr) {
                    console.error('[sticky.select] Fallback error:', fallbackErr);
                    await interaction.reply({ content: 'there was an error updating the sticky UI.', ephemeral: true });
                }
            }
        }
        } catch (mainError) {
            console.error('[sticky.select] MAIN ERROR:', mainError);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({ content: 'An error occurred processing your selection.', ephemeral: true });
            }
        }
    },
    async buttons(interaction) {
        // Check permissions for all button actions
        const hasPermission = global.hasFeaturePermission(interaction.member, 'sticky');
        if (!hasPermission) {
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky: {}, // Will be replaced by demo data
                guild: interaction.guild,
                roles,
                enabled: true,
                hasPermission: false,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('sticky-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger)
                .setDisabled(true);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)]
            });
            return;
        }

        let guildData = await optimizedFindOne("guilds", { id: interaction.guild.id });
        let sticky = guildData.sticky || { roles: [], nick: false, enabled: true };
        
        if (interaction.customId === 'sticky-disable') {
            sticky.enabled = false;
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.enabled': false } });

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Sticky',
                null,
                false,
                interaction.user.id,
                interaction.client
            );
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky,
                guild: interaction.guild,
                roles,
                enabled: sticky.enabled,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const enableButton = new ButtonBuilder()
                .setCustomId('sticky-enable')
                .setLabel('enable')
                .setStyle(ButtonStyle.Success);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(enableButton)]
            });
            return;
        } else if (interaction.customId === 'sticky-enable') {
            sticky.enabled = true;
            await optimizedUpdateOne("guilds", { id: interaction.guild.id }, { $set: { 'sticky.enabled': true } });

            // Send feature toggle log
            await sendFeatureToggleLog(
                interaction.guild.id,
                'Sticky',
                null,
                true,
                interaction.user.id,
                interaction.client
            );
            const roles = interaction.guild.roles.cache;
            const container = buildStickyContainer({
                sticky,
                guild: interaction.guild,
                roles,
                enabled: sticky.enabled,
                member: interaction.member,
                commandChannel: interaction.channel
            });
            const disableButton = new ButtonBuilder()
                .setCustomId('sticky-disable')
                .setLabel('disable')
                .setStyle(ButtonStyle.Danger);
            const selectMenu = buildSelectMenu(true, interaction.user.id, 'sticky');
            await interaction.update({
                flags: MessageFlags.IsComponentsV2 | MessageFlags.Ephemeral,
                components: [selectMenu, container, new ActionRowBuilder().addComponents(disableButton)]
            });
            return;
        }
    },


};