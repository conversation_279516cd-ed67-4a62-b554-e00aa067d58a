/**
 * Item System Caching Utility (Enterprise-Grade Performance Optimized)
 * Provides caching for frequently accessed item data to improve performance
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

const { optimizedFindOne, optimizedFind, optimizedAggregate } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const itemCacheMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    itemsServed: 0,
    inventoriesServed: 0,
    leaderboardsServed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const itemCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for item definitions
const inventoryCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for inventory data
const leaderboardCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for leaderboards
const itemStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for item statistics

// Register caches for global cleanup
registerCache(itemCache);
registerCache(inventoryCache);
registerCache(leaderboardCache);
registerCache(itemStatsCache);

/**
 * Get cached item by ID (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} itemId - Item ID
 * @returns {Promise<Object|null>} Item data or null if not found
 */
async function getCachedItem(itemId) {
    const startTime = Date.now();

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = itemCache.get(itemId);
    if (cached) {
        itemCacheMetrics.cacheHits++;
        itemCacheMetrics.itemsServed++;
        if (itemCacheMetrics.verboseLogging) {
            console.log(`[itemCache] ⚡ Item cache hit for ${itemId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    itemCacheMetrics.cacheMisses++;
    itemCacheMetrics.databaseQueries++;

    try {
        const item = await optimizedFindOne('custom_items', { id: itemId });

        if (item) {
            // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
            itemCache.set(itemId, item);
        }

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        itemCacheMetrics.averageQueryTime =
            (itemCacheMetrics.averageQueryTime * (itemCacheMetrics.databaseQueries - 1) + duration) /
            itemCacheMetrics.databaseQueries;

        if (itemCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[itemCache] ✅ Item fetched for ${itemId}: ${duration}ms - cached for future access`);
        }

        itemCacheMetrics.itemsServed++;
        return item;
    } catch (error) {
        console.error(`[itemCache] ❌ Error getting item ${itemId}:`, error);
        return null;
    }
}

/**
 * Get cached user inventory (guild-specific) (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Array>} Inventory items
 */
async function getCachedUserInventory(userId, guildId) {
    const startTime = Date.now();
    const cacheKey = `${userId}_${guildId}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = inventoryCache.get(cacheKey);
    if (cached) {
        itemCacheMetrics.cacheHits++;
        itemCacheMetrics.inventoriesServed++;
        if (itemCacheMetrics.verboseLogging) {
            console.log(`[itemCache] ⚡ Inventory cache hit for ${userId} in guild ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    itemCacheMetrics.cacheMisses++;
    itemCacheMetrics.databaseQueries++;

    try {
        const items = await optimizedFind('user_inventory', {
            userId: userId,
            guildId: guildId
        }, { sort: { droppedAt: -1 } });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        inventoryCache.set(cacheKey, items);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        itemCacheMetrics.averageQueryTime =
            (itemCacheMetrics.averageQueryTime * (itemCacheMetrics.databaseQueries - 1) + duration) /
            itemCacheMetrics.databaseQueries;

        if (itemCacheMetrics.verboseLogging || duration > 200) {
            console.log(`[itemCache] ✅ Inventory fetched for ${userId} in guild ${guildId}: ${items.length} items (${duration}ms) - cached for future access`);
        }

        itemCacheMetrics.inventoriesServed++;
        return items;
    } catch (error) {
        console.error(`[itemCache] ❌ Error getting inventory for ${userId}:`, error);
        return [];
    }
}

/**
 * Get cached user global inventory (across all servers) (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Inventory items
 */
async function getCachedUserGlobalInventory(userId) {
    const startTime = Date.now();
    const cacheKey = `${userId}_global`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = inventoryCache.get(cacheKey);
    if (cached) {
        itemCacheMetrics.cacheHits++;
        itemCacheMetrics.inventoriesServed++;
        if (itemCacheMetrics.verboseLogging) {
            console.log(`[itemCache] ⚡ Global inventory cache hit for ${userId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    itemCacheMetrics.cacheMisses++;
    itemCacheMetrics.databaseQueries++;

    try {
        const items = await optimizedFind('user_inventory', {
            userId: userId
        }, { sort: { droppedAt: -1 } });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        inventoryCache.set(cacheKey, items);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        itemCacheMetrics.averageQueryTime =
            (itemCacheMetrics.averageQueryTime * (itemCacheMetrics.databaseQueries - 1) + duration) /
            itemCacheMetrics.databaseQueries;

        if (itemCacheMetrics.verboseLogging || duration > 300) {
            console.log(`[itemCache] ✅ Global inventory fetched for ${userId}: ${items.length} items (${duration}ms) - cached for future access`);
        }

        itemCacheMetrics.inventoriesServed++;
        return items;
    } catch (error) {
        console.error(`[itemCache] ❌ Error getting global inventory for ${userId}:`, error);
        return [];
    }
}

/**
 * Get cached item leaderboard (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} itemId - Item ID
 * @param {string} guildId - Guild ID (null for global)
 * @param {string} parameter - Parameter to rank by
 * @param {number} limit - Number of results
 * @returns {Promise<Array>} Leaderboard data
 */
async function getCachedItemLeaderboard(itemId, guildId, parameter, limit = 10) {
    const startTime = Date.now();
    const cacheKey = `${itemId}_${guildId || 'global'}_${parameter}_${limit}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = leaderboardCache.get(cacheKey);
    if (cached) {
        itemCacheMetrics.cacheHits++;
        itemCacheMetrics.leaderboardsServed++;
        if (itemCacheMetrics.verboseLogging) {
            console.log(`[itemCache] ⚡ Leaderboard cache hit for ${itemId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    itemCacheMetrics.cacheMisses++;
    return null; // Return null if not found/expired
}

/**
 * Cache item leaderboard data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} itemId - Item ID
 * @param {string} guildId - Guild ID (null for global)
 * @param {string} parameter - Parameter to rank by
 * @param {number} limit - Number of results
 * @param {Array} data - Leaderboard data
 */
function setCachedItemLeaderboard(itemId, guildId, parameter, limit, data) {
    const cacheKey = `${itemId}_${guildId || 'global'}_${parameter}_${limit}`;

    // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
    leaderboardCache.set(cacheKey, data);

    if (itemCacheMetrics.verboseLogging) {
        console.log(`[itemCache] ✅ Leaderboard cached for ${itemId}: ${data.length} entries`);
    }
}

/**
 * Invalidate cache for a specific item
 * @param {string} itemId - Item ID
 */
function invalidateItemCache(itemId) {
    // OPTIMIZED: Use LRU cache delete methods
    itemCache.delete(itemId);

    // Remove related leaderboard caches - need to iterate through keys
    const leaderboardKeys = leaderboardCache.getKeysByAccessTime();
    for (const key of leaderboardKeys) {
        if (key.startsWith(`${itemId}_`)) {
            leaderboardCache.delete(key);
        }
    }
}

/**
 * Invalidate inventory cache for a user
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional - if not provided, invalidates global cache)
 */
function invalidateInventoryCache(userId, guildId = null) {
    if (guildId) {
        // Invalidate guild-specific cache
        const cacheKey = `${userId}_${guildId}`;
        inventoryCache.delete(cacheKey);
    } else {
        // Invalidate all caches for this user (both guild-specific and global)
        const inventoryKeys = inventoryCache.getKeysByAccessTime();
        for (const key of inventoryKeys) {
            if (key.startsWith(`${userId}_`)) {
                inventoryCache.delete(key);
            }
        }
    }

    // Always invalidate global cache when any inventory changes
    const globalCacheKey = `${userId}_global`;
    inventoryCache.delete(globalCacheKey);
}

/**
 * Invalidate all leaderboard caches for an item
 * @param {string} itemId - Item ID
 */
function invalidateLeaderboardCache(itemId) {
    const leaderboardKeys = leaderboardCache.getKeysByAccessTime();
    for (const key of leaderboardKeys) {
        if (key.startsWith(`${itemId}_`)) {
            leaderboardCache.delete(key);
        }
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = itemCacheMetrics.cacheHits + itemCacheMetrics.cacheMisses > 0 ?
        (itemCacheMetrics.cacheHits / (itemCacheMetrics.cacheHits + itemCacheMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: itemCacheMetrics.cacheHits,
            cacheMisses: itemCacheMetrics.cacheMisses,
            databaseQueries: itemCacheMetrics.databaseQueries,
            averageQueryTime: `${itemCacheMetrics.averageQueryTime.toFixed(2)}ms`,
            itemsServed: itemCacheMetrics.itemsServed,
            inventoriesServed: itemCacheMetrics.inventoriesServed,
            leaderboardsServed: itemCacheMetrics.leaderboardsServed,
            parallelOperations: itemCacheMetrics.parallelOperations,
            partialFailures: itemCacheMetrics.partialFailures,
            lastOptimization: new Date(itemCacheMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            items: itemCache.getStats(),
            inventory: inventoryCache.getStats(),
            leaderboards: leaderboardCache.getStats(),
            itemStats: itemStatsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            items: itemCache.getStats().memoryUsage,
            inventory: inventoryCache.getStats().memoryUsage,
            leaderboards: leaderboardCache.getStats().memoryUsage,
            itemStats: itemStatsCache.getStats().memoryUsage,
            total: itemCache.getStats().memoryUsage +
                   inventoryCache.getStats().memoryUsage +
                   leaderboardCache.getStats().memoryUsage +
                   itemStatsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, itemCacheMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 200) {
        recommendations.push('Item operations are slow - investigate database performance');
    }

    if (metrics.partialFailures > metrics.parallelOperations * 0.1) {
        recommendations.push('High partial failure rate - investigate system reliability');
    }

    if (metrics.parallelOperations < metrics.databaseQueries * 0.3) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (metrics.inventoriesServed > metrics.itemsServed * 2) {
        recommendations.push('High inventory to item ratio - consider inventory-specific optimizations');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    itemCacheMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[itemCache] 📊 Performance Report:`);
    console.log(`[itemCache]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[itemCache]   Items Served: ${stats.performance.itemsServed}`);
    console.log(`[itemCache]   Inventories Served: ${stats.performance.inventoriesServed}`);
    console.log(`[itemCache]   Leaderboards Served: ${stats.performance.leaderboardsServed}`);
    console.log(`[itemCache]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[itemCache]   Partial Failures: ${stats.performance.partialFailures}`);
    console.log(`[itemCache]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[itemCache]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[itemCache]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[itemCache] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all item caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllItemCaches() {
    itemCache.clear();
    inventoryCache.clear();
    leaderboardCache.clear();
    itemStatsCache.clear();

    console.log('[itemCache] 🗑️ Cleared all item caches');
}

/**
 * Batch invalidate multiple user inventories (Enterprise-Grade Optimization)
 * OPTIMIZED: Efficient batch cache invalidation for bulk operations
 * @param {Array} userIds - Array of user IDs to invalidate
 * @param {string} guildId - Guild ID (optional)
 */
function batchInvalidateInventories(userIds, guildId = null) {
    let totalInvalidated = 0;

    userIds.forEach(userId => {
        if (guildId) {
            const cacheKey = `${userId}_${guildId}`;
            if (inventoryCache.delete(cacheKey)) totalInvalidated++;
        } else {
            // Invalidate all caches for this user
            const inventoryKeys = Array.from(inventoryCache.keys()).filter(key => key.startsWith(`${userId}_`));
            inventoryKeys.forEach(key => {
                if (inventoryCache.delete(key)) totalInvalidated++;
            });
        }

        // Always invalidate global cache
        const globalCacheKey = `${userId}_global`;
        if (inventoryCache.delete(globalCacheKey)) totalInvalidated++;
    });

    if (itemCacheMetrics.verboseLogging) {
        console.log(`[itemCache] 🗑️ Batch invalidated ${totalInvalidated} inventory cache entries for ${userIds.length} users`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, itemCacheMetrics.performanceReportInterval);

/**
 * Preload frequently accessed items into cache (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced parallel processing with comprehensive error handling
 * @param {Array} itemIds - Array of item IDs to preload
 */
async function preloadItems(itemIds) {
    const startTime = Date.now();

    try {
        itemCacheMetrics.databaseQueries++;
        itemCacheMetrics.parallelOperations++;

        const items = await optimizedFind('custom_items', {
            id: { $in: itemIds }
        });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        items.forEach(item => {
            itemCache.set(item.id, item);
        });

        const duration = Date.now() - startTime;
        itemCacheMetrics.averageQueryTime =
            (itemCacheMetrics.averageQueryTime * (itemCacheMetrics.databaseQueries - 1) + duration) /
            itemCacheMetrics.databaseQueries;

        if (itemCacheMetrics.verboseLogging || duration > 200) {
            console.log(`[itemCache] ✅ Preloaded ${items.length} items into cache in ${duration}ms`);
        }
    } catch (error) {
        console.error('[itemCache] ❌ Error preloading items:', error);
    }
}

/**
 * Batch get multiple items with parallel processing (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing with Promise.allSettled for comprehensive error recovery
 * @param {Array} itemIds - Array of item IDs to fetch
 * @returns {Promise<Array>} Array of items (null for failed fetches)
 */
async function batchGetItems(itemIds) {
    const startTime = Date.now();
    itemCacheMetrics.parallelOperations++;

    try {
        // OPTIMIZED: Use Promise.allSettled for comprehensive error handling
        const itemResults = await Promise.allSettled(
            itemIds.map(itemId => getCachedItem(itemId))
        );

        const items = itemResults.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            } else {
                itemCacheMetrics.partialFailures++;
                if (itemCacheMetrics.verboseLogging) {
                    console.warn(`[itemCache] ⚠️  Failed to fetch item ${itemIds[index]}: ${result.reason}`);
                }
                return null;
            }
        });

        const duration = Date.now() - startTime;
        const successCount = items.filter(item => item !== null).length;
        const failureCount = items.length - successCount;

        if (itemCacheMetrics.verboseLogging || failureCount > 0) {
            console.log(`[itemCache] ✅ Batch fetched ${successCount}/${items.length} items (${failureCount} failures) in ${duration}ms`);
        }

        return items;
    } catch (error) {
        console.error('[itemCache] ❌ Error in batch get items:', error);
        return itemIds.map(() => null);
    }
}

/**
 * Get item statistics with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Aggregation pipeline with intelligent caching
 * @param {string} itemId - Item ID
 * @param {string} guildId - Guild ID (null for global)
 * @returns {Promise<Object>} Item statistics
 */
async function getCachedItemStats(itemId, guildId = null) {
    const startTime = Date.now();
    const cacheKey = `stats_${itemId}_${guildId || 'global'}`;

    // Check cache first
    const cached = itemStatsCache.get(cacheKey);
    if (cached) {
        itemCacheMetrics.cacheHits++;
        if (itemCacheMetrics.verboseLogging) {
            console.log(`[itemCache] ⚡ Item stats cache hit for ${itemId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    itemCacheMetrics.cacheMisses++;
    itemCacheMetrics.databaseQueries++;

    try {
        // OPTIMIZED: Use aggregation pipeline for efficient statistics
        const pipeline = [
            {
                $match: {
                    itemId: itemId,
                    ...(guildId && { guildId: guildId })
                }
            },
            {
                $group: {
                    _id: null,
                    totalDropped: { $sum: 1 },
                    uniqueOwners: { $addToSet: "$userId" },
                    avgParameters: {
                        $avg: {
                            $add: [
                                { $ifNull: ["$parameters.attack", 0] },
                                { $ifNull: ["$parameters.defense", 0] },
                                { $ifNull: ["$parameters.speed", 0] },
                                { $ifNull: ["$parameters.health", 0] }
                            ]
                        }
                    },
                    latestDrop: { $max: "$droppedAt" }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalDropped: 1,
                    uniqueOwners: { $size: "$uniqueOwners" },
                    avgParameters: { $round: ["$avgParameters", 2] },
                    latestDrop: 1
                }
            }
        ];

        const statsResult = await optimizedAggregate('user_inventory', pipeline);
        const stats = statsResult.length > 0 ? statsResult[0] : {
            totalDropped: 0,
            uniqueOwners: 0,
            avgParameters: 0,
            latestDrop: null
        };

        // Cache the result
        itemStatsCache.set(cacheKey, stats);

        const duration = Date.now() - startTime;
        if (itemCacheMetrics.verboseLogging || duration > 150) {
            console.log(`[itemCache] ✅ Item stats calculated for ${itemId}: ${duration}ms - cached for future access`);
        }

        return stats;
    } catch (error) {
        console.error(`[itemCache] ❌ Error getting item stats for ${itemId}:`, error);
        return {
            totalDropped: 0,
            uniqueOwners: 0,
            avgParameters: 0,
            latestDrop: null
        };
    }
}

/**
 * Batch invalidate caches after item operations
 * @param {Array} operations - Array of cache operations
 */
function batchInvalidateCache(operations) {
    operations.forEach(op => {
        switch (op.type) {
            case 'item':
                invalidateItemCache(op.itemId);
                break;
            case 'inventory':
                invalidateInventoryCache(op.userId, op.guildId);
                break;
            case 'leaderboard':
                invalidateLeaderboardCache(op.itemId);
                break;
        }
    });
}

module.exports = {
    // Core functions
    getCachedItem,
    getCachedUserInventory,
    getCachedUserGlobalInventory,
    getCachedItemLeaderboard,
    setCachedItemLeaderboard,
    invalidateItemCache,
    invalidateInventoryCache,
    invalidateLeaderboardCache,
    preloadItems,
    batchInvalidateCache,

    // Enhanced optimization functions
    batchGetItems,
    getCachedItemStats,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllItemCaches,
    batchInvalidateInventories,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...itemCacheMetrics })
};
