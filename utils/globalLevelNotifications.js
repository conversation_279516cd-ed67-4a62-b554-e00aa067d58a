/**
 * Global Level Notifications System
 * Handles notifications for global level-ups in notification center and DMs
 */

const { mongoClient } = require('../mongo/client.js');
const { optimizedInsertOne, optimizedFind, optimizedUpdateOne, optimizedFindOne } = require('./database-optimizer.js');
const { ContainerBuilder, TextDisplayBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, SectionBuilder, SeparatorBuilder, SeparatorSpacingSize, ThumbnailBuilder, MessageFlags } = require('discord.js');
const { OPERATION_COLORS } = require('./colors.js');

/**
 * Build level-up container for notifications
 * @param {Object} levelData - Level up data
 * @param {string} userAvatar - User avatar URL
 * @param {Object} globalLevelData - Global level configuration data
 * @returns {ContainerBuilder} Level-up container
 */
function buildLevelUpContainer(levelData, userAvatar, globalLevelData) {
    // Build rewards text
    let rewardsText = '**rewards:**\n';
    if (levelData.levelRewards && levelData.levelRewards.items && levelData.levelRewards.items.length > 0) {
        for (const item of levelData.levelRewards.items) {
            rewardsText += `\\- 1 item (${item.emote} ${item.name})\n`;
        }
    }
    if (levelData.levelRewards && levelData.levelRewards.xpBooster) {
        rewardsText += `\\- ${levelData.levelRewards.xpBooster}x exp boost\n`;
    }
    if (levelData.levelRewards && levelData.levelRewards.dropBooster) {
        rewardsText += `\\- ${levelData.levelRewards.dropBooster}x drop boost\n`;
    }

    // Get level name and emoji from global level data
    const levelName = globalLevelData?.name || 'Unknown';
    const levelEmoji = globalLevelData?.icon || '🌟';

    const profileThumbnail = new ThumbnailBuilder({
        media: { url: userAvatar }
    });

    const thumbnailSection = new SectionBuilder()
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent('## level up'),
            new TextDisplayBuilder().setContent(`> you reached **global level ${levelData.newLevel}**`)
        )
        .setThumbnailAccessory(profileThumbnail);

    const container = new ContainerBuilder()
        .addSectionComponents(thumbnailSection)
        .addSeparatorComponents(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false))
        .addTextDisplayComponents(
            new TextDisplayBuilder().setContent(`**title gained:** ${levelEmoji} ${levelName}\n**total global exp:** ${levelData.newExp.toLocaleString()}`)
        )
        .addSeparatorComponents(new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small).setDivider(false))
        .addTextDisplayComponents(new TextDisplayBuilder().setContent(rewardsText))
        .setAccentColor(OPERATION_COLORS.ENTITY);

    return container;
}

/**
 * Add global level-up notification to user's queue
 * @param {string} userId - User ID
 * @param {Object} levelUpData - Level up information
 * @returns {Promise<boolean>} Success status
 */
async function addGlobalLevelNotification(userId, levelUpData) {
    try {
        const notification = {
            userId: userId,
            levelUpData: levelUpData,
            createdAt: new Date(),
            viewed: false
        };

        await optimizedInsertOne('global_level_notifications', notification);

        console.log(`[globalLevelNotifications] ✅ Added global level notification for ${userId} (level ${levelUpData.newLevel})`);
        return true;

    } catch (error) {
        console.error('[globalLevelNotifications] Error adding notification:', error);
        return false;
    }
}

/**
 * Get unviewed global level notifications for a user
 * @param {string} userId - User ID
 * @param {number} limit - Maximum number of notifications to return
 * @returns {Promise<Array>} Array of notifications
 */
async function getGlobalLevelNotifications(userId, limit = 5) {
    try {
        const notifications = await optimizedFind('global_level_notifications', {
            userId: userId,
            viewed: false
        }, {
            projection: {
                levelUpData: 1,
                createdAt: 1,
                _id: 1
            },
            sort: { createdAt: -1 },
            limit: limit
        });

        return notifications;

    } catch (error) {
        console.error('[globalLevelNotifications] Error getting notifications:', error);
        return [];
    }
}

/**
 * Dismiss (mark as viewed) a global level notification
 * @param {string} notificationId - Notification ID
 * @returns {Promise<boolean>} Success status
 */
async function dismissGlobalLevelNotification(notificationId) {
    try {
        const { ObjectId } = require('mongodb');

        await optimizedUpdateOne('global_level_notifications',
            { _id: new ObjectId(notificationId) },
            { $set: { viewed: true, viewedAt: new Date() } }
        );

        console.log(`[globalLevelNotifications] ✅ Dismissed global level notification ${notificationId}`);
        return true;

    } catch (error) {
        console.error('[globalLevelNotifications] Error dismissing notification:', error);
        return false;
    }
}

/**
 * Build global level notification display container
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} Notification container or null if no notifications
 */
async function buildGlobalLevelNotificationDisplay(userId) {
    try {
        const notifications = await getGlobalLevelNotifications(userId, 1);

        if (notifications.length === 0) {
            return null; // No notifications to display
        }

        const notification = notifications[0];
        const levelData = notification.levelUpData;

        // Get global level configuration data
        const { getCachedGlobalLevels } = require('./globalLevels.js');
        const levels = await getCachedGlobalLevels();
        const globalLevelData = levels.find(level => level.level === levelData.newLevel);

        // For notification center, we need a user avatar - try to get it from client if available
        let userAvatar = 'https://cdn.discordapp.com/embed/avatars/0.png'; // Default avatar
        try {
            // Try to get user from client if available (this might not always work in notification center context)
            if (global.client) {
                const user = await global.client.users.fetch(userId).catch(() => null);
                if (user) {
                    userAvatar = user.displayAvatarURL();
                }
            }
        } catch (error) {
            // Use default avatar if we can't fetch user
        }

        // Build level-up container for notification center
        const container = buildLevelUpContainer(levelData, userAvatar, globalLevelData);

        // Add dismiss button
        const dismissButton = new ButtonBuilder()
            .setCustomId(`dismiss-global-level-${notification._id}`)
            .setLabel('dismiss')
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('✅');

        const buttonRow = new ActionRowBuilder().addComponents(dismissButton);

        return { container, buttonRow, notificationId: notification._id };

    } catch (error) {
        console.error('[globalLevelNotifications] Error building notification display:', error);
        return null;
    }
}

/**
 * Send global level-up DM notification with new Components v2 design
 * @param {string} userId - User ID
 * @param {Object} levelUpData - Level up information
 * @param {Object} client - Discord client
 * @returns {Promise<boolean>} Success status
 */
async function sendGlobalLevelDM(userId, levelUpData, client) {
    try {
        // Check if user has global level DM notifications enabled
        const userData = await optimizedFindOne('users', { id: userId });
        const globalLevelDMEnabled = userData?.globalLevelDMNotificationsEnabled === true;

        if (!globalLevelDMEnabled) {
            console.log(`[globalLevelNotifications] Global level DM notifications disabled for user ${userId}`);
            return false;
        }

        // Get user object
        const user = await client.users.fetch(userId).catch(() => null);
        if (!user) {
            console.log(`[globalLevelNotifications] Could not fetch user ${userId}`);
            return false;
        }

        // Get global level configuration data
        const { getCachedGlobalLevels } = require('./globalLevels.js');
        const levels = await getCachedGlobalLevels();
        const globalLevelData = levels.find(level => level.level === levelUpData.newLevel);

        // Build level-up container
        const levelUpContainer = buildLevelUpContainer(levelUpData, user.displayAvatarURL(), globalLevelData);

        // Create dynamic message text based on rewards
        const levelName = globalLevelData?.name || 'Unknown';
        const levelEmoji = globalLevelData?.icon || '🌟';

        let dmMessageText = `You leveled up to ${levelEmoji} **${levelName}**, global level ${levelUpData.newLevel}`;

        const hasItems = levelUpData.levelRewards && levelUpData.levelRewards.items && levelUpData.levelRewards.items.length > 0;
        const hasOtherRewards = (levelUpData.levelRewards && levelUpData.levelRewards.xpBooster) || (levelUpData.levelRewards && levelUpData.levelRewards.dropBooster);

        if (hasItems && hasOtherRewards) {
            dmMessageText += ', and received multiple rewards:';
        } else if (hasItems) {
            dmMessageText += ', and received an item:';
        } else if (hasOtherRewards) {
            dmMessageText += ', and received a reward:';
        } else {
            dmMessageText += ':';
        }

        const dmMessageHeader = new TextDisplayBuilder().setContent(dmMessageText);

        // Prepare components array
        const components = [dmMessageHeader, levelUpContainer];

        // Add item containers for LEVEL_UP items
        if (hasItems) {
            const { buildFoundItemContainer } = require('../commands/utility/items.js');

            for (const item of levelUpData.levelRewards.items) {
                // Create item data structure for the container
                const itemData = {
                    itemName: item.name,
                    name: item.name,
                    emote: item.emote,
                    rarity: item.rarity,
                    type: item.type,
                    itemType: item.type,
                    description: item.description
                };

                // Create catch data from the inventory item
                const catchData = item.catchData || {};

                // Create context
                const context = {
                    user: user,
                    server: null, // Global level-ups have no server context
                    location: 'level up'
                };

                // Create leaderboard results from the item
                const leaderboardResults = item.leaderboardResults || {
                    serverRank: null,
                    globalRank: null,
                    isNewServerRecord: false,
                    isNewGlobalRecord: false
                };

                try {
                    const itemContainer = await buildFoundItemContainer(itemData, catchData, context, leaderboardResults);
                    components.push(itemContainer);
                } catch (itemError) {
                    console.error('[globalLevelNotifications] Error building item container:', itemError);
                }
            }
        }

        // Send DM with Components v2
        await user.send({
            flags: MessageFlags.IsComponentsV2,
            components: components,
            allowedMentions: { parse: [] }
        });

        console.log(`[globalLevelNotifications] ✅ Sent global level DM to ${userId} with ${components.length} components`);
        return true;

    } catch (error) {
        console.error('[globalLevelNotifications] Error sending global level DM:', error);
        return false;
    }
}

/**
 * Process global level-up (add to notification queue, send DM, and handle item drop notifications)
 * @param {string} userId - User ID
 * @param {Object} levelUpData - Level up information
 * @param {Object} client - Discord client
 */
async function processGlobalLevelUp(userId, levelUpData, client) {
    try {
        // Add to notification queue
        await addGlobalLevelNotification(userId, levelUpData);

        // Send DM notification (async, don't wait)
        sendGlobalLevelDM(userId, levelUpData, client).catch(error => {
            console.error('[globalLevelNotifications] Error sending global level DM:', error);
        });

        // Process item drop notifications if items were awarded
        if (levelUpData.levelRewards && levelUpData.levelRewards.items && levelUpData.levelRewards.items.length > 0) {
            try {
                const {
                    addItemDropNotification,
                    sendItemDropDM,
                    checkFirstItemDropInServer
                } = require('./itemDrops.js');

                // Process each item for notifications
                for (const awardedItem of levelUpData.levelRewards.items) {
                    console.log(`[globalLevelNotifications] 🎁 Processing level-up item notifications: ${awardedItem.itemName} for user ${userId}`);

                    // Process notifications and DMs in parallel (don't wait for them)
                    const notificationPromises = [];

                    // Add to notification queue (global level-ups use null guild)
                    notificationPromises.push(
                        addItemDropNotification(userId, null, [awardedItem], 'LEVEL_UP').catch(error => {
                            console.error('[globalLevelNotifications] Error adding level-up item notification:', error);
                        })
                    );

                    // Check if this is first drop globally for logging
                    notificationPromises.push(
                        checkFirstItemDropInServer(null, awardedItem.itemId).then(isFirst => {
                            if (isFirst) {
                                console.log(`[globalLevelNotifications] 🎉 First global ${awardedItem.itemName} drop via level-up`);
                            }
                        }).catch(error => {
                            console.error('[globalLevelNotifications] Error checking first drop:', error);
                        })
                    );

                    // Send DM notification for the item
                    notificationPromises.push(
                        sendItemDropDM(userId, null, [awardedItem], 'LEVEL_UP', client).then(result => {
                            if (result.success) {
                                console.log(`[globalLevelNotifications] ✅ Level-up item DM sent to user ${userId} for ${awardedItem.itemName}`);
                            } else {
                                console.log(`[globalLevelNotifications] ❌ Level-up item DM failed for user ${userId}: ${result.reason || result.error}`);
                            }
                        }).catch(error => {
                            console.error('[globalLevelNotifications] Error sending level-up item DM:', error);
                        })
                    );

                    // Execute all notifications in parallel without blocking
                    Promise.all(notificationPromises);
                }
            } catch (itemNotificationError) {
                console.error('[globalLevelNotifications] Error processing item drop notifications:', itemNotificationError);
            }
        }

        console.log(`[globalLevelNotifications] ✅ Processed global level-up for ${userId}`);

    } catch (error) {
        console.error('[globalLevelNotifications] Error processing global level-up:', error);
    }
}

module.exports = {
    addGlobalLevelNotification,
    getGlobalLevelNotifications,
    dismissGlobalLevelNotification,
    buildGlobalLevelNotificationDisplay,
    sendGlobalLevelDM,
    processGlobalLevelUp
};
