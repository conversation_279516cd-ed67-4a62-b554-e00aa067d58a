const { optimizedFind, optimizedFindOne, optimizedInsertOne, optimizedUpdateOne, optimizedBulkWrite } = require('./database-optimizer.js');
const { ObjectId } = require('mongodb');
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * Item Drops System (Enterprise-Grade Performance Optimized)
 * Handles item drops, inventory management, and notifications with comprehensive optimization
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const itemDropsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    itemsProcessed: 0,
    dropsSuccessful: 0,
    parallelOperations: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildDropConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild drop configurations
const itemNotificationConfigCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for notification configs
const userDropSettingsCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user drop settings
const droppableItemsCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for droppable items
const itemDataCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for item data

// Register caches for global cleanup
registerCache(guildDropConfigCache);
registerCache(itemNotificationConfigCache);
registerCache(userDropSettingsCache);
registerCache(droppableItemsCache);
registerCache(itemDataCache);

/**
 * Get cached guild drop configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild drop configuration
 */
async function getCachedGuildDropConfig(guildId) {
    const startTime = Date.now();

    // OPTIMIZED: Check LRU cache first (automatic TTL and size management)
    const cached = guildDropConfigCache.get(guildId);
    if (cached) {
        itemDropsMetrics.cacheHits++;
        if (itemDropsMetrics.verboseLogging) {
            console.log(`[itemDrops] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    try {
        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        const guildData = await optimizedFindOne("guilds", { id: guildId });
        const config = guildData || {};

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        guildDropConfigCache.set(guildId, config);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        itemDropsMetrics.averageQueryTime =
            (itemDropsMetrics.averageQueryTime * (itemDropsMetrics.databaseQueries - 1) + duration) /
            itemDropsMetrics.databaseQueries;

        if (itemDropsMetrics.verboseLogging || duration > 50) {
            console.log(`[itemDrops] ✅ Guild config fetched for ${guildId} (${duration}ms) - cached for future access`);
        }

        return config;
    } catch (error) {
        console.error(`[itemDrops] ❌ Error getting guild drop config for ${guildId}:`, error);
        return {};
    }
}

/**
 * Get cached item notification configuration
 * @param {string} key - Configuration key (usually 'global')
 * @returns {Promise<Object>} Notification configuration
 */
async function getCachedItemNotificationConfig(key) {
    // OPTIMIZED: Check LRU cache first (automatic TTL and size management)
    const cached = itemNotificationConfigCache.get(key);
    if (cached) {
        return cached;
    }

    try {
        const config = await optimizedFindOne("item_notifications", { key: key });
        const result = config || {};

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        itemNotificationConfigCache.set(key, result);

        return result;
    } catch (error) {
        console.error(`[itemDrops] Error getting item notification config for ${key}:`, error);
        return {};
    }
}

/**
 * Get cached user drop settings
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User drop settings
 */
async function getCachedUserDropSettings(userId) {
    // OPTIMIZED: Check LRU cache first (automatic TTL and size management)
    const cached = userDropSettingsCache.get(userId);
    if (cached) {
        return cached;
    }

    try {
        const userData = await optimizedFindOne("users", { id: userId });
        const settings = userData || {};

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        userDropSettingsCache.set(userId, settings);

        return settings;
    } catch (error) {
        console.error(`[itemDrops] Error getting user drop settings for ${userId}:`, error);
        return {};
    }
}

/**
 * Invalidate item drop related caches
 * @param {string} guildId - Guild ID (optional)
 * @param {string} userId - User ID (optional)
 */
function invalidateItemDropCaches(guildId = null, userId = null) {
    if (guildId) {
        guildDropConfigCache.delete(guildId);
    }
    if (userId) {
        userDropSettingsCache.delete(userId);
    }
    // Clear global notification config cache when needed
    itemNotificationConfigCache.delete('global');
}

/**
 * Item Drop System
 * Handles item drops during EXP gain events
 */

/**
 * Perform single master roll to determine if any item drops and which one
 * Uses spinning wheel approach with weights and applies global drop chance boosters
 * @param {Array} droppableItems - Array of items that can drop
 * @param {number} dropChanceMultiplier - Global drop chance multiplier (default 1.0)
 * @returns {Object|null} Selected item or null if nothing drops
 */
function performMasterRoll(droppableItems, dropChanceMultiplier = 1.0) {
    if (droppableItems.length === 0) {
        return null;
    }

    const { RARITIES, NOTHING_WEIGHT } = require('../commands/utility/items.js');

    // Apply drop chance booster by reducing the "nothing" weight
    // Higher multiplier = lower nothing weight = more drops
    const adjustedNothingWeight = Math.max(1, Math.floor(NOTHING_WEIGHT / dropChanceMultiplier));

    // Calculate total weight including adjusted "nothing" weight
    let totalWeight = adjustedNothingWeight;

    // Add up all item weights
    for (const item of droppableItems) {
        const rarityData = RARITIES[item.rarity];
        if (rarityData) {
            totalWeight += rarityData.weight;
        }
    }

    // Roll from 1 to totalWeight
    const roll = Math.floor(Math.random() * totalWeight) + 1;

    // Check if roll falls in "nothing" range
    if (roll <= adjustedNothingWeight) {
        return null; // Nothing drops
    }

    // Find which item this roll corresponds to
    let currentWeight = adjustedNothingWeight;
    for (const item of droppableItems) {
        const rarityData = RARITIES[item.rarity];
        if (rarityData) {
            currentWeight += rarityData.weight;
            if (roll <= currentWeight) {
                return item; // This item drops
            }
        }
    }

    return null; // Fallback to nothing
}

/**
 * Get all items that can drop from a specific location (Enterprise-Grade Optimized)
 * OPTIMIZED: LRU caching with performance monitoring and enhanced error handling
 * @param {string} location - Drop location (TEXT, VOICE, etc.)
 * @param {string} guildId - Guild ID (null for bot-wide items)
 * @returns {Array} Array of items that can drop from this location
 */
async function getDroppableItems(location, guildId) {
    const startTime = Date.now();
    const cacheKey = `${location}_${guildId || 'global'}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = droppableItemsCache.get(cacheKey);
        if (cached) {
            itemDropsMetrics.cacheHits++;
            if (itemDropsMetrics.verboseLogging) {
                console.log(`[itemDrops] ⚡ Droppable items cache hit for ${cacheKey} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        // Find items that can drop from this location (and are not disabled)
        const query = {
            dropLocations: location,
            disabled: { $ne: true }, // Exclude disabled items
            $or: [
                { guildId: guildId }, // Guild-specific items
                { guildId: null }     // Bot-wide items
            ]
        };

        // Use projection to only fetch needed fields for better performance
        const items = await optimizedFind("custom_items", query, {
            projection: {
                id: 1,
                name: 1,
                type: 1,
                rarity: 1,
                emote: 1,
                description: 1,
                parameters: 1,
                guildId: 1
            }
        });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        droppableItemsCache.set(cacheKey, items);

        const duration = Date.now() - startTime;
        if (itemDropsMetrics.verboseLogging || duration > 100) {
            console.log(`[itemDrops] ✅ Droppable items fetched for ${cacheKey}: ${items.length} items (${duration}ms)`);
        }

        return items;

    } catch (error) {
        console.error('[itemDrops] ❌ Error fetching droppable items:', error);
        return [];
    }
}

/**
 * Process item drops for EXP gain event (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing, enhanced error handling, and comprehensive performance monitoring
 * @param {string} userId - User ID who gained EXP
 * @param {string} guildId - Guild ID where EXP was gained
 * @param {string} location - Drop location (TEXT, VOICE, etc.)
 * @param {number} expGained - Amount of EXP gained
 * @returns {Array} Array with single dropped item or empty array
 */
async function processItemDrops(userId, guildId, location, expGained) {
    const startTime = Date.now();
    itemDropsMetrics.itemsProcessed++;

    // CRITICAL: Validate that EXP was actually gained before processing item drops
    if (!expGained || expGained <= 0) {
        console.log(`[processItemDrops] ⚠️  No EXP gained (${expGained}), skipping item drops for ${userId}`);
        return [];
    }

    try {
        // OPTIMIZED: Parallel fetch of droppable items and user boosters
        const [droppableItems, dropChanceMultiplier] = await Promise.allSettled([
            getDroppableItems(location, guildId),
            (async () => {
                try {
                    const { getUserBoosters } = require('./globalLevels.js');
                    const boosters = await getUserBoosters(userId);
                    return boosters.dropChanceMultiplier;
                } catch (boosterError) {
                    if (itemDropsMetrics.verboseLogging) {
                        console.error('[processItemDrops] Error getting drop chance booster:', boosterError);
                    }
                    return 1.0; // Default multiplier
                }
            })()
        ]);

        // Extract results from Promise.allSettled
        const items = droppableItems.status === 'fulfilled' ? droppableItems.value : [];
        const multiplier = dropChanceMultiplier.status === 'fulfilled' ? dropChanceMultiplier.value : 1.0;

        if (items.length === 0) {
            return []; // No items configured for this location
        }

        // Perform single master roll with booster applied
        const selectedItem = performMasterRoll(items, multiplier);

        if (!selectedItem) {
            return []; // Nothing dropped
        }

        // Enhanced logging with performance context
        if (multiplier > 1.0 && itemDropsMetrics.verboseLogging) {
            console.log(`[processItemDrops] Applied ${multiplier}x drop chance booster for ${userId}`);
        }

        try {
            if (itemDropsMetrics.verboseLogging) {
                console.log('[processItemDrops] Item dropped:', selectedItem.name, 'with parameters:', selectedItem.parameters);
            }

            const droppedItem = await addItemToInventory(userId, guildId, selectedItem, location);

            if (droppedItem) {
                itemDropsMetrics.dropsSuccessful++;

                if (itemDropsMetrics.verboseLogging) {
                    console.log('[processItemDrops] Added to inventory:', droppedItem);
                }

                // OPTIMIZED: Process all post-drop operations in parallel
                itemDropsMetrics.parallelOperations++;
                const parallelTasks = await Promise.allSettled([
                    checkFirstItemDropInServer(guildId, selectedItem.id),
                    // Log the item drop
                    (async () => {
                        try {
                            const { sendItemDropLog } = require('./sendLog.js');
                            await sendItemDropLog(guildId, userId, droppedItem, location, expGained, false);
                        } catch (logError) {
                            console.error('[processItemDrops] Error sending item drop log:', logError);
                        }
                    })(),
                    // Add item to notification queue
                    addItemDropNotification(userId, guildId, [droppedItem], location)
                ]);

                // FIXED: Invalidate discovery count caches to ensure accurate totals
                try {
                    const { invalidateDiscoveryCountCache } = require('../utils/globalLevelNotifications.js');
                    await invalidateDiscoveryCountCache(selectedItem.name, selectedItem.type, guildId);
                    console.log(`[processItemDrops] 🔄 Invalidated discovery count cache for ${selectedItem.name}`);
                } catch (cacheError) {
                    console.error('[processItemDrops] Error invalidating discovery cache:', cacheError);
                }

                // Handle first-drop status update if needed
                const isFirstInServer = parallelTasks[0].status === 'fulfilled' ? parallelTasks[0].value : false;
                if (isFirstInServer) {
                    try {
                        const { sendItemDropLog } = require('./sendLog.js');
                        await sendItemDropLog(guildId, userId, droppedItem, location, expGained, true);
                    } catch (logError) {
                        console.error('[processItemDrops] Error sending first-drop log:', logError);
                    }
                }

                const duration = Date.now() - startTime;
                if (itemDropsMetrics.verboseLogging || duration > 200) {
                    console.log(`[processItemDrops] ✅ Item drop processed for ${userId}: ${selectedItem.name} (${duration}ms)`);
                }

                return [droppedItem]; // Return single item in array for compatibility
            }
        } catch (error) {
            console.error('[processItemDrops] ❌ Error processing dropped item:', error);
        }

        return []; // Nothing dropped or error occurred

    } catch (error) {
        console.error('[processItemDrops] ❌ Error processing item drops:', error);
        return [];
    }
}

/**
 * Check if this is the first time an item has dropped in a server
 * @param {string} guildId - Guild ID
 * @param {string} itemId - Item ID
 * @returns {boolean} True if this is the first drop in the server
 */
async function checkFirstItemDropInServer(guildId, itemId) {
    try {


        // Check if any user in this guild has this item
        const existingDrop = await optimizedFindOne("user_inventory", {
            guildId: guildId,
            itemId: itemId
        });

        return !existingDrop; // True if no existing drop found

    } catch (error) {
        console.error('Error checking first item drop:', error);
        return false; // Default to false on error
    }
}

/**
 * Add dropped item to user's inventory
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Object} item - Item data
 * @param {string} location - Drop location
 * @returns {Object|null} Inventory item or null if failed
 */
async function addItemToInventory(userId, guildId, item, location) {
    try {


        // Debug the original item
        console.log('[addItemToInventory] Original item:', item);
        console.log('[addItemToInventory] Item parameters:', item.parameters);

        // Randomize item parameters when caught
        const { randomizeItemParameters } = require('../commands/utility/items.js');
        const randomizedItem = randomizeItemParameters(item);

        console.log('[addItemToInventory] Randomized item parameters:', randomizedItem.parameters);

        // Create inventory item with randomized parameters
        const inventoryItem = {
            id: `inv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            userId: userId,
            guildId: guildId,
            itemId: item.id,
            itemName: item.name,
            itemType: item.type,
            itemRarity: item.rarity,
            itemEmote: item.emote,
            itemDescription: item.description,
            droppedAt: new Date(),
            droppedFrom: location,
            // Store the randomized parameters as catchData
            catchData: randomizedItem.parameters || {},
            // Keep original parameters for reference
            itemParameters: item.parameters || {}
        };

        // Insert item and update leaderboards in parallel for better performance
        const [insertResult, leaderboardResults] = await Promise.all([
            optimizedInsertOne("user_inventory", inventoryItem),
            (async () => {
                try {
                    const { updateItemLeaderboards } = require('./itemRecords.js');
                    return await updateItemLeaderboards(inventoryItem);
                } catch (error) {
                    console.error('[itemDrops] Error updating leaderboards:', error);
                    return { guildRecords: [], globalRecords: [], guildRanks: {}, globalRanks: {} };
                }
            })()
        ]);

        // Log any records achieved
        if (leaderboardResults.guildRecords.length > 0) {
            console.log(`[itemDrops] 🏆 Guild records achieved:`, leaderboardResults.guildRecords);
        }
        if (leaderboardResults.globalRecords.length > 0) {
            console.log(`[itemDrops] 🌟 Global records achieved:`, leaderboardResults.globalRecords);
        }

        // Store leaderboard results on the inventory item for potential UI use later
        inventoryItem.leaderboardResults = leaderboardResults;

        // Clear live totals cache to ensure fresh data for dynamic inventory views
        try {
            const { clearLiveTotalsCache } = require('../commands/utility/items.js');
            clearLiveTotalsCache();
        } catch (error) {
            console.error('[itemDrops] Error clearing live totals cache:', error);
        }

        return inventoryItem;

    } catch (error) {
        console.error('Error adding item to inventory:', error);
        return null;
    }
}

/**
 * Get user's inventory items (guild-specific - legacy function)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Array} Array of inventory items
 */
async function getUserInventory(userId, guildId) {
    try {


        const items = await optimizedFind("user_inventory", {
            userId: userId,
            guildId: guildId
        }, { sort: { droppedAt: -1 } });

        return items;

    } catch (error) {
        console.error('Error fetching user inventory:', error);
        return [];
    }
}

/**
 * Get user's global inventory items (across all servers)
 * @param {string} userId - User ID
 * @returns {Array} Array of inventory items
 */
async function getUserGlobalInventory(userId) {
    try {


        const items = await optimizedFind("user_inventory", {
            userId: userId
        }, { sort: { droppedAt: -1 } });

        return items;

    } catch (error) {
        console.error('Error fetching user global inventory:', error);
        return [];
    }
}

/**
 * Send DM notification for item drop
 * @param {string} userId - User ID who received item
 * @param {string} guildId - Guild ID where item dropped
 * @param {Array} droppedItems - Array with single dropped item
 * @param {string} location - Drop location (TEXT, VOICE, etc.)
 * @param {Object} client - Discord client
 * @returns {boolean} True if DM was sent successfully
 */
async function sendItemDropDM(userId, guildId, droppedItems, location, client) {
    try {


        // OPTIMIZED: Check global owner setting using cached function
        const ownerConfig = await getCachedItemNotificationConfig('global');
        const globalDMEnabled = ownerConfig?.enabled ?? true;

        if (!globalDMEnabled) {
            console.log(`[itemDropDM] DM notifications disabled globally`);
            return { success: false, reason: 'disabled_globally' };
        }

        // OPTIMIZED: Check user's personal DM preference using cached function
        const userData = await getCachedUserDropSettings(userId);
        const userDMEnabled = userData?.itemDMNotificationsEnabled === true; // Default to false unless explicitly enabled

        if (!userDMEnabled) {
            console.log(`[itemDropDM] DM notifications disabled for user ${userId}`);
            return { success: false, reason: 'disabled_user' };
        }

        // Get user and guild objects
        const user = await client.users.fetch(userId).catch(() => null);
        const guild = await client.guilds.fetch(guildId).catch(() => null);

        if (!user || !guild) {
            console.log(`[itemDropDM] Could not fetch user or guild`);
            return { success: false, reason: 'fetch_failed' };
        }

        // Build proper DM with custom message + item embeds
        const { ContainerBuilder, TextDisplayBuilder, MessageFlags } = require('discord.js');
        const { RARITIES, DROP_LOCATIONS, buildFoundItemContainer } = require('../commands/utility/items.js');

        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Get custom DM message template from bot owner configuration
        let dmMessageTemplate;
        try {
            // OPTIMIZED: Get DM message template using cached function
            const ownerConfig = await getCachedItemNotificationConfig('global');
            dmMessageTemplate = ownerConfig?.dmMessage || 'You found {items} in **{server}**, dropped from {location}:';
        } catch (error) {
            console.error('[itemDropDM] Error fetching owner config:', error);
            dmMessageTemplate = 'You found {items} in **{server}**, dropped from {location}:';
        }

        // Since only one item can drop now, simplify the message
        const { getArticle } = require('../commands/utility/items.js');
        const droppedItem = droppedItems[0]; // Only one item possible
        const article = getArticle(droppedItem.itemName);
        const itemText = `${article} ${droppedItem.itemName}`;

        // Replace template variables in custom message
        const customMessage = dmMessageTemplate
            .replace('{items}', itemText)
            .replace('{server}', guild.name)
            .replace('{location}', locationText);

        // Build item container for the single dropped item
        const item = droppedItem;

        // Build context for the found item
        const context = {
            user: user.username,
            server: guild.name,
            location: locationText,
            timestamp: item.droppedAt, // Add timestamp for DMs
            userId: userId // Add userId for personal record checking
        };

        // Create proper item data structure for buildFoundItemContainer
        // Need to get the original item's guildId from the database
        const originalItem = await optimizedFindOne("custom_items", { id: item.itemId });

        const itemData = {
            itemName: item.itemName,
            itemType: item.itemType,
            rarity: item.itemRarity,
            emote: item.itemEmote,
            description: item.itemDescription,
            guildId: originalItem?.guildId // Add the guildId from original item
        };

        // Build the item container
        const itemContainer = await buildFoundItemContainer(itemData, item.catchData || {}, context, item.leaderboardResults);

        // Create simple text display for the custom message (not in container)
        const textDisplay = new TextDisplayBuilder().setContent(customMessage);

        // Combine text message + item container
        const allComponents = [textDisplay, itemContainer];

        // Try to send DM
        try {
            await user.send({
                flags: MessageFlags.IsComponentsV2,
                components: allComponents
            });
            console.log(`[itemDropDM] ✅ DM sent to ${user.username} for ${droppedItem.itemName}`);
            return { success: true };
        } catch (dmError) {
            console.log(`[itemDropDM] ❌ Could not send DM to ${user.username}: ${dmError.message}`);

            // Return detailed error info for fallback handling
            return {
                success: false,
                error: dmError.message,
                user: user,
                guild: guild,
                items: droppedItems,
                location: locationText
            };
        }

    } catch (error) {
        console.error('[itemDropDM] Error sending item drop DM:', error);
        return { success: false, reason: 'error', error: error.message };
    }
}

/**
 * Send guild channel notification for item drop (without pings)
 * @param {string} userId - User ID who received item
 * @param {string} guildId - Guild ID where item dropped
 * @param {Array} droppedItems - Array with single dropped item
 * @param {string} location - Drop location (TEXT, VOICE, etc.)
 * @param {Object} client - Discord client
 * @returns {boolean} True if notification was sent successfully
 */
async function sendGuildChannelNotification(userId, guildId, droppedItems, location, client) {
    try {
        // OPTIMIZED: Check if guild channel notifications are enabled using cached function
        const guildData = await getCachedGuildDropConfig(guildId);

        const dropNotificationsEnabled = guildData?.items?.dropNotificationsEnabled ?? true;
        const dropChannelId = guildData?.items?.dropChannel;

        if (!dropNotificationsEnabled || !dropChannelId) {
            console.log(`[guildChannelNotification] Notifications disabled or no channel set for guild ${guildId}`);
            return false;
        }

        // Get user and guild objects
        const user = await client.users.fetch(userId).catch(() => null);
        const guild = await client.guilds.fetch(guildId).catch(() => null);
        const channel = await client.channels.fetch(dropChannelId).catch(() => null);

        if (!user || !guild || !channel) {
            console.log(`[guildChannelNotification] Could not fetch user, guild, or channel`);
            return false;
        }

        // Build notification message using server version (includes "found by" info)
        const { ContainerBuilder, TextDisplayBuilder, MessageFlags } = require('discord.js');
        const { RARITIES, DROP_LOCATIONS, buildServerFoundItemContainer } = require('../commands/utility/items.js');

        const locationInfo = DROP_LOCATIONS[location];
        const locationText = locationInfo ? (locationInfo.displayName || locationInfo.name) : location;

        // Build item components for the single dropped item (server version with context text)
        const item = droppedItems[0]; // Only one item possible

        // Build context for the found item
        const context = {
            user: `<@${userId}>`, // Mention user (no ping with allowedMentions)
            server: guild.name,
            location: locationText,
            timestamp: item.droppedAt, // Add timestamp for server notifications
            userId: userId // Add userId for personal record checking
        };

        // Create proper item data structure
        // Need to get the original item's guildId from the database
        const originalItem = await optimizedFindOne("custom_items", { id: item.itemId });

        const itemData = {
            itemName: item.itemName,
            itemType: item.itemType,
            rarity: item.itemRarity,
            emote: item.itemEmote,
            description: item.itemDescription,
            guildId: originalItem?.guildId // Add the guildId from original item
        };

        // Use the server version which returns [textComponent, itemContainer] with leaderboard results
        const [textComponent, itemContainer] = await buildServerFoundItemContainer(itemData, item.catchData || {}, context, item.leaderboardResults);
        const allComponents = [textComponent, itemContainer];

        // Send to guild channel (with no pings)
        try {
            await channel.send({
                flags: MessageFlags.IsComponentsV2,
                allowedMentions: { parse: [] }, // No pings
                components: allComponents
            });
            console.log(`[guildChannelNotification] ✅ Sent to #${channel.name} for ${user.username} (${item.itemName})`);
            return true;
        } catch (sendError) {
            console.log(`[guildChannelNotification] ❌ Could not send to channel: ${sendError.message}`);
            return false;
        }

    } catch (error) {
        console.error('[guildChannelNotification] Error sending guild channel notification:', error);
        return false;
    }
}

/**
 * Add item drop notification to user's queue
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Array} droppedItems - Array of dropped items
 * @param {string} location - Drop location
 * @returns {boolean} Success status
 */
async function addItemDropNotification(userId, guildId, droppedItems, location) {
    try {


        const notification = {
            userId: userId,
            guildId: guildId,
            items: droppedItems,
            location: location,
            createdAt: new Date(),
            viewed: false
        };

        await optimizedInsertOne("item_notifications_queue", notification);

        console.log(`[itemDropQueue] ✅ Added notification for ${userId} in ${guildId} (${droppedItems[0].itemName})`);
        return true;

    } catch (error) {
        console.error('[itemDropQueue] Error adding notification:', error);
        return false;
    }
}

/**
 * Get unviewed item drop notifications for a user in a guild (optimized)
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {number} limit - Maximum number of notifications to return (default: 5)
 * @returns {Array} Array of notifications
 */
async function getUserItemNotifications(userId, guildId, limit = 5) {
    try {


        // FIXED: Include both guild-specific and global (level-up) notifications
        const notifications = await optimizedFind("item_notifications_queue", {
            userId: userId,
            $or: [
                { guildId: guildId },      // Guild-specific notifications
                { guildId: null }          // Global level-up notifications
            ],
            viewed: false
        }, {
            // Only fetch needed fields for performance
            projection: {
                items: 1,
                location: 1,
                createdAt: 1,
                _id: 1 // Needed for dismiss functionality
            },
            sort: { createdAt: 1 }, // Oldest first
            limit: limit // Limit results for performance
        });

        return notifications;

    } catch (error) {
        console.error('[itemDropQueue] Error getting notifications:', error);
        return [];
    }
}

/**
 * Dismiss (mark as viewed) an item drop notification
 * @param {string} notificationId - Notification ID
 * @returns {boolean} Success status
 */
async function dismissItemNotification(notificationId) {
    try {


        await optimizedUpdateOne("item_notifications_queue",
            { _id: new ObjectId(notificationId) },
            { $set: { viewed: true, viewedAt: new Date() } }
        );

        console.log(`[itemDropQueue] ✅ Dismissed notification ${notificationId}`);
        return true;

    } catch (error) {
        console.error('[itemDropQueue] Error dismissing notification:', error);
        return false;
    }
}

/**
 * Get item data by ID with LRU caching (Enterprise-Grade Optimized)
 * OPTIMIZED: LRU caching with performance monitoring and enhanced error handling
 * @param {string} itemId - Item ID
 * @returns {Promise<Object|null>} Item data or null if not found
 */
async function getItemData(itemId) {
    const startTime = Date.now();
    const cacheKey = `item_${itemId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = itemDataCache.get(cacheKey);
        if (cached !== undefined) {
            itemDropsMetrics.cacheHits++;
            if (itemDropsMetrics.verboseLogging) {
                console.log(`[itemDrops] ⚡ Item data cache hit for ${itemId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        itemDropsMetrics.cacheMisses++;
        itemDropsMetrics.databaseQueries++;

        const itemData = await optimizedFindOne("custom_items", { id: itemId });

        // Cache the result (including null results to prevent repeated queries)
        itemDataCache.set(cacheKey, itemData);

        const duration = Date.now() - startTime;
        if (itemDropsMetrics.verboseLogging || duration > 50) {
            console.log(`[itemDrops] ✅ Item data fetched for ${itemId}: ${itemData ? 'found' : 'not found'} (${duration}ms)`);
        }

        return itemData;
    } catch (error) {
        console.error(`[itemDrops] ❌ Error getting item data for ${itemId}:`, error);
        return null;
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = itemDropsMetrics.cacheHits + itemDropsMetrics.cacheMisses > 0 ?
        (itemDropsMetrics.cacheHits / (itemDropsMetrics.cacheHits + itemDropsMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: itemDropsMetrics.cacheHits,
            cacheMisses: itemDropsMetrics.cacheMisses,
            databaseQueries: itemDropsMetrics.databaseQueries,
            averageQueryTime: `${itemDropsMetrics.averageQueryTime.toFixed(2)}ms`,
            itemsProcessed: itemDropsMetrics.itemsProcessed,
            dropsSuccessful: itemDropsMetrics.dropsSuccessful,
            parallelOperations: itemDropsMetrics.parallelOperations,
            successRate: itemDropsMetrics.itemsProcessed > 0 ?
                `${((itemDropsMetrics.dropsSuccessful / itemDropsMetrics.itemsProcessed) * 100).toFixed(2)}%` : '0%',
            lastOptimization: new Date(itemDropsMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildDropConfig: guildDropConfigCache.getStats(),
            itemNotificationConfig: itemNotificationConfigCache.getStats(),
            userDropSettings: userDropSettingsCache.getStats(),
            droppableItems: droppableItemsCache.getStats(),
            itemData: itemDataCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildDropConfig: guildDropConfigCache.getStats().memoryUsage,
            itemNotificationConfig: itemNotificationConfigCache.getStats().memoryUsage,
            userDropSettings: userDropSettingsCache.getStats().memoryUsage,
            droppableItems: droppableItemsCache.getStats().memoryUsage,
            itemData: itemDataCache.getStats().memoryUsage,
            total: guildDropConfigCache.getStats().memoryUsage +
                   itemNotificationConfigCache.getStats().memoryUsage +
                   userDropSettingsCache.getStats().memoryUsage +
                   droppableItemsCache.getStats().memoryUsage +
                   itemDataCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, itemDropsMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 100) {
        recommendations.push('Database queries are slow - consider adding indexes or optimizing queries');
    }

    if (metrics.databaseQueries > metrics.cacheHits * 2) {
        recommendations.push('High database query ratio - consider caching more aggressively');
    }

    if (metrics.itemsProcessed > 0 && (metrics.dropsSuccessful / metrics.itemsProcessed) < 0.8) {
        recommendations.push('Low drop success rate - investigate error patterns');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    itemDropsMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[itemDrops] 📊 Performance Report:`);
    console.log(`[itemDrops]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[itemDrops]   Items Processed: ${stats.performance.itemsProcessed}`);
    console.log(`[itemDrops]   Success Rate: ${stats.performance.successRate}`);
    console.log(`[itemDrops]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[itemDrops]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[itemDrops]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[itemDrops]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[itemDrops] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all item drop caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllItemDropCaches() {
    droppableItemsCache.clear();
    itemDataCache.clear();
    guildDropConfigCache.clear();
    itemNotificationConfigCache.clear();
    userDropSettingsCache.clear();

    console.log('[itemDrops] 🗑️ Cleared all item drop caches');
}

/**
 * Clear the droppable items cache (used when items are enabled/disabled)
 */
function clearDroppableItemsCache() {
    droppableItemsCache.clear();
    console.log('[itemDrops] 🗑️ Cleared droppable items cache');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, itemDropsMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    performMasterRoll,
    getDroppableItems,
    processItemDrops,
    addItemToInventory,
    getUserInventory,
    getUserGlobalInventory,
    checkFirstItemDropInServer,
    sendItemDropDM,
    sendGuildChannelNotification,
    addItemDropNotification,
    getUserItemNotifications,
    dismissItemNotification,

    // Enhanced optimization functions
    getItemData,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllItemDropCaches,
    clearDroppableItemsCache,
    invalidateItemDropCaches,
    getCachedGuildDropConfig,
    getCachedItemNotificationConfig,
    getCachedUserDropSettings,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...itemDropsMetrics })
};
