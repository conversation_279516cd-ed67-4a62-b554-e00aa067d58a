/**
 * Opener System Caching Utility (Enterprise-Grade Performance Optimized)
 * Provides caching for frequently accessed opener data to improve performance
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

const { optimizedFindOne, optimizedFind, optimizedAggregate } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const openerCacheMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    configsServed: 0,
    threadsServed: 0,
    refreshQueuesServed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildOpenerConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes
const threadDataCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes
const refreshQueueCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes
const threadStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes

// Register caches for global cleanup
registerCache(guildOpenerConfigCache);
registerCache(threadDataCache);
registerCache(refreshQueueCache);
registerCache(threadStatsCache);

/**
 * Get cached guild opener configuration (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object>} Guild opener configuration
 */
async function getCachedGuildOpenerConfig(guildId) {
    const startTime = Date.now();

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = guildOpenerConfigCache.get(guildId);
    if (cached) {
        openerCacheMetrics.cacheHits++;
        openerCacheMetrics.configsServed++;
        if (openerCacheMetrics.verboseLogging) {
            console.log(`[openerCache] ⚡ Guild config cache hit for ${guildId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    openerCacheMetrics.cacheMisses++;
    openerCacheMetrics.databaseQueries++;

    try {
        const guildData = await optimizedFindOne("guilds",
            { id: guildId },
            { projection: { opener: 1 } } // Only fetch opener configuration
        );

        const config = guildData?.opener || { enabled: true };

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        guildOpenerConfigCache.set(guildId, config);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        openerCacheMetrics.averageQueryTime =
            (openerCacheMetrics.averageQueryTime * (openerCacheMetrics.databaseQueries - 1) + duration) /
            openerCacheMetrics.databaseQueries;

        if (openerCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[openerCache] ✅ Guild config fetched for ${guildId}: ${duration}ms - cached for future access`);
        }

        openerCacheMetrics.configsServed++;
        return config;
    } catch (error) {
        console.error(`[openerCache] ❌ Error getting guild config for ${guildId}:`, error);
        return { enabled: true };
    }
}

/**
 * Get cached thread data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} threadId - Thread ID
 * @returns {Promise<Object|null>} Thread data or null if not found
 */
async function getCachedThreadData(threadId) {
    const startTime = Date.now();

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = threadDataCache.get(threadId);
    if (cached) {
        openerCacheMetrics.cacheHits++;
        openerCacheMetrics.threadsServed++;
        if (openerCacheMetrics.verboseLogging) {
            console.log(`[openerCache] ⚡ Thread data cache hit for ${threadId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    openerCacheMetrics.cacheMisses++;
    openerCacheMetrics.databaseQueries++;

    try {
        const threadData = await optimizedFindOne("opener_threads", { threadId: threadId });

        if (threadData) {
            // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
            threadDataCache.set(threadId, threadData);
        }

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        openerCacheMetrics.averageQueryTime =
            (openerCacheMetrics.averageQueryTime * (openerCacheMetrics.databaseQueries - 1) + duration) /
            openerCacheMetrics.databaseQueries;

        if (openerCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[openerCache] ✅ Thread data fetched for ${threadId}: ${duration}ms - cached for future access`);
        }

        openerCacheMetrics.threadsServed++;
        return threadData;
    } catch (error) {
        console.error(`[openerCache] ❌ Error getting thread data for ${threadId}:`, error);
        return null;
    }
}

/**
 * Get cached refresh queue (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @returns {Promise<Array>} Array of threads needing refresh
 */
async function getCachedRefreshQueue() {
    const startTime = Date.now();

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = refreshQueueCache.get('global');
    if (cached) {
        openerCacheMetrics.cacheHits++;
        openerCacheMetrics.refreshQueuesServed++;
        if (openerCacheMetrics.verboseLogging) {
            console.log(`[openerCache] ⚡ Refresh queue cache hit (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    openerCacheMetrics.cacheMisses++;
    openerCacheMetrics.databaseQueries++;

    try {
        const now = Date.now();
        const threads = await optimizedFind("opener_threads", {
            refreshAt: { $lte: now }
        }, { sort: { refreshAt: 1 } });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        refreshQueueCache.set('global', threads);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        openerCacheMetrics.averageQueryTime =
            (openerCacheMetrics.averageQueryTime * (openerCacheMetrics.databaseQueries - 1) + duration) /
            openerCacheMetrics.databaseQueries;

        if (openerCacheMetrics.verboseLogging || duration > 100) {
            console.log(`[openerCache] ✅ Refresh queue fetched: ${threads.length} threads (${duration}ms) - cached for future access`);
        }

        openerCacheMetrics.refreshQueuesServed++;
        return threads;
    } catch (error) {
        console.error('[openerCache] ❌ Error getting refresh queue:', error);
        return [];
    }
}

/**
 * Invalidate guild opener config cache
 * @param {string} guildId - Guild ID
 */
function invalidateGuildOpenerConfig(guildId) {
    guildOpenerConfigCache.delete(guildId);
}

/**
 * Invalidate thread data cache
 * @param {string} threadId - Thread ID
 */
function invalidateThreadData(threadId) {
    threadDataCache.delete(threadId);
}

/**
 * Invalidate refresh queue cache
 */
function invalidateRefreshQueue() {
    refreshQueueCache.delete('global');
}

/**
 * Batch invalidate thread caches
 * @param {Array} threadIds - Array of thread IDs
 */
function batchInvalidateThreads(threadIds) {
    threadIds.forEach(threadId => {
        threadDataCache.delete(threadId);
    });
    // Also invalidate refresh queue as it might be affected
    invalidateRefreshQueue();
}

/**
 * Get cache statistics with LRU cache details
 * @returns {Object} Cache statistics
 */
function getCacheStats() {
    return {
        guildConfig: guildOpenerConfigCache.getStats(),
        threadData: threadDataCache.getStats(),
        refreshQueue: refreshQueueCache.getStats(),
        totalMemoryUsage: {
            guildConfig: guildOpenerConfigCache.getStats().memoryUsage,
            threadData: threadDataCache.getStats().memoryUsage,
            refreshQueue: refreshQueueCache.getStats().memoryUsage
        }
    };
}

/**
 * Preload frequently accessed thread data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced parallel processing with comprehensive error handling
 * @param {Array} threadIds - Array of thread IDs to preload
 */
async function preloadThreadData(threadIds) {
    const startTime = Date.now();

    try {
        openerCacheMetrics.databaseQueries++;
        openerCacheMetrics.parallelOperations++;

        const threads = await optimizedFind("opener_threads", {
            threadId: { $in: threadIds }
        });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        threads.forEach(thread => {
            threadDataCache.set(thread.threadId, thread);
        });

        const duration = Date.now() - startTime;
        openerCacheMetrics.averageQueryTime =
            (openerCacheMetrics.averageQueryTime * (openerCacheMetrics.databaseQueries - 1) + duration) /
            openerCacheMetrics.databaseQueries;

        if (openerCacheMetrics.verboseLogging || duration > 200) {
            console.log(`[openerCache] ✅ Preloaded ${threads.length} threads into cache in ${duration}ms`);
        }
    } catch (error) {
        console.error('[openerCache] ❌ Error preloading thread data:', error);
    }
}

/**
 * Batch get multiple thread data with parallel processing (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing with Promise.allSettled for comprehensive error recovery
 * @param {Array} threadIds - Array of thread IDs to fetch
 * @returns {Promise<Array>} Array of thread data (null for failed fetches)
 */
async function batchGetThreadData(threadIds) {
    const startTime = Date.now();
    openerCacheMetrics.parallelOperations++;

    try {
        // OPTIMIZED: Use Promise.allSettled for comprehensive error handling
        const threadResults = await Promise.allSettled(
            threadIds.map(threadId => getCachedThreadData(threadId))
        );

        const threads = threadResults.map((result, index) => {
            if (result.status === 'fulfilled') {
                return result.value;
            } else {
                openerCacheMetrics.partialFailures++;
                if (openerCacheMetrics.verboseLogging) {
                    console.warn(`[openerCache] ⚠️  Failed to fetch thread ${threadIds[index]}: ${result.reason}`);
                }
                return null;
            }
        });

        const duration = Date.now() - startTime;
        const successCount = threads.filter(thread => thread !== null).length;
        const failureCount = threads.length - successCount;

        if (openerCacheMetrics.verboseLogging || failureCount > 0) {
            console.log(`[openerCache] ✅ Batch fetched ${successCount}/${threads.length} threads (${failureCount} failures) in ${duration}ms`);
        }

        return threads;
    } catch (error) {
        console.error('[openerCache] ❌ Error in batch get thread data:', error);
        return threadIds.map(() => null);
    }
}

/**
 * Get thread statistics with caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Aggregation pipeline with intelligent caching
 * @param {string} guildId - Guild ID (null for global)
 * @returns {Promise<Object>} Thread statistics
 */
async function getCachedThreadStats(guildId = null) {
    const startTime = Date.now();
    const cacheKey = `thread_stats_${guildId || 'global'}`;

    // Check cache first
    const cached = threadStatsCache.get(cacheKey);
    if (cached) {
        openerCacheMetrics.cacheHits++;
        if (openerCacheMetrics.verboseLogging) {
            console.log(`[openerCache] ⚡ Thread stats cache hit for ${guildId || 'global'} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    openerCacheMetrics.cacheMisses++;
    openerCacheMetrics.databaseQueries++;

    try {
        // OPTIMIZED: Use aggregation pipeline for efficient statistics
        const pipeline = [
            {
                $match: {
                    ...(guildId && { guildId: guildId })
                }
            },
            {
                $group: {
                    _id: null,
                    totalThreads: { $sum: 1 },
                    activeThreads: {
                        $sum: {
                            $cond: [{ $gte: ["$refreshAt", new Date(Date.now() - 24 * 60 * 60 * 1000)] }, 1, 0]
                        }
                    },
                    avgRefreshInterval: { $avg: "$refreshInterval" },
                    latestActivity: { $max: "$lastOpened" }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalThreads: 1,
                    activeThreads: 1,
                    avgRefreshInterval: { $round: ["$avgRefreshInterval", 2] },
                    latestActivity: 1
                }
            }
        ];

        const statsResult = await optimizedAggregate('opener_threads', pipeline);
        const stats = statsResult.length > 0 ? statsResult[0] : {
            totalThreads: 0,
            activeThreads: 0,
            avgRefreshInterval: 0,
            latestActivity: null
        };

        // Cache the result
        threadStatsCache.set(cacheKey, stats);

        const duration = Date.now() - startTime;
        if (openerCacheMetrics.verboseLogging || duration > 150) {
            console.log(`[openerCache] ✅ Thread stats calculated for ${guildId || 'global'}: ${duration}ms - cached for future access`);
        }

        return stats;
    } catch (error) {
        console.error(`[openerCache] ❌ Error getting thread stats for ${guildId || 'global'}:`, error);
        return {
            totalThreads: 0,
            activeThreads: 0,
            avgRefreshInterval: 0,
            latestActivity: null
        };
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = openerCacheMetrics.cacheHits + openerCacheMetrics.cacheMisses > 0 ?
        (openerCacheMetrics.cacheHits / (openerCacheMetrics.cacheHits + openerCacheMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: openerCacheMetrics.cacheHits,
            cacheMisses: openerCacheMetrics.cacheMisses,
            databaseQueries: openerCacheMetrics.databaseQueries,
            averageQueryTime: `${openerCacheMetrics.averageQueryTime.toFixed(2)}ms`,
            configsServed: openerCacheMetrics.configsServed,
            threadsServed: openerCacheMetrics.threadsServed,
            refreshQueuesServed: openerCacheMetrics.refreshQueuesServed,
            parallelOperations: openerCacheMetrics.parallelOperations,
            partialFailures: openerCacheMetrics.partialFailures,
            lastOptimization: new Date(openerCacheMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildOpenerConfig: guildOpenerConfigCache.getStats(),
            threadData: threadDataCache.getStats(),
            refreshQueue: refreshQueueCache.getStats(),
            threadStats: threadStatsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildOpenerConfig: guildOpenerConfigCache.getStats().memoryUsage,
            threadData: threadDataCache.getStats().memoryUsage,
            refreshQueue: refreshQueueCache.getStats().memoryUsage,
            threadStats: threadStatsCache.getStats().memoryUsage,
            total: guildOpenerConfigCache.getStats().memoryUsage +
                   threadDataCache.getStats().memoryUsage +
                   refreshQueueCache.getStats().memoryUsage +
                   threadStatsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, openerCacheMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 150) {
        recommendations.push('Opener operations are slow - investigate database performance');
    }

    if (metrics.partialFailures > metrics.parallelOperations * 0.1) {
        recommendations.push('High partial failure rate - investigate system reliability');
    }

    if (metrics.parallelOperations < metrics.databaseQueries * 0.3) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (metrics.threadsServed > metrics.configsServed * 10) {
        recommendations.push('High thread to config ratio - consider thread-specific optimizations');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    openerCacheMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[openerCache] 📊 Performance Report:`);
    console.log(`[openerCache]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[openerCache]   Configs Served: ${stats.performance.configsServed}`);
    console.log(`[openerCache]   Threads Served: ${stats.performance.threadsServed}`);
    console.log(`[openerCache]   Refresh Queues Served: ${stats.performance.refreshQueuesServed}`);
    console.log(`[openerCache]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[openerCache]   Partial Failures: ${stats.performance.partialFailures}`);
    console.log(`[openerCache]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[openerCache]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[openerCache]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[openerCache] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all opener caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllOpenerCaches() {
    guildOpenerConfigCache.clear();
    threadDataCache.clear();
    refreshQueueCache.clear();
    threadStatsCache.clear();

    console.log('[openerCache] 🗑️ Cleared all opener caches');
}

/**
 * Batch invalidate multiple guild caches (Enterprise-Grade Optimization)
 * OPTIMIZED: Efficient batch cache invalidation for bulk operations
 * @param {Array} guildIds - Array of guild IDs to invalidate
 */
function batchInvalidateGuildCaches(guildIds) {
    let totalInvalidated = 0;

    guildIds.forEach(guildId => {
        // Invalidate guild config
        if (guildOpenerConfigCache.delete(guildId)) totalInvalidated++;

        // Invalidate thread stats
        if (threadStatsCache.delete(`thread_stats_${guildId}`)) totalInvalidated++;
    });

    if (openerCacheMetrics.verboseLogging) {
        console.log(`[openerCache] 🗑️ Batch invalidated ${totalInvalidated} cache entries for ${guildIds.length} guilds`);
    }
}

/**
 * Warm up caches with frequently accessed data
 */
async function warmupCaches() {
    try {
        // Get all active threads for preloading
        const activeThreads = await optimizedFind("opener_threads", {
            refreshAt: { $gte: Date.now() - (24 * 60 * 60 * 1000) } // Last 24 hours
        }, { limit: 100 });

        if (activeThreads.length > 0) {
            await preloadThreadData(activeThreads.map(t => t.threadId));
        }

        console.log('[openerCache] Cache warmup completed');
    } catch (error) {
        console.error('[openerCache] Error during cache warmup:', error);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, openerCacheMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getCachedGuildOpenerConfig,
    getCachedThreadData,
    getCachedRefreshQueue,
    invalidateGuildOpenerConfig,
    invalidateThreadData,
    invalidateRefreshQueue,
    batchInvalidateThreads,
    preloadThreadData,
    warmupCaches,

    // Enhanced optimization functions
    batchGetThreadData,
    getCachedThreadStats,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllOpenerCaches,
    batchInvalidateGuildCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...openerCacheMetrics })
};
