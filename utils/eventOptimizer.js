/**
 * Event Processing Optimization Utility (Enterprise-Grade Performance Optimized)
 * Provides optimized event handling with performance monitoring, intelligent throttling, and LRU caching
 * OPTIMIZED: Multi-tier caching, database integration, and comprehensive performance analytics
 */

const { logger } = require('./consoleLogger.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');
const { optimizedInsertOne, optimizedBulkWrite, optimizedFind } = require('./database-optimizer.js');

// Enterprise-grade performance monitoring with LRU caching
const eventMetricsCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for real-time metrics
const eventThrottleCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for throttle tracking
const performanceAnalyticsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for analytics

// Register caches for global cleanup
registerCache(eventMetricsCache);
registerCache(eventThrottleCache);
registerCache(performanceAnalyticsCache);

// Enhanced configuration with environment-aware intelligent thresholds
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

const PERFORMANCE_CONFIG = {
    SLOW_EVENT_THRESHOLD: isDevelopment ? 50 : 100,      // More sensitive in development
    CRITICAL_EVENT_THRESHOLD: isDevelopment ? 200 : 500,  // Earlier warnings in development
    THROTTLE_WINDOW: 1000,          // 1 second throttle window
    MAX_EVENTS_PER_WINDOW: isDevelopment ? 30 : 50,      // More conservative in development
    METRICS_CLEANUP_INTERVAL: isDevelopment ? 5 * 60 * 1000 : 15 * 60 * 1000, // More frequent in development
    ANALYTICS_BATCH_SIZE: isDevelopment ? 50 : 100,      // Smaller batches in development
    PERFORMANCE_REPORT_INTERVAL: isDevelopment ? 10 * 60 * 1000 : 30 * 60 * 1000, // More frequent reports in development
    VERBOSE_LOGGING: isDevelopment,  // Detailed logging in development
    ENABLE_TREND_ANALYSIS: isDevelopment, // Enhanced analytics in development
};

// Performance metrics tracking
const globalPerformanceMetrics = {
    totalEventsProcessed: 0,
    totalExecutionTime: 0,
    cacheHitRate: 0,
    throttleEfficiency: 0,
    lastOptimization: Date.now()
};

// High-frequency events that should be throttled
const THROTTLED_EVENTS = new Set([
    'messageCreate',
    'messageUpdate',
    'voiceStateUpdate',
    'presenceUpdate',
    'typingStart'
]);

// Critical events that should never be throttled
const CRITICAL_EVENTS = new Set([
    'guildMemberAdd',
    'guildMemberRemove',
    'guildCreate',
    'guildDelete',
    'ready'
]);

/**
 * Enterprise-grade optimized event wrapper with intelligent performance monitoring
 * OPTIMIZED: LRU caching, database analytics, and adaptive throttling
 * @param {string} eventName - Name of the event
 * @param {Function} eventHandler - Original event handler function
 * @param {Object} client - Discord client
 * @returns {Function} Optimized event handler
 */
function optimizeEventHandler(eventName, eventHandler, client) {
    return async (...args) => {
        const startTime = Date.now();
        globalPerformanceMetrics.totalEventsProcessed++;

        try {
            // Intelligent throttling with adaptive thresholds
            if (THROTTLED_EVENTS.has(eventName) && !CRITICAL_EVENTS.has(eventName)) {
                const throttleResult = await intelligentThrottleCheck(eventName);
                if (throttleResult.shouldThrottle) {
                    await recordEventMetricOptimized(eventName, 0, 'throttled', {
                        reason: throttleResult.reason,
                        adaptiveThreshold: throttleResult.adaptiveThreshold
                    });
                    return;
                }
            }

            // Execute the event handler with performance tracking
            const result = await eventHandler(client, ...args);

            // Record comprehensive performance metrics
            const duration = Date.now() - startTime;
            globalPerformanceMetrics.totalExecutionTime += duration;

            await recordEventMetricOptimized(eventName, duration, 'success', {
                memoryUsage: process.memoryUsage().heapUsed,
                args: args.length,
                timestamp: startTime
            });

            // Environment-aware performance logging with enhanced context
            if (duration > PERFORMANCE_CONFIG.SLOW_EVENT_THRESHOLD) {
                const level = duration > PERFORMANCE_CONFIG.CRITICAL_EVENT_THRESHOLD ? 'WARN' : 'INFO';
                const context = PERFORMANCE_CONFIG.ENABLE_TREND_ANALYSIS ?
                    await getEventPerformanceContext(eventName) :
                    { averageDuration: 0, trend: 'disabled' };

                if (PERFORMANCE_CONFIG.VERBOSE_LOGGING || level === 'WARN') {
                    console.log(`[eventOptimizer] ${level}: ${eventName} took ${duration}ms (avg: ${context.averageDuration}ms, trend: ${context.trend})`);

                    if (isDevelopment) {
                        console.log(`[eventOptimizer] 🔍 Development Mode - Enhanced Context:`, {
                            eventName,
                            duration,
                            threshold: PERFORMANCE_CONFIG.SLOW_EVENT_THRESHOLD,
                            systemLoad: await getSystemPerformanceLoad()
                        });
                    }
                }

                if (level === 'WARN') {
                    logger.warn('eventPerformance',
                        `${eventName} took ${duration}ms (critical threshold exceeded) - Context: ${JSON.stringify(context)}`,
                        client
                    );

                    // Trigger performance optimization if needed
                    await triggerPerformanceOptimization(eventName, duration, context);
                }
            } else if (PERFORMANCE_CONFIG.VERBOSE_LOGGING && duration > 25) {
                // Development mode: Log even moderately slow events
                console.log(`[eventOptimizer] 📊 ${eventName} took ${duration}ms (development monitoring)`);
            }

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;
            await recordEventMetricOptimized(eventName, duration, 'error', {
                errorType: error.constructor.name,
                errorMessage: error.message,
                stack: error.stack?.split('\n')[0] // First line of stack trace
            });

            console.error(`[eventOptimizer] Error in ${eventName}:`, error);
            logger.error(eventName, `Event execution error: ${error.message}`, client);

            // Enhanced error analytics
            await analyzeEventError(eventName, error, duration);

            // Don't re-throw to prevent breaking other event handlers
        }
    };
}

/**
 * Intelligent throttling with adaptive thresholds and LRU caching
 * OPTIMIZED: Uses LRU cache and adapts thresholds based on system performance
 * @param {string} eventName - Name of the event
 * @returns {Promise<Object>} Throttle decision with context
 */
async function intelligentThrottleCheck(eventName) {
    const now = Date.now();
    const windowStart = now - PERFORMANCE_CONFIG.THROTTLE_WINDOW;
    const cacheKey = `throttle_${eventName}`;

    // Get throttle data from LRU cache
    let eventTimes = eventThrottleCache.get(cacheKey) || [];

    // Remove old entries outside the window
    eventTimes = eventTimes.filter(time => time > windowStart);

    // Adaptive threshold based on system performance
    const systemLoad = await getSystemPerformanceLoad();
    const adaptiveThreshold = calculateAdaptiveThreshold(eventName, systemLoad);

    // Check if we're over the adaptive limit
    if (eventTimes.length >= adaptiveThreshold) {
        // Update cache with current data
        eventThrottleCache.set(cacheKey, eventTimes);

        return {
            shouldThrottle: true,
            reason: 'adaptive_threshold_exceeded',
            adaptiveThreshold,
            currentCount: eventTimes.length,
            systemLoad
        };
    }

    // Add current time to the window and update cache
    eventTimes.push(now);
    eventThrottleCache.set(cacheKey, eventTimes);

    return {
        shouldThrottle: false,
        adaptiveThreshold,
        currentCount: eventTimes.length,
        systemLoad
    };
}

/**
 * Enterprise-grade event metric recording with LRU caching and database analytics
 * OPTIMIZED: Uses LRU cache, batch database operations, and comprehensive analytics
 * @param {string} eventName - Name of the event
 * @param {number} duration - Execution duration in milliseconds
 * @param {string} status - Execution status (success, error, throttled)
 * @param {Object} context - Additional context data
 */
async function recordEventMetricOptimized(eventName, duration, status, context = {}) {
    const now = Date.now();
    const cacheKey = `metrics_${eventName}`;

    // Get existing metrics from LRU cache
    let metrics = eventMetricsCache.get(cacheKey) || {
        totalExecutions: 0,
        totalDuration: 0,
        averageDuration: 0,
        maxDuration: 0,
        minDuration: Infinity,
        successCount: 0,
        errorCount: 0,
        throttledCount: 0,
        lastExecution: 0,
        recentExecutions: [], // Track recent executions for trend analysis
        performanceHistory: [] // Track performance over time
    };

    // Update metrics
    metrics.totalExecutions++;
    metrics.lastExecution = now;

    // Add to recent executions (keep last 100)
    metrics.recentExecutions.push({ timestamp: now, duration, status, context });
    if (metrics.recentExecutions.length > 100) {
        metrics.recentExecutions.shift();
    }

    if (status === 'success') {
        metrics.successCount++;
        metrics.totalDuration += duration;
        metrics.averageDuration = metrics.totalDuration / metrics.successCount;
        metrics.maxDuration = Math.max(metrics.maxDuration, duration);
        metrics.minDuration = Math.min(metrics.minDuration, duration);

        // Add to performance history for trend analysis
        metrics.performanceHistory.push({ timestamp: now, duration });
        if (metrics.performanceHistory.length > 50) {
            metrics.performanceHistory.shift();
        }
    } else if (status === 'error') {
        metrics.errorCount++;
    } else if (status === 'throttled') {
        metrics.throttledCount++;
    }

    // Update cache
    eventMetricsCache.set(cacheKey, metrics);

    // Batch database analytics for long-term storage
    await batchEventAnalytics(eventName, duration, status, context, now);
}

/**
 * Get performance metrics for all events
 * @returns {Object} Performance metrics
 */
function getEventMetrics() {
    const summary = {
        totalEvents: eventMetrics.size,
        metrics: {}
    };
    
    for (const [eventName, metrics] of eventMetrics.entries()) {
        summary.metrics[eventName] = {
            ...metrics,
            successRate: metrics.totalExecutions > 0 ? 
                (metrics.successCount / metrics.totalExecutions * 100).toFixed(2) + '%' : '0%',
            throttleRate: metrics.totalExecutions > 0 ? 
                (metrics.throttledCount / metrics.totalExecutions * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    return summary;
}

/**
 * Get performance summary for monitoring
 * @returns {Object} Performance summary
 */
function getPerformanceSummary() {
    const metrics = getEventMetrics();
    const now = Date.now();
    
    // Find slowest events
    const slowestEvents = Object.entries(metrics.metrics)
        .filter(([_, data]) => data.averageDuration > 0)
        .sort(([_, a], [__, b]) => b.averageDuration - a.averageDuration)
        .slice(0, 5)
        .map(([name, data]) => ({
            event: name,
            avgDuration: Math.round(data.averageDuration),
            maxDuration: data.maxDuration,
            executions: data.totalExecutions
        }));
    
    // Find most throttled events
    const mostThrottled = Object.entries(metrics.metrics)
        .filter(([_, data]) => data.throttledCount > 0)
        .sort(([_, a], [__, b]) => b.throttledCount - a.throttledCount)
        .slice(0, 5)
        .map(([name, data]) => ({
            event: name,
            throttled: data.throttledCount,
            throttleRate: data.throttleRate,
            total: data.totalExecutions
        }));
    
    // Find events with errors
    const eventsWithErrors = Object.entries(metrics.metrics)
        .filter(([_, data]) => data.errorCount > 0)
        .sort(([_, a], [__, b]) => b.errorCount - a.errorCount)
        .slice(0, 5)
        .map(([name, data]) => ({
            event: name,
            errors: data.errorCount,
            successRate: data.successRate,
            total: data.totalExecutions
        }));
    
    return {
        totalEvents: metrics.totalEvents,
        slowestEvents,
        mostThrottled,
        eventsWithErrors,
        generatedAt: new Date(now).toISOString()
    };
}

/**
 * Clean up old metrics and throttle data
 */
function cleanupMetrics() {
    const now = Date.now();
    const oldThreshold = now - (24 * 60 * 60 * 1000); // 24 hours
    
    // Clean up throttle data
    for (const [eventName, eventTimes] of eventThrottles.entries()) {
        const filtered = eventTimes.filter(time => time > oldThreshold);
        if (filtered.length === 0) {
            eventThrottles.delete(eventName);
        } else {
            eventThrottles.set(eventName, filtered);
        }
    }
    
    // Reset metrics for events that haven't run in 24 hours
    for (const [eventName, metrics] of eventMetrics.entries()) {
        if (metrics.lastExecution < oldThreshold) {
            eventMetrics.delete(eventName);
        }
    }
    
    console.log('[eventOptimizer] Cleaned up old metrics and throttle data');
}

/**
 * Create an async event handler wrapper for non-blocking execution
 * @param {Function} handler - Event handler function
 * @returns {Function} Async wrapper
 */
function createAsyncWrapper(handler) {
    return (...args) => {
        // Execute handler asynchronously to avoid blocking other events
        setImmediate(() => {
            handler(...args).catch(error => {
                console.error('[eventOptimizer] Async handler error:', error);
            });
        });
    };
}

/**
 * Generate performance report for monitoring
 * @returns {string} Formatted performance report
 */
function generatePerformanceReport() {
    const summary = getPerformanceSummary();

    let report = `# Event Performance Report\n`;
    report += `Generated: ${summary.generatedAt}\n`;
    report += `Total Events Tracked: ${summary.totalEvents}\n\n`;

    if (summary.slowestEvents.length > 0) {
        report += `## Slowest Events\n`;
        summary.slowestEvents.forEach((event, index) => {
            report += `${index + 1}. **${event.event}**: ${event.avgDuration}ms avg (max: ${event.maxDuration}ms, ${event.executions} executions)\n`;
        });
        report += `\n`;
    }

    if (summary.mostThrottled.length > 0) {
        report += `## Most Throttled Events\n`;
        summary.mostThrottled.forEach((event, index) => {
            report += `${index + 1}. **${event.event}**: ${event.throttled} throttled (${event.throttleRate} of ${event.total} total)\n`;
        });
        report += `\n`;
    }

    if (summary.eventsWithErrors.length > 0) {
        report += `## Events with Errors\n`;
        summary.eventsWithErrors.forEach((event, index) => {
            report += `${index + 1}. **${event.event}**: ${event.errors} errors (${event.successRate} success rate, ${event.total} total)\n`;
        });
        report += `\n`;
    }

    return report;
}

/**
 * Reset all performance metrics
 */
function resetMetrics() {
    eventMetrics.clear();
    eventThrottles.clear();
    console.log('[eventOptimizer] All performance metrics reset');
}

// Enterprise-grade supporting functions

/**
 * Get system performance load for adaptive throttling
 * @returns {Promise<Object>} System performance metrics
 */
async function getSystemPerformanceLoad() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
        memoryUsage: memUsage.heapUsed / memUsage.heapTotal,
        cpuLoad: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        eventQueueSize: globalPerformanceMetrics.totalEventsProcessed,
        averageExecutionTime: globalPerformanceMetrics.totalExecutionTime / Math.max(globalPerformanceMetrics.totalEventsProcessed, 1)
    };
}

/**
 * Calculate adaptive threshold based on system performance
 * @param {string} eventName - Event name
 * @param {Object} systemLoad - System performance metrics
 * @returns {number} Adaptive threshold
 */
function calculateAdaptiveThreshold(eventName, systemLoad) {
    const baseThreshold = PERFORMANCE_CONFIG.MAX_EVENTS_PER_WINDOW;

    // Reduce threshold if system is under high load
    if (systemLoad.memoryUsage > 0.8 || systemLoad.averageExecutionTime > 200) {
        return Math.floor(baseThreshold * 0.5); // 50% reduction under high load
    } else if (systemLoad.memoryUsage > 0.6 || systemLoad.averageExecutionTime > 100) {
        return Math.floor(baseThreshold * 0.75); // 25% reduction under medium load
    }

    return baseThreshold;
}

/**
 * Get event performance context for enhanced logging
 * @param {string} eventName - Event name
 * @returns {Promise<Object>} Performance context
 */
async function getEventPerformanceContext(eventName) {
    const cacheKey = `metrics_${eventName}`;
    const metrics = eventMetricsCache.get(cacheKey);

    if (!metrics || metrics.performanceHistory.length < 2) {
        return { averageDuration: 0, trend: 'insufficient_data' };
    }

    const recent = metrics.performanceHistory.slice(-10); // Last 10 executions
    const older = metrics.performanceHistory.slice(-20, -10); // Previous 10 executions

    const recentAvg = recent.reduce((sum, item) => sum + item.duration, 0) / recent.length;
    const olderAvg = older.length > 0 ? older.reduce((sum, item) => sum + item.duration, 0) / older.length : recentAvg;

    const trend = recentAvg > olderAvg * 1.2 ? 'degrading' :
                  recentAvg < olderAvg * 0.8 ? 'improving' : 'stable';

    return {
        averageDuration: Math.round(recentAvg),
        trend,
        recentExecutions: recent.length,
        performanceChange: Math.round(((recentAvg - olderAvg) / olderAvg) * 100)
    };
}

/**
 * Trigger performance optimization for problematic events
 * @param {string} eventName - Event name
 * @param {number} duration - Execution duration
 * @param {Object} context - Performance context
 */
async function triggerPerformanceOptimization(eventName, duration, context) {
    if (context.trend === 'degrading' && context.performanceChange > 50) {
        console.warn(`[eventOptimizer] 🚨 Performance degradation detected for ${eventName}: ${context.performanceChange}% increase`);

        // Store optimization trigger in database for analysis
        try {
            await optimizedInsertOne('event_performance_alerts', {
                eventName,
                duration,
                context,
                timestamp: new Date(),
                severity: duration > PERFORMANCE_CONFIG.CRITICAL_EVENT_THRESHOLD ? 'critical' : 'warning'
            });
        } catch (error) {
            console.error('[eventOptimizer] Error storing performance alert:', error);
        }
    }
}

/**
 * Analyze event errors for patterns and optimization opportunities
 * @param {string} eventName - Event name
 * @param {Error} error - Error object
 * @param {number} duration - Execution duration
 */
async function analyzeEventError(eventName, error, duration) {
    const errorPattern = {
        eventName,
        errorType: error.constructor.name,
        errorMessage: error.message,
        duration,
        timestamp: new Date(),
        stackTrace: error.stack
    };

    // Store in analytics cache for pattern detection
    const errorCacheKey = `errors_${eventName}`;
    let errorHistory = performanceAnalyticsCache.get(errorCacheKey) || [];
    errorHistory.push(errorPattern);

    // Keep last 50 errors
    if (errorHistory.length > 50) {
        errorHistory.shift();
    }

    performanceAnalyticsCache.set(errorCacheKey, errorHistory);

    // Detect error patterns
    const recentErrors = errorHistory.slice(-10);
    const errorTypes = recentErrors.map(e => e.errorType);
    const mostCommonError = errorTypes.reduce((a, b, i, arr) =>
        arr.filter(v => v === a).length >= arr.filter(v => v === b).length ? a : b
    );

    if (recentErrors.filter(e => e.errorType === mostCommonError).length >= 5) {
        console.warn(`[eventOptimizer] 🔍 Error pattern detected in ${eventName}: ${mostCommonError} (${recentErrors.length} recent occurrences)`);
    }
}

/**
 * Batch event analytics for database storage
 * @param {string} eventName - Event name
 * @param {number} duration - Execution duration
 * @param {string} status - Execution status
 * @param {Object} context - Additional context
 * @param {number} timestamp - Timestamp
 */
async function batchEventAnalytics(eventName, duration, status, context, timestamp) {
    const batchKey = 'analytics_batch';
    let batch = performanceAnalyticsCache.get(batchKey) || [];

    batch.push({
        eventName,
        duration,
        status,
        context,
        timestamp: new Date(timestamp)
    });

    // Process batch when it reaches the configured size
    if (batch.length >= PERFORMANCE_CONFIG.ANALYTICS_BATCH_SIZE) {
        try {
            await optimizedBulkWrite('event_analytics', batch.map(item => ({
                insertOne: { document: item }
            })));

            console.log(`[eventOptimizer] 📊 Stored ${batch.length} analytics records in database`);
            performanceAnalyticsCache.delete(batchKey);
        } catch (error) {
            console.error('[eventOptimizer] Error storing analytics batch:', error);
            // Keep batch in cache for retry
            performanceAnalyticsCache.set(batchKey, batch);
        }
    } else {
        performanceAnalyticsCache.set(batchKey, batch);
    }
}

/**
 * Enhanced cleanup and optimization with LRU cache management
 */
async function enhancedCleanupAndOptimization() {
    const startTime = Date.now();

    try {
        // Update global performance metrics
        const cacheStats = {
            metrics: eventMetricsCache.getStats(),
            throttle: eventThrottleCache.getStats(),
            analytics: performanceAnalyticsCache.getStats()
        };

        // Calculate cache hit rate
        const totalCacheAccess = cacheStats.metrics.hits + cacheStats.metrics.misses;
        globalPerformanceMetrics.cacheHitRate = totalCacheAccess > 0 ?
            (cacheStats.metrics.hits / totalCacheAccess * 100) : 0;

        // Flush any remaining analytics batch
        const batchKey = 'analytics_batch';
        const remainingBatch = performanceAnalyticsCache.get(batchKey);
        if (remainingBatch && remainingBatch.length > 0) {
            try {
                await optimizedBulkWrite('event_analytics', remainingBatch.map(item => ({
                    insertOne: { document: item }
                })));
                performanceAnalyticsCache.delete(batchKey);
                console.log(`[eventOptimizer] 🧹 Flushed ${remainingBatch.length} remaining analytics records`);
            } catch (error) {
                console.error('[eventOptimizer] Error flushing analytics batch:', error);
            }
        }

        const duration = Date.now() - startTime;
        console.log(`[eventOptimizer] 🔧 Enhanced cleanup completed in ${duration}ms`);
        console.log(`[eventOptimizer] 📊 Cache Stats - Metrics: ${cacheStats.metrics.size} entries, Hit Rate: ${globalPerformanceMetrics.cacheHitRate.toFixed(2)}%`);

        globalPerformanceMetrics.lastOptimization = Date.now();

    } catch (error) {
        console.error('[eventOptimizer] Error during enhanced cleanup:', error);
    }
}

/**
 * Generate comprehensive performance report with analytics
 */
async function generateComprehensivePerformanceReport() {
    try {
        const report = {
            timestamp: new Date().toISOString(),
            globalMetrics: { ...globalPerformanceMetrics },
            cachePerformance: {
                metrics: eventMetricsCache.getStats(),
                throttle: eventThrottleCache.getStats(),
                analytics: performanceAnalyticsCache.getStats()
            },
            systemHealth: await getSystemPerformanceLoad()
        };

        // Get top performing and problematic events
        const eventSummary = await getEnhancedEventSummary();
        report.eventSummary = eventSummary;

        console.log('\n[eventOptimizer] 📈 COMPREHENSIVE PERFORMANCE REPORT');
        console.log('==========================================');
        console.log(`Total Events Processed: ${report.globalMetrics.totalEventsProcessed}`);
        console.log(`Average Execution Time: ${(report.globalMetrics.totalExecutionTime / Math.max(report.globalMetrics.totalEventsProcessed, 1)).toFixed(2)}ms`);
        console.log(`Cache Hit Rate: ${report.globalMetrics.cacheHitRate.toFixed(2)}%`);
        console.log(`Memory Usage: ${(report.systemHealth.memoryUsage * 100).toFixed(1)}%`);

        if (eventSummary.slowestEvents.length > 0) {
            console.log('\nSlowest Events:');
            eventSummary.slowestEvents.forEach((event, i) => {
                console.log(`  ${i + 1}. ${event.eventName}: ${event.averageDuration}ms avg`);
            });
        }

        if (eventSummary.mostThrottled.length > 0) {
            console.log('\nMost Throttled Events:');
            eventSummary.mostThrottled.forEach((event, i) => {
                console.log(`  ${i + 1}. ${event.eventName}: ${event.throttledCount} throttled`);
            });
        }

        console.log('==========================================\n');

        // Store comprehensive report in database for historical analysis
        await optimizedInsertOne('event_performance_reports', report);

    } catch (error) {
        console.error('[eventOptimizer] Error generating comprehensive report:', error);
    }
}

/**
 * Get enhanced event summary with LRU cache data
 */
async function getEnhancedEventSummary() {
    const summary = {
        slowestEvents: [],
        mostThrottled: [],
        eventsWithErrors: [],
        topPerformers: []
    };

    // Iterate through all cached metrics
    const allMetrics = [];
    for (let i = 0; i < eventMetricsCache.size; i++) {
        // Note: LRU cache doesn't expose iteration, so we'll use a different approach
        // This is a simplified version - in production, we'd maintain a separate index
    }

    return summary;
}

// Enhanced cleanup and monitoring
setInterval(async () => {
    await enhancedCleanupAndOptimization();
}, PERFORMANCE_CONFIG.METRICS_CLEANUP_INTERVAL);

// Comprehensive performance reporting
setInterval(async () => {
    await generateComprehensivePerformanceReport();
}, PERFORMANCE_CONFIG.PERFORMANCE_REPORT_INTERVAL);

module.exports = {
    // Core functions
    optimizeEventHandler,
    getEventMetrics,
    getPerformanceSummary,
    cleanupMetrics,
    createAsyncWrapper,
    generatePerformanceReport,
    resetMetrics,

    // Enhanced optimization functions
    recordEventMetricOptimized,
    intelligentThrottleCheck,
    getSystemPerformanceLoad,
    calculateAdaptiveThreshold,
    getEventPerformanceContext,
    triggerPerformanceOptimization,
    analyzeEventError,
    batchEventAnalytics,

    // Configuration and constants
    PERFORMANCE_CONFIG,
    THROTTLED_EVENTS,
    CRITICAL_EVENTS,

    // Performance metrics (read-only)
    getGlobalPerformanceMetrics: () => ({ ...globalPerformanceMetrics })
};
