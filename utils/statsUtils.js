const { optimizedAggregate, optimizedFind } = require('./database-optimizer.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * Statistics Utilities (Enterprise-Grade Performance Optimized)
 * Optimizes ranking calculations and guild configuration lookups with comprehensive caching
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const statsUtilsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    rankingsCalculated: 0,
    guildConfigsServed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const guildRankingCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for guild rankings
const globalRankingCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for global rankings
const guildConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configurations

// Register caches for global cleanup
registerCache(guildRankingCache);
registerCache(globalRankingCache);
registerCache(guildConfigCache);

/**
 * Format milliseconds into human readable time (2d14h32m32s)
 */
function formatDuration(milliseconds) {
    if (!milliseconds || milliseconds <= 0) return '0s';
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    const remainingHours = hours % 24;
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    
    let result = '';
    if (days > 0) result += `${days}d`;
    if (remainingHours > 0) result += `${remainingHours}h`;
    if (remainingMinutes > 0) result += `${remainingMinutes}m`;
    if (remainingSeconds > 0) result += `${remainingSeconds}s`;
    
    return result || '0s';
}

/**
 * Get voice EXP guild ranking for a user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Ranking data with rank, total, and exp
 */
async function getVoiceExpGuildRank(guildId, userId) {
    const startTime = Date.now();
    const cacheKey = `voice_guild_${guildId}_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildRankingCache.get(cacheKey);
        if (cached) {
            statsUtilsMetrics.cacheHits++;
            if (statsUtilsMetrics.verboseLogging) {
                console.log(`[statsUtils] ⚡ Voice guild rank cache hit for ${userId} in ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        statsUtilsMetrics.cacheMisses++;
        statsUtilsMetrics.databaseQueries++;

        // Use aggregation for better performance
        const pipeline = [
            { $match: { guildId: guildId, "exp.voice.total": { $gt: 0 } } },
            { $project: { userId: 1, "exp.voice.total": 1 } },
            { $sort: { "exp.voice.total": -1 } }
        ];

        const members = await optimizedAggregate("member", pipeline);

        // Find user's position
        const userIndex = members.findIndex(member => member.userId === userId);

        const result = userIndex === -1 ?
            { rank: null, total: members.length, exp: 0 } :
            {
                rank: userIndex + 1,
                total: members.length,
                exp: members[userIndex].exp.voice.total
            };

        // Cache the result
        guildRankingCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        statsUtilsMetrics.averageQueryTime =
            (statsUtilsMetrics.averageQueryTime * (statsUtilsMetrics.databaseQueries - 1) + duration) /
            statsUtilsMetrics.databaseQueries;

        if (statsUtilsMetrics.verboseLogging || duration > 200) {
            console.log(`[statsUtils] ✅ Voice guild rank calculated for ${userId} in ${guildId}: ${duration}ms - cached for future access`);
        }

        statsUtilsMetrics.rankingsCalculated++;
        return result;
    } catch (error) {
        console.error('[statsUtils] ❌ Error getting voice EXP guild rank:', error);
        return { rank: null, total: 0, exp: 0 };
    }
}

/**
 * Get text EXP guild ranking for a user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Ranking data with rank, total, and exp
 */
async function getTextExpGuildRank(guildId, userId) {
    const startTime = Date.now();
    const cacheKey = `text_guild_${guildId}_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildRankingCache.get(cacheKey);
        if (cached) {
            statsUtilsMetrics.cacheHits++;
            if (statsUtilsMetrics.verboseLogging) {
                console.log(`[statsUtils] ⚡ Text guild rank cache hit for ${userId} in ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        statsUtilsMetrics.cacheMisses++;
        statsUtilsMetrics.databaseQueries++;

        // Use aggregation for better performance
        const pipeline = [
            { $match: { guildId: guildId, "exp.text.total": { $gt: 0 } } },
            { $project: { userId: 1, "exp.text.total": 1 } },
            { $sort: { "exp.text.total": -1 } }
        ];

        const members = await optimizedAggregate("member", pipeline);

        // Find user's position
        const userIndex = members.findIndex(member => member.userId === userId);

        const result = userIndex === -1 ?
            { rank: null, total: members.length, exp: 0 } :
            {
                rank: userIndex + 1,
                total: members.length,
                exp: members[userIndex].exp.text.total
            };

        // Cache the result
        guildRankingCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        statsUtilsMetrics.averageQueryTime =
            (statsUtilsMetrics.averageQueryTime * (statsUtilsMetrics.databaseQueries - 1) + duration) /
            statsUtilsMetrics.databaseQueries;

        if (statsUtilsMetrics.verboseLogging || duration > 200) {
            console.log(`[statsUtils] ✅ Text guild rank calculated for ${userId} in ${guildId}: ${duration}ms - cached for future access`);
        }

        statsUtilsMetrics.rankingsCalculated++;
        return result;
    } catch (error) {
        console.error('[statsUtils] ❌ Error getting text EXP guild rank:', error);
        return { rank: null, total: 0, exp: 0 };
    }
}

/**
 * Get cached guild configurations for EXP rates (Enterprise-Grade Optimized)
 * OPTIMIZED: LRU caching for guild configurations to eliminate repeated database lookups
 * @param {string} expType - Type of EXP ('voice' or 'text')
 * @returns {Promise<Object>} Guild configurations with rates
 */
async function getCachedGuildConfigs(expType) {
    const startTime = Date.now();
    const cacheKey = `guild_configs_${expType}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = guildConfigCache.get(cacheKey);
        if (cached) {
            statsUtilsMetrics.cacheHits++;
            if (statsUtilsMetrics.verboseLogging) {
                console.log(`[statsUtils] ⚡ Guild configs cache hit for ${expType} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        statsUtilsMetrics.cacheMisses++;
        statsUtilsMetrics.databaseQueries++;

        // Get all guilds with EXP enabled
        const guilds = await optimizedFind("guilds", { "exp.enabled": true });
        const guildRates = {};

        guilds.forEach(guild => {
            const rate = expType === 'voice' ?
                (guild.exp?.voice?.expPerMin ?? 2) :
                (guild.exp?.text?.expPerMin ?? 1);
            guildRates[guild.id] = rate;
        });

        // Cache the result
        guildConfigCache.set(cacheKey, guildRates);

        const duration = Date.now() - startTime;
        if (statsUtilsMetrics.verboseLogging || duration > 100) {
            console.log(`[statsUtils] ✅ Guild configs fetched for ${expType}: ${duration}ms - cached for future access`);
        }

        statsUtilsMetrics.guildConfigsServed++;
        return guildRates;
    } catch (error) {
        console.error(`[statsUtils] ❌ Error getting guild configs for ${expType}:`, error);
        return {};
    }
}

/**
 * Get voice EXP global ranking for a user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and guild config caching
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Global ranking data
 */
async function getVoiceExpGlobalRank(userId) {
    const startTime = Date.now();
    const cacheKey = `voice_global_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = globalRankingCache.get(cacheKey);
        if (cached) {
            statsUtilsMetrics.cacheHits++;
            if (statsUtilsMetrics.verboseLogging) {
                console.log(`[statsUtils] ⚡ Voice global rank cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        statsUtilsMetrics.cacheMisses++;
        statsUtilsMetrics.databaseQueries++;
        statsUtilsMetrics.parallelOperations++;

        // OPTIMIZED: Use Promise.allSettled for parallel processing
        const [guildConfigsResult, membersResult] = await Promise.allSettled([
            getCachedGuildConfigs('voice'),
            optimizedFind("member", { "exp.voice.total": { $gt: 0 } })
        ]);

        if (guildConfigsResult.status === 'rejected' || membersResult.status === 'rejected') {
            statsUtilsMetrics.partialFailures++;
            console.warn('[statsUtils] ⚠️ Partial failure in voice global rank calculation');
        }

        const guildRates = guildConfigsResult.status === 'fulfilled' ? guildConfigsResult.value : {};
        const members = membersResult.status === 'fulfilled' ? membersResult.value : [];

        // Since EXP rates are now standardized, no normalization needed
        const sortedMembers = members.sort((a, b) => b.exp.voice.total - a.exp.voice.total);

        // Find user's position
        const userIndex = sortedMembers.findIndex(member => member.userId === userId);

        const result = userIndex === -1 ?
            { rank: null, total: sortedMembers.length, exp: 0 } :
            {
                rank: userIndex + 1,
                total: sortedMembers.length,
                exp: sortedMembers[userIndex].exp.voice.total
            };

        // Cache the result
        globalRankingCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        statsUtilsMetrics.averageQueryTime =
            (statsUtilsMetrics.averageQueryTime * (statsUtilsMetrics.databaseQueries - 1) + duration) /
            statsUtilsMetrics.databaseQueries;

        if (statsUtilsMetrics.verboseLogging || duration > 300) {
            console.log(`[statsUtils] ✅ Voice global rank calculated for ${userId}: ${duration}ms - cached for future access`);
        }

        statsUtilsMetrics.rankingsCalculated++;
        return result;
    } catch (error) {
        console.error('[statsUtils] ❌ Error getting voice EXP global rank:', error);
        return { rank: null, total: 0, exp: 0, rawExp: 0 };
    }
}

/**
 * Get text EXP global ranking for a user (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and guild config caching
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Global ranking data
 */
async function getTextExpGlobalRank(userId) {
    const startTime = Date.now();
    const cacheKey = `text_global_${userId}`;

    try {
        // OPTIMIZED: Check LRU cache first
        const cached = globalRankingCache.get(cacheKey);
        if (cached) {
            statsUtilsMetrics.cacheHits++;
            if (statsUtilsMetrics.verboseLogging) {
                console.log(`[statsUtils] ⚡ Text global rank cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        statsUtilsMetrics.cacheMisses++;
        statsUtilsMetrics.databaseQueries++;
        statsUtilsMetrics.parallelOperations++;

        // OPTIMIZED: Use Promise.allSettled for parallel processing
        const [guildConfigsResult, membersResult] = await Promise.allSettled([
            getCachedGuildConfigs('text'),
            optimizedFind("member", { "exp.text.total": { $gt: 0 } })
        ]);

        if (guildConfigsResult.status === 'rejected' || membersResult.status === 'rejected') {
            statsUtilsMetrics.partialFailures++;
            console.warn('[statsUtils] ⚠️ Partial failure in text global rank calculation');
        }

        const guildRates = guildConfigsResult.status === 'fulfilled' ? guildConfigsResult.value : {};
        const members = membersResult.status === 'fulfilled' ? membersResult.value : [];

        // Since EXP rates are now standardized, no normalization needed
        const sortedMembers = members.sort((a, b) => b.exp.text.total - a.exp.text.total);

        // Find user's position
        const userIndex = sortedMembers.findIndex(member => member.userId === userId);

        const result = userIndex === -1 ?
            { rank: null, total: sortedMembers.length, exp: 0 } :
            {
                rank: userIndex + 1,
                total: sortedMembers.length,
                exp: sortedMembers[userIndex].exp.text.total
            };

        // Cache the result
        globalRankingCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        statsUtilsMetrics.averageQueryTime =
            (statsUtilsMetrics.averageQueryTime * (statsUtilsMetrics.databaseQueries - 1) + duration) /
            statsUtilsMetrics.databaseQueries;

        if (statsUtilsMetrics.verboseLogging || duration > 300) {
            console.log(`[statsUtils] ✅ Text global rank calculated for ${userId}: ${duration}ms - cached for future access`);
        }

        statsUtilsMetrics.rankingsCalculated++;
        return result;
    } catch (error) {
        console.error('[statsUtils] ❌ Error getting text EXP global rank:', error);
        return { rank: null, total: 0, exp: 0, rawExp: 0 };
    }
}

/**
 * Calculate average messages per day for text stats
 */
function calculateAvgPerDay(messagesCounted, firstActiveDay) {
    if (!messagesCounted || !firstActiveDay) return 0;
    
    const firstDate = new Date(firstActiveDay);
    const today = new Date();
    const daysDiff = Math.max(1, Math.ceil((today - firstDate) / (1000 * 60 * 60 * 24)));
    
    return Math.round((messagesCounted / daysDiff) * 10) / 10; // Round to 1 decimal
}

/**
 * Get comprehensive statistics utilities performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive statistics utilities performance data
 */
function getStatsUtilsStats() {
    const cacheHitRate = statsUtilsMetrics.cacheHits + statsUtilsMetrics.cacheMisses > 0 ?
        (statsUtilsMetrics.cacheHits / (statsUtilsMetrics.cacheHits + statsUtilsMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: statsUtilsMetrics.cacheHits,
            cacheMisses: statsUtilsMetrics.cacheMisses,
            databaseQueries: statsUtilsMetrics.databaseQueries,
            averageQueryTime: `${statsUtilsMetrics.averageQueryTime.toFixed(2)}ms`,
            rankingsCalculated: statsUtilsMetrics.rankingsCalculated,
            guildConfigsServed: statsUtilsMetrics.guildConfigsServed,
            parallelOperations: statsUtilsMetrics.parallelOperations,
            partialFailures: statsUtilsMetrics.partialFailures,
            lastOptimization: new Date(statsUtilsMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            guildRanking: guildRankingCache.getStats(),
            globalRanking: globalRankingCache.getStats(),
            guildConfig: guildConfigCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            guildRanking: guildRankingCache.getStats().memoryUsage,
            globalRanking: globalRankingCache.getStats().memoryUsage,
            guildConfig: guildConfigCache.getStats().memoryUsage,
            total: guildRankingCache.getStats().memoryUsage +
                   globalRankingCache.getStats().memoryUsage +
                   guildConfigCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    statsUtilsMetrics.lastOptimization = Date.now();

    const stats = getStatsUtilsStats();
    if (statsUtilsMetrics.verboseLogging) {
        console.log(`[statsUtils] 📊 Performance Report:`);
        console.log(`[statsUtils]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[statsUtils]   Rankings Calculated: ${stats.performance.rankingsCalculated}`);
        console.log(`[statsUtils]   Guild Configs Served: ${stats.performance.guildConfigsServed}`);
        console.log(`[statsUtils]   Parallel Operations: ${stats.performance.parallelOperations}`);
        console.log(`[statsUtils]   Partial Failures: ${stats.performance.partialFailures}`);
        console.log(`[statsUtils]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[statsUtils]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
        console.log(`[statsUtils]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Clear all statistics caches (Enterprise-Grade Cache Management)
 */
function clearAllStatsCaches() {
    guildRankingCache.clear();
    globalRankingCache.clear();
    guildConfigCache.clear();

    console.log('[statsUtils] 🗑️ Cleared all statistics caches');
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, statsUtilsMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    formatDuration,
    getVoiceExpGuildRank,
    getTextExpGuildRank,
    getVoiceExpGlobalRank,
    getTextExpGlobalRank,
    calculateAvgPerDay,

    // Enhanced optimization functions
    getCachedGuildConfigs,
    getStatsUtilsStats,
    performanceCleanupAndOptimization,
    clearAllStatsCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...statsUtilsMetrics })
};
