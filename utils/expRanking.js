const { optimizedFind, optimizedFindOne, optimizedCountDocuments, optimizedAggregate } = require('./database-optimizer.js');
const {
    getCachedGuildRankings,
    setCachedGuildRankings,
    getCachedGlobalRankings,
    setCachedGlobalRankings
} = require('./expCache.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * EXP Ranking System (Enterprise-Grade Performance Optimized)
 * Handles both guild and global rankings with comprehensive optimization
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const expRankingMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    rankingsProcessed: 0,
    userRankQueries: 0,
    parallelOperations: 0,
    aggregationQueries: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const userRankCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for user rankings
const guildStatsCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild statistics
const globalStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for global statistics
const rankingAnalyticsCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for ranking analytics

// Register caches for global cleanup
registerCache(userRankCache);
registerCache(guildStatsCache);
registerCache(globalStatsCache);
registerCache(rankingAnalyticsCache);

// Since EXP rates are now standardized (bot owner only), normalization is no longer needed

/**
 * Get guild EXP rankings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} guildId - Guild ID
 * @param {number} limit - Number of results to return (default: 10)
 * @returns {Array} Array of ranked members
 */
async function getGuildRankings(guildId, limit = 10) {
    const startTime = Date.now();
    expRankingMetrics.rankingsProcessed++;

    try {
        // Check cache first
        const cached = await getCachedGuildRankings(guildId, limit);
        if (cached) {
            expRankingMetrics.cacheHits++;
            if (expRankingMetrics.verboseLogging) {
                console.log(`[expRanking] ⚡ Guild rankings cache hit for ${guildId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expRankingMetrics.cacheMisses++;
        expRankingMetrics.databaseQueries++;

        // Use projection to only fetch needed fields for better performance
        const members = await optimizedFind("member",
            {
                guildId: guildId,
                "exp.total": { $gt: 0 } // Only members with EXP
            },
            {
                projection: {
                    userId: 1,
                    guildId: 1,
                    "exp.total": 1,
                    "exp.lastText": 1,
                    "exp.lastVoice": 1
                },
                sort: { "exp.total": -1 },
                limit: limit
            }
        );

        // Add rank numbers
        const rankings = members.map((member, index) => ({
            rank: index + 1,
            userId: member.userId,
            guildId: member.guildId,
            exp: {
                total: member.exp?.total || 0,
                lastText: member.exp?.lastText || 0,
                lastVoice: member.exp?.lastVoice || 0
            }
        }));

        // Cache the results
        setCachedGuildRankings(guildId, limit, rankings);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        expRankingMetrics.averageQueryTime =
            (expRankingMetrics.averageQueryTime * (expRankingMetrics.databaseQueries - 1) + duration) /
            expRankingMetrics.databaseQueries;

        if (expRankingMetrics.verboseLogging || duration > 100) {
            console.log(`[expRanking] ✅ Guild rankings fetched for ${guildId}: ${rankings.length} members (${duration}ms) - cached for future access`);
        }

        return rankings;

    } catch (error) {
        console.error('[expRanking] ❌ Error getting guild rankings:', error);
        return [];
    }
}

/**
 * Get global EXP rankings (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {number} limit - Number of results to return (default: 10)
 * @returns {Array} Array of ranked members
 */
async function getGlobalRankings(limit = 10) {
    const startTime = Date.now();
    expRankingMetrics.rankingsProcessed++;

    try {
        // Check cache first
        const cached = await getCachedGlobalRankings(limit);
        if (cached) {
            expRankingMetrics.cacheHits++;
            if (expRankingMetrics.verboseLogging) {
                console.log(`[expRanking] ⚡ Global rankings cache hit for limit ${limit} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expRankingMetrics.cacheMisses++;
        expRankingMetrics.databaseQueries++;

        // Simple query since EXP rates are now standardized
        const members = await optimizedFind("member",
            { "exp.total": { $gt: 0 } },
            {
                projection: {
                    userId: 1,
                    guildId: 1,
                    "exp.total": 1,
                    "exp.lastText": 1,
                    "exp.lastVoice": 1
                },
                sort: { "exp.total": -1 },
                limit: limit
            }
        );

        // Add rank numbers
        const rankings = members.map((member, index) => ({
            rank: index + 1,
            userId: member.userId,
            guildId: member.guildId,
            exp: {
                total: member.exp?.total || 0,
                lastText: member.exp?.lastText || 0,
                lastVoice: member.exp?.lastVoice || 0
            }
        }));

        // Cache the results
        setCachedGlobalRankings(limit, rankings);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        expRankingMetrics.averageQueryTime =
            (expRankingMetrics.averageQueryTime * (expRankingMetrics.databaseQueries - 1) + duration) /
            expRankingMetrics.databaseQueries;

        if (expRankingMetrics.verboseLogging || duration > 150) {
            console.log(`[expRanking] ✅ Global rankings fetched: ${rankings.length} members (${duration}ms) - cached for future access`);
        }

        return rankings;

    } catch (error) {
        console.error('[expRanking] ❌ Error getting global rankings:', error);
        return [];
    }
}

/**
 * Get a specific user's rank in guild (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel processing, enhanced caching, and comprehensive performance monitoring
 * @param {string} guildId - Guild ID
 * @param {string} userId - User ID
 * @returns {Object} User's rank and EXP info
 */
async function getUserGuildRank(guildId, userId) {
    const startTime = Date.now();
    const cacheKey = `guild_rank_${guildId}_${userId}`;
    expRankingMetrics.userRankQueries++;

    try {
        // Check cache first
        const cached = userRankCache.get(cacheKey);
        if (cached) {
            expRankingMetrics.cacheHits++;
            if (expRankingMetrics.verboseLogging) {
                console.log(`[expRanking] ⚡ User guild rank cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expRankingMetrics.cacheMisses++;
        expRankingMetrics.databaseQueries++;

        // OPTIMIZED: Execute user lookup and count operations in parallel
        expRankingMetrics.parallelOperations++;
        const [userMemberResult, totalWithExpResult] = await Promise.allSettled([
            optimizedFindOne("member", { guildId, userId }),
            optimizedCountDocuments("member", {
                guildId: guildId,
                "exp.total": { $gt: 0 }
            })
        ]);

        const userMember = userMemberResult.status === 'fulfilled' ? userMemberResult.value : null;
        const totalWithExp = totalWithExpResult.status === 'fulfilled' ? totalWithExpResult.value : 0;

        if (!userMember || !userMember.exp || userMember.exp.total <= 0) {
            const result = { rank: null, exp: 0, total: totalWithExp };
            userRankCache.set(cacheKey, result);
            return result;
        }

        // Count how many members have more EXP
        const higherCount = await optimizedCountDocuments("member", {
            guildId: guildId,
            "exp.total": { $gt: userMember.exp.total }
        });

        const result = {
            rank: higherCount + 1,
            exp: userMember.exp.total,
            total: totalWithExp
        };

        // Cache the result
        userRankCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        if (expRankingMetrics.verboseLogging || duration > 100) {
            console.log(`[expRanking] ✅ User guild rank calculated for ${userId}: #${result.rank}/${result.total} (${duration}ms)`);
        }

        return result;

    } catch (error) {
        console.error('[expRanking] ❌ Error getting user guild rank:', error);
        return { rank: null, exp: 0, total: 0 };
    }
}

/**
 * Get a specific user's global rank (Enterprise-Grade Optimized)
 * OPTIMIZED: Aggregation pipeline instead of fetching large datasets, enhanced caching
 * @param {string} userId - User ID
 * @returns {Object} User's global rank and EXP info
 */
async function getUserGlobalRank(userId) {
    const startTime = Date.now();
    const cacheKey = `global_rank_${userId}`;
    expRankingMetrics.userRankQueries++;

    try {
        // Check cache first
        const cached = userRankCache.get(cacheKey);
        if (cached) {
            expRankingMetrics.cacheHits++;
            if (expRankingMetrics.verboseLogging) {
                console.log(`[expRanking] ⚡ User global rank cache hit for ${userId} (${Date.now() - startTime}ms)`);
            }
            return cached;
        }

        expRankingMetrics.cacheMisses++;
        expRankingMetrics.databaseQueries++;
        expRankingMetrics.aggregationQueries++;

        // OPTIMIZED: Use aggregation pipeline for efficient rank calculation
        expRankingMetrics.parallelOperations++;
        const [userDataResult, rankDataResult] = await Promise.allSettled([
            // Get user's EXP data
            optimizedFindOne("member",
                { userId: userId, "exp.total": { $gt: 0 } },
                { projection: { "exp.total": 1, userId: 1, guildId: 1 } }
            ),

            // Get total count of users with EXP
            optimizedCountDocuments("member", { "exp.total": { $gt: 0 } })
        ]);

        const userData = userDataResult.status === 'fulfilled' ? userDataResult.value : null;
        const totalUsers = rankDataResult.status === 'fulfilled' ? rankDataResult.value : 0;

        if (!userData || !userData.exp || userData.exp.total <= 0) {
            const result = { rank: null, exp: 0, total: totalUsers };
            userRankCache.set(cacheKey, result);
            return result;
        }

        // OPTIMIZED: Use aggregation to count users with higher EXP efficiently
        const higherUsersResult = await optimizedAggregate("member", [
            {
                $match: {
                    "exp.total": { $gt: userData.exp.total }
                }
            },
            {
                $count: "higherUsers"
            }
        ]);

        const higherCount = higherUsersResult.length > 0 ? higherUsersResult[0].higherUsers : 0;

        const result = {
            rank: higherCount + 1,
            exp: userData.exp.total,
            total: totalUsers
        };

        // Cache the result
        userRankCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        if (expRankingMetrics.verboseLogging || duration > 150) {
            console.log(`[expRanking] ✅ User global rank calculated for ${userId}: #${result.rank}/${result.total} (${duration}ms) - using aggregation pipeline`);
        }

        return result;

    } catch (error) {
        console.error('[expRanking] ❌ Error getting user global rank:', error);
        return { rank: null, exp: 0, total: 0 };
    }
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = expRankingMetrics.cacheHits + expRankingMetrics.cacheMisses > 0 ?
        (expRankingMetrics.cacheHits / (expRankingMetrics.cacheHits + expRankingMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: expRankingMetrics.cacheHits,
            cacheMisses: expRankingMetrics.cacheMisses,
            databaseQueries: expRankingMetrics.databaseQueries,
            averageQueryTime: `${expRankingMetrics.averageQueryTime.toFixed(2)}ms`,
            rankingsProcessed: expRankingMetrics.rankingsProcessed,
            userRankQueries: expRankingMetrics.userRankQueries,
            parallelOperations: expRankingMetrics.parallelOperations,
            aggregationQueries: expRankingMetrics.aggregationQueries,
            lastOptimization: new Date(expRankingMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            userRank: userRankCache.getStats(),
            guildStats: guildStatsCache.getStats(),
            globalStats: globalStatsCache.getStats(),
            rankingAnalytics: rankingAnalyticsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            userRank: userRankCache.getStats().memoryUsage,
            guildStats: guildStatsCache.getStats().memoryUsage,
            globalStats: globalStatsCache.getStats().memoryUsage,
            rankingAnalytics: rankingAnalyticsCache.getStats().memoryUsage,
            total: userRankCache.getStats().memoryUsage +
                   guildStatsCache.getStats().memoryUsage +
                   globalStatsCache.getStats().memoryUsage +
                   rankingAnalyticsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, expRankingMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 100) {
        recommendations.push('Database queries are slow - consider adding indexes or optimizing queries');
    }

    if (metrics.databaseQueries > metrics.cacheHits * 2) {
        recommendations.push('High database query ratio - consider caching more aggressively');
    }

    if (metrics.aggregationQueries > metrics.userRankQueries * 0.5) {
        recommendations.push('High aggregation usage - consider pre-computing rankings');
    }

    if (metrics.parallelOperations < metrics.userRankQueries * 0.8) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    expRankingMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[expRanking] 📊 Performance Report:`);
    console.log(`[expRanking]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[expRanking]   Rankings Processed: ${stats.performance.rankingsProcessed}`);
    console.log(`[expRanking]   User Rank Queries: ${stats.performance.userRankQueries}`);
    console.log(`[expRanking]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[expRanking]   Aggregation Queries: ${stats.performance.aggregationQueries}`);
    console.log(`[expRanking]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[expRanking]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[expRanking]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[expRanking] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all EXP ranking caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllExpRankingCaches() {
    userRankCache.clear();
    guildStatsCache.clear();
    globalStatsCache.clear();
    rankingAnalyticsCache.clear();

    console.log('[expRanking] 🗑️ Cleared all EXP ranking caches');
}

/**
 * Invalidate specific user ranking caches
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional)
 */
function invalidateUserRankCaches(userId, guildId = null) {
    // Clear user-specific cache entries
    const keys = Array.from(userRankCache.keys()).filter(key => key.includes(`_${userId}`));

    keys.forEach(key => userRankCache.delete(key));

    // If guild specified, also clear guild-specific caches
    if (guildId) {
        const guildKeys = Array.from(guildStatsCache.keys()).filter(key => key.includes(guildId));
        guildKeys.forEach(key => guildStatsCache.delete(key));
    }

    if (expRankingMetrics.verboseLogging) {
        console.log(`[expRanking] 🗑️ Invalidated ${keys.length} cache entries for user ${userId}${guildId ? ` in guild ${guildId}` : ''}`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, expRankingMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getGuildRankings,
    getGlobalRankings,
    getUserGuildRank,
    getUserGlobalRank,

    // Enhanced optimization functions
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllExpRankingCaches,
    invalidateUserRankCaches,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...expRankingMetrics })
};
