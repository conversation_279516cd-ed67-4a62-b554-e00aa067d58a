/**
 * Optimized Thread Refresh System
 * Handles thread refreshing with improved performance and rate limiting
 */

const { getCachedRefreshQueue, invalidateRefreshQueue } = require('./openerCache.js');
const { setThreadLastOpened, isOpenerEnabled } = require('../commands/utility/opener_db.js');
const { sendOpenerThreadBumpedLog } = require('./sendLog.js');

// Rate limiting and batching configuration
const REFRESH_BATCH_SIZE = 10; // Process 10 threads at a time
const REFRESH_DELAY = 2000; // 2 seconds between batches
const DISCORD_RATE_LIMIT_DELAY = 1000; // 1 second between Discord API calls
const MAX_CONCURRENT_REFRESHES = 3; // Maximum concurrent refresh operations

// Track ongoing refresh operations
const activeRefreshes = new Set();
let refreshInProgress = false;

/**
 * Refresh a single thread with rate limiting
 * @param {Object} client - Discord client
 * @param {Object} threadData - Thread data from database
 * @returns {Promise<Object>} Refresh result
 */
async function refreshSingleThread(client, threadData) {
    const { threadId } = threadData;
    
    try {
        // Check if already being refreshed
        if (activeRefreshes.has(threadId)) {
            return { success: false, reason: 'already_refreshing', threadId };
        }
        
        activeRefreshes.add(threadId);
        
        // Fetch thread from Discord
        const thread = await client.channels.fetch(threadId);
        if (!thread || !thread.isThread()) {
            activeRefreshes.delete(threadId);
            return { success: false, reason: 'thread_not_found', threadId };
        }
        
        // Check if opener is enabled for this guild
        const enabled = await isOpenerEnabled(thread.guild.id);
        if (!enabled) {
            activeRefreshes.delete(threadId);
            return { success: false, reason: 'opener_disabled', threadId, guildId: thread.guild.id };
        }
        
        // Only refresh if thread is not archived and manageable
        if (thread.archived || !thread.manageable) {
            activeRefreshes.delete(threadId);
            return { success: false, reason: 'thread_not_manageable', threadId };
        }
        
        // Perform the refresh operation
        const current = thread.autoArchiveDuration;
        const alt = current === 60 ? 1440 : 60;
        
        await thread.setAutoArchiveDuration(alt);
        await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
        await thread.setAutoArchiveDuration(current);
        
        const now = Date.now();
        await setThreadLastOpened(thread.id, now, thread.autoArchiveDuration, thread.guild.id);
        
        // Send thread bumped log (async, don't wait)
        sendOpenerThreadBumpedLog(
            thread.guild.id,
            thread.id,
            thread.name,
            'refreshed',
            client
        ).catch(error => {
            console.error(`[openerRefresh] Error sending bump log for ${threadId}:`, error.message);
        });
        
        activeRefreshes.delete(threadId);
        return { 
            success: true, 
            threadId, 
            threadName: thread.name,
            guildId: thread.guild.id 
        };
        
    } catch (error) {
        activeRefreshes.delete(threadId);
        return { 
            success: false, 
            reason: 'error', 
            threadId, 
            error: error.message 
        };
    }
}

/**
 * Process a batch of threads for refreshing
 * @param {Object} client - Discord client
 * @param {Array} threadBatch - Array of thread data objects
 * @returns {Promise<Array>} Array of refresh results
 */
async function processBatchRefresh(client, threadBatch) {
    const results = [];
    
    // Process threads with rate limiting
    for (let i = 0; i < threadBatch.length; i++) {
        const threadData = threadBatch[i];
        
        try {
            const result = await refreshSingleThread(client, threadData);
            results.push(result);
            
            // Add delay between Discord API calls to respect rate limits
            if (i < threadBatch.length - 1) {
                await new Promise(resolve => setTimeout(resolve, DISCORD_RATE_LIMIT_DELAY));
            }
            
        } catch (error) {
            results.push({
                success: false,
                reason: 'batch_error',
                threadId: threadData.threadId,
                error: error.message
            });
        }
    }
    
    return results;
}

/**
 * Main refresh function with optimized batching and rate limiting
 * @param {Object} client - Discord client
 * @returns {Promise<Object>} Refresh summary
 */
async function refreshThreadsOptimized(client) {
    if (refreshInProgress) {
        console.log('[openerRefresh] Refresh already in progress, skipping');
        return { skipped: true, reason: 'already_in_progress' };
    }
    
    try {
        refreshInProgress = true;
        
        // Get threads needing refresh (cached)
        const threadsNeedingRefresh = await getCachedRefreshQueue();
        
        if (threadsNeedingRefresh.length === 0) {
            return { processed: 0, successful: 0, failed: 0 };
        }
        
        console.log(`[openerRefresh] Found ${threadsNeedingRefresh.length} threads needing refresh`);
        
        // Process in batches to avoid overwhelming Discord API
        const batches = [];
        for (let i = 0; i < threadsNeedingRefresh.length; i += REFRESH_BATCH_SIZE) {
            batches.push(threadsNeedingRefresh.slice(i, i + REFRESH_BATCH_SIZE));
        }
        
        let totalProcessed = 0;
        let totalSuccessful = 0;
        let totalFailed = 0;
        
        // Process batches with delays
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            
            console.log(`[openerRefresh] Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} threads)`);
            
            const batchResults = await processBatchRefresh(client, batch);
            
            // Collect statistics
            totalProcessed += batchResults.length;
            totalSuccessful += batchResults.filter(r => r.success).length;
            totalFailed += batchResults.filter(r => !r.success).length;
            
            // Log batch results
            const successful = batchResults.filter(r => r.success);
            const failed = batchResults.filter(r => !r.success);
            
            if (successful.length > 0) {
                console.log(`[openerRefresh] Batch ${batchIndex + 1}: ${successful.length} threads refreshed successfully`);
            }
            
            if (failed.length > 0) {
                console.log(`[openerRefresh] Batch ${batchIndex + 1}: ${failed.length} threads failed to refresh`);
                // Log first few failures for debugging
                failed.slice(0, 3).forEach(result => {
                    console.log(`[openerRefresh] Failed: ${result.threadId} - ${result.reason}`);
                });
            }
            
            // Add delay between batches (except for the last one)
            if (batchIndex < batches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, REFRESH_DELAY));
            }
        }
        
        // Invalidate refresh queue cache after processing
        invalidateRefreshQueue();
        
        console.log(`[openerRefresh] Completed: ${totalSuccessful}/${totalProcessed} threads refreshed successfully`);
        
        return {
            processed: totalProcessed,
            successful: totalSuccessful,
            failed: totalFailed,
            batches: batches.length
        };
        
    } catch (error) {
        console.error('[openerRefresh] Error in refresh cycle:', error);
        return {
            processed: 0,
            successful: 0,
            failed: 0,
            error: error.message
        };
    } finally {
        refreshInProgress = false;
    }
}

/**
 * Get refresh system statistics
 * @returns {Object} Refresh system stats
 */
function getRefreshStats() {
    return {
        refreshInProgress: refreshInProgress,
        activeRefreshes: activeRefreshes.size,
        batchSize: REFRESH_BATCH_SIZE,
        refreshDelay: REFRESH_DELAY,
        rateLimitDelay: DISCORD_RATE_LIMIT_DELAY,
        maxConcurrent: MAX_CONCURRENT_REFRESHES
    };
}

/**
 * Force stop all refresh operations (emergency use)
 */
function forceStopRefresh() {
    refreshInProgress = false;
    activeRefreshes.clear();
    console.log('[openerRefresh] Force stopped all refresh operations');
}

module.exports = {
    refreshThreadsOptimized,
    refreshSingleThread,
    processBatchRefresh,
    getRefreshStats,
    forceStopRefresh
};
