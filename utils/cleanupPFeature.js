/**
 * Cleanup script for removing 'p' feature remnants from the database
 * This script removes:
 * 1. The 'pchannels' collection entirely
 * 2. The 'p' configuration from all guild documents
 */

const { mongoClient } = require('../mongo/client.js');

async function cleanupPFeatureRemnants() {
    try {
        console.log('[cleanupPFeature] Starting cleanup of p feature remnants...');
        
        const db = mongoClient.db("test");
        
        // 1. Drop the pchannels collection entirely
        try {
            const pchannelsResult = await db.collection("pchannels").drop();
            console.log('[cleanupPFeature] Dropped pchannels collection');
        } catch (error) {
            if (error.codeName === 'NamespaceNotFound') {
                console.log('[cleanupPFeature] pchannels collection does not exist (already clean)');
            } else {
                console.error('[cleanupPFeature] Error dropping pchannels collection:', error);
            }
        }
        
        // 2. Remove 'p' configuration from all guild documents
        const guildsCol = db.collection("guilds");
        const updateResult = await guildsCol.updateMany(
            { p: { $exists: true } }, // Only update documents that have the 'p' field
            { $unset: { p: "" } }     // Remove the 'p' field entirely
        );
        
        console.log(`[cleanupPFeature] Removed 'p' configuration from ${updateResult.modifiedCount} guild documents`);
        
        // 3. Verify cleanup
        const remainingPConfigs = await guildsCol.countDocuments({ p: { $exists: true } });
        const remainingPChannels = await db.listCollections({ name: "pchannels" }).toArray();
        
        console.log('[cleanupPFeature] Cleanup verification:');
        console.log(`  - Remaining guild 'p' configurations: ${remainingPConfigs}`);
        console.log(`  - pchannels collection exists: ${remainingPChannels.length > 0}`);
        
        if (remainingPConfigs === 0 && remainingPChannels.length === 0) {
            console.log('[cleanupPFeature] ✅ All p feature remnants successfully removed!');
        } else {
            console.log('[cleanupPFeature] ⚠️  Some remnants may still exist');
        }
        
        return {
            pchannelsDropped: true,
            guildsUpdated: updateResult.modifiedCount,
            remainingPConfigs,
            pchannelsCollectionExists: remainingPChannels.length > 0
        };
        
    } catch (error) {
        console.error('[cleanupPFeature] Error during cleanup:', error);
        throw error;
    }
}

// Export for use in other files
module.exports = {
    cleanupPFeatureRemnants
};

// Allow running directly with node
if (require.main === module) {
    const { connect } = require('../mongo/client.js');
    
    connect()
        .then(() => {
            console.log('[cleanupPFeature] Connected to database');
            return cleanupPFeatureRemnants();
        })
        .then((result) => {
            console.log('[cleanupPFeature] Cleanup completed:', result);
            process.exit(0);
        })
        .catch((error) => {
            console.error('[cleanupPFeature] Cleanup failed:', error);
            process.exit(1);
        });
}
