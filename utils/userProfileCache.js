/**
 * User Profile Caching Utility (Enterprise-Grade Performance Optimized)
 * Provides unified caching for user profile data to dramatically improve /you command performance
 * Combines EXP data, inventory summary, and notifications into a single cached object
 * OPTIMIZED: Multi-tier LRU caching, parallel processing, and performance monitoring
 */

const { optimizedFindOne, optimizedFind } = require('./database-optimizer.js');
const { getCachedLevelCalculation } = require('./expCache.js');
const { getCachedUserGlobalInventory } = require('./itemCache.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring
const userProfileMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    profilesBuilt: 0,
    profilesServed: 0,
    parallelOperations: 0,
    partialFailures: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000 // 10min dev, 20min prod
};

// OPTIMIZED: Multi-tier LRU caches for maximum performance
const userProfileCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for complete profiles
const expDataCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes for EXP calculations
const inventorySummaryCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for inventory summaries
const notificationCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes for notification data

// Register caches for global cleanup
registerCache(userProfileCache);
registerCache(expDataCache);
registerCache(inventorySummaryCache);
registerCache(notificationCache);

/**
 * Get cached user profile data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @returns {Promise<Object|null>} Cached profile data or null if not found/expired
 */
async function getCachedUserProfile(userId, guildId) {
    const startTime = Date.now();
    const cacheKey = `${userId}_${guildId}`;

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = userProfileCache.get(cacheKey);

    if (cached) {
        userProfileMetrics.cacheHits++;
        userProfileMetrics.profilesServed++;
        if (userProfileMetrics.verboseLogging) {
            console.log(`[userProfileCache] ⚡ Profile cache hit for ${userId} (${Date.now() - startTime}ms)`);
        }
        return cached;
    }

    userProfileMetrics.cacheMisses++;
    return null; // Return null if not found/expired
}

/**
 * Build and cache user profile data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced parallel processing, comprehensive error handling, and performance monitoring
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Object} client - Discord client (for guild fetches)
 * @returns {Promise<Object>} Complete user profile data
 */
async function buildAndCacheUserProfile(userId, guildId, client) {
    const startTime = Date.now();
    userProfileMetrics.profilesBuilt++;
    userProfileMetrics.databaseQueries++;

    try {
        // OPTIMIZED: Fetch all required data in parallel with comprehensive error handling
        userProfileMetrics.parallelOperations++;
        const [guildDataResult, memberDataResult, globalInventoryResult, notificationsResult] = await Promise.allSettled([
            // Guild configuration
            optimizedFindOne("guilds",
                { id: guildId },
                { projection: { exp: 1 } } // Only fetch EXP config
            ),
            // Member EXP data
            optimizedFindOne("member",
                { guildId: guildId, userId: userId },
                { projection: { exp: 1 } } // Only fetch EXP data
            ),
            // Global inventory (cached)
            getCachedUserGlobalInventory(userId),
            // Notifications (limited to 5 for performance)
            optimizedFind("item_notifications_queue",
                { userId: userId, guildId: guildId, viewed: false },
                {
                    projection: { items: 1, location: 1, createdAt: 1 }, // Only needed fields
                    limit: 5,
                    sort: { createdAt: 1 }
                }
            )
        ]);

        // Extract results with fallback values for failed operations
        const guildData = guildDataResult.status === 'fulfilled' ? guildDataResult.value : null;
        const memberData = memberDataResult.status === 'fulfilled' ? memberDataResult.value : null;
        const globalInventory = globalInventoryResult.status === 'fulfilled' ? globalInventoryResult.value : [];
        const notifications = notificationsResult.status === 'fulfilled' ? notificationsResult.value : [];

        // Track partial failures for monitoring
        const failedOperations = [guildDataResult, memberDataResult, globalInventoryResult, notificationsResult]
            .filter(result => result.status === 'rejected').length;

        if (failedOperations > 0) {
            userProfileMetrics.partialFailures++;
            if (userProfileMetrics.verboseLogging) {
                console.warn(`[userProfileCache] ⚠️  ${failedOperations} operations failed for user ${userId}`);
            }
        }

        // OPTIMIZED: Process EXP data with enhanced caching
        const expData = await processExpDataWithCaching(userId, guildId, guildData, memberData);

        // OPTIMIZED: Process inventory and notifications with enhanced caching
        const [inventorySummary, notificationsSummary] = await Promise.allSettled([
            processInventorySummaryWithCaching(userId, globalInventory),
            processNotificationsSummaryWithCaching(userId, guildId, notifications)
        ]);

        const finalInventorySummary = inventorySummary.status === 'fulfilled' ? inventorySummary.value : {
            totalItems: 0,
            emoteDisplay: '*Error loading inventory*',
            hasItems: false
        };

        const finalNotificationsSummary = notificationsSummary.status === 'fulfilled' ? notificationsSummary.value : {
            count: 0,
            hasUnviewed: false,
            firstNotification: null
        };

        // Build complete profile object
        const profileData = {
            userId: userId,
            guildId: guildId,
            exp: expData,
            inventory: finalInventorySummary,
            notifications: finalNotificationsSummary,
            lastUpdated: new Date()
        };

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        const cacheKey = `${userId}_${guildId}`;
        userProfileCache.set(cacheKey, profileData);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        userProfileMetrics.averageQueryTime =
            (userProfileMetrics.averageQueryTime * (userProfileMetrics.profilesBuilt - 1) + duration) /
            userProfileMetrics.profilesBuilt;

        if (userProfileMetrics.verboseLogging || duration > 200) {
            console.log(`[userProfileCache] ✅ Profile built for ${userId}: ${duration}ms (${failedOperations} failures) - cached for future access`);
        }

        return profileData;

    } catch (error) {
        console.error(`[userProfileCache] Error building profile for ${userId}:`, error);
        
        // Return minimal profile data on error
        return {
            userId: userId,
            guildId: guildId,
            exp: {
                total: 0,
                currentLevel: 0,
                nextLevelExp: 100,
                levelRole: null,
                levelEmoji: '🌱',
                progressCurrent: 0,
                progressMax: 100,
                progressPercentage: 0
            },
            inventory: {
                totalItems: 0,
                emoteDisplay: '*Error loading inventory*',
                hasItems: false
            },
            notifications: {
                count: 0,
                hasUnviewed: false,
                firstNotification: null
            },
            lastUpdated: new Date()
        };
    }
}

/**
 * Process EXP data with enhanced caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Specialized caching for EXP calculations and level data
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Object} guildData - Guild configuration data
 * @param {Object} memberData - Member EXP data
 * @returns {Promise<Object>} Processed EXP data
 */
async function processExpDataWithCaching(userId, guildId, guildData, memberData) {
    const cacheKey = `exp_${userId}_${guildId}`;

    // Check cache first
    const cached = expDataCache.get(cacheKey);
    if (cached && memberData?.exp?.total === cached.cachedExp) {
        userProfileMetrics.cacheHits++;
        return cached.data;
    }

    // Process EXP data
    let expData = {
        total: 0,
        currentLevel: 0,
        nextLevelExp: 100,
        levelRole: null,
        levelEmoji: '🌱',
        progressCurrent: 0,
        progressMax: 100,
        progressPercentage: 0
    };

    if (guildData?.exp?.enabled && memberData?.exp?.total !== undefined) {
        const exp = memberData.exp.total;
        const levels = guildData.exp.levels ?? [];

        // Use cached level calculation
        const levelCalc = getCachedLevelCalculation(exp, levels);

        expData.total = exp;
        expData.currentLevel = levelCalc.currentLevel;
        expData.nextLevelExp = levelCalc.nextLevelExp;

        if (levelCalc.levelIndex >= 0) {
            const currentLevelData = levels[levelCalc.levelIndex];
            expData.levelRole = currentLevelData.roleId ? `<@&${currentLevelData.roleId}>` : null;
            expData.levelEmoji = currentLevelData.emoji || '🌱';
        }

        // Calculate progress
        if (levelCalc.nextLevelExp === null) {
            // Max level reached
            expData.progressCurrent = exp;
            expData.progressMax = exp;
            expData.progressPercentage = 100;
        } else {
            expData.progressCurrent = exp;
            expData.progressMax = levelCalc.nextLevelExp;
            expData.progressPercentage = Math.floor((exp / levelCalc.nextLevelExp) * 100);
        }
    }

    // Cache the result with EXP value for validation
    expDataCache.set(cacheKey, {
        data: expData,
        cachedExp: memberData?.exp?.total || 0
    });

    return expData;
}

/**
 * Process inventory summary with enhanced caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Specialized caching for inventory display generation
 * @param {string} userId - User ID
 * @param {Array} globalInventory - Global inventory data
 * @returns {Promise<Object>} Processed inventory summary
 */
async function processInventorySummaryWithCaching(userId, globalInventory) {
    const cacheKey = `inventory_${userId}_${globalInventory.length}`;

    // Check cache first
    const cached = inventorySummaryCache.get(cacheKey);
    if (cached) {
        userProfileMetrics.cacheHits++;
        return cached;
    }

    // Process inventory summary (fast, just counts and emotes)
    let inventorySummary = {
        totalItems: 0,
        emoteDisplay: '*No items yet - gain EXP to find items!*',
        hasItems: false
    };

    if (globalInventory.length > 0) {
        inventorySummary.totalItems = globalInventory.length;
        inventorySummary.hasItems = true;

        // Fast processing - just count emotes without complex sorting
        const itemCounts = new Map();
        globalInventory.forEach(item => {
            const emote = item.itemEmote || '📦';
            itemCounts.set(emote, (itemCounts.get(emote) || 0) + 1);
        });

        // Build display efficiently (limit to 10 emotes per row, max 3 rows)
        const emoteStrings = [];
        let emoteCount = 0;
        for (const [emote, count] of itemCounts) {
            if (emoteCount >= 30) break; // Max 30 emotes for performance
            const subscriptCount = numberToSubscript(count);
            emoteStrings.push(`${emote}${subscriptCount}`);
            emoteCount++;
        }

        // Group into rows of 10
        const rows = [];
        for (let i = 0; i < emoteStrings.length; i += 10) {
            rows.push(emoteStrings.slice(i, i + 10).join(' '));
        }

        // Add inventory header with count
        inventorySummary.emoteDisplay = `## inventory (${inventorySummary.totalItems})\n${rows.slice(0, 3).join('\n')}`;
    }

    // Cache the result
    inventorySummaryCache.set(cacheKey, inventorySummary);

    return inventorySummary;
}

/**
 * Process notifications summary with enhanced caching (Enterprise-Grade Optimized)
 * OPTIMIZED: Specialized caching for notification data
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Array} notifications - Notification data
 * @returns {Promise<Object>} Processed notifications summary
 */
async function processNotificationsSummaryWithCaching(userId, guildId, notifications) {
    const cacheKey = `notifications_${userId}_${guildId}_${notifications.length}`;

    // Check cache first
    const cached = notificationCache.get(cacheKey);
    if (cached) {
        userProfileMetrics.cacheHits++;
        return cached;
    }

    // Process notifications summary
    const notificationsSummary = {
        count: notifications.length,
        hasUnviewed: notifications.length > 0,
        firstNotification: notifications.length > 0 ? notifications[0] : null
    };

    // Cache the result
    notificationCache.set(cacheKey, notificationsSummary);

    return notificationsSummary;
}

/**
 * Get or build user profile data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID
 * @param {Object} client - Discord client
 * @returns {Promise<Object>} User profile data
 */
async function getUserProfile(userId, guildId, client) {
    const startTime = Date.now();

    // Try cache first
    const cached = await getCachedUserProfile(userId, guildId);
    if (cached) {
        return cached;
    }

    // Cache miss - build and cache new data
    const result = await buildAndCacheUserProfile(userId, guildId, client);

    const duration = Date.now() - startTime;
    if (userProfileMetrics.verboseLogging || duration > 300) {
        console.log(`[userProfileCache] ✅ Profile served for ${userId}: ${duration}ms (cache miss)`);
    }

    return result;
}

/**
 * Invalidate user profile cache
 * @param {string} userId - User ID
 * @param {string} guildId - Guild ID (optional, if not provided, invalidates all guilds for user)
 */
function invalidateUserProfile(userId, guildId = null) {
    if (guildId) {
        // Invalidate specific guild
        const cacheKey = `${userId}_${guildId}`;
        userProfileCache.delete(cacheKey);
    } else {
        // Invalidate all guilds for user - need to iterate through keys
        const profileKeys = userProfileCache.getKeysByAccessTime();
        for (const key of profileKeys) {
            if (key.startsWith(`${userId}_`)) {
                userProfileCache.delete(key);
            }
        }
    }
}

/**
 * Helper function to convert numbers to subscript
 * @param {number} num - Number to convert
 * @returns {string} Subscript representation
 */
function numberToSubscript(num) {
    const subscriptMap = {
        '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
        '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉'
    };
    return num.toString().split('').map(digit => subscriptMap[digit] || digit).join('');
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = userProfileMetrics.cacheHits + userProfileMetrics.cacheMisses > 0 ?
        (userProfileMetrics.cacheHits / (userProfileMetrics.cacheHits + userProfileMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: userProfileMetrics.cacheHits,
            cacheMisses: userProfileMetrics.cacheMisses,
            databaseQueries: userProfileMetrics.databaseQueries,
            averageQueryTime: `${userProfileMetrics.averageQueryTime.toFixed(2)}ms`,
            profilesBuilt: userProfileMetrics.profilesBuilt,
            profilesServed: userProfileMetrics.profilesServed,
            parallelOperations: userProfileMetrics.parallelOperations,
            partialFailures: userProfileMetrics.partialFailures,
            lastOptimization: new Date(userProfileMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            userProfiles: userProfileCache.getStats(),
            expData: expDataCache.getStats(),
            inventorySummary: inventorySummaryCache.getStats(),
            notifications: notificationCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            userProfiles: userProfileCache.getStats().memoryUsage,
            expData: expDataCache.getStats().memoryUsage,
            inventorySummary: inventorySummaryCache.getStats().memoryUsage,
            notifications: notificationCache.getStats().memoryUsage,
            total: userProfileCache.getStats().memoryUsage +
                   expDataCache.getStats().memoryUsage +
                   inventorySummaryCache.getStats().memoryUsage +
                   notificationCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, userProfileMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 200) {
        recommendations.push('Profile building is slow - investigate database performance');
    }

    if (metrics.partialFailures > metrics.profilesBuilt * 0.1) {
        recommendations.push('High partial failure rate - investigate database connectivity');
    }

    if (metrics.parallelOperations < metrics.profilesBuilt * 0.8) {
        recommendations.push('Low parallel operation usage - investigate sequential processing bottlenecks');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    userProfileMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[userProfileCache] 📊 Performance Report:`);
    console.log(`[userProfileCache]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[userProfileCache]   Profiles Built: ${stats.performance.profilesBuilt}`);
    console.log(`[userProfileCache]   Profiles Served: ${stats.performance.profilesServed}`);
    console.log(`[userProfileCache]   Parallel Operations: ${stats.performance.parallelOperations}`);
    console.log(`[userProfileCache]   Partial Failures: ${stats.performance.partialFailures}`);
    console.log(`[userProfileCache]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[userProfileCache]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[userProfileCache]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[userProfileCache] ⚠️  ${rec}`);
        }
    });

    return stats;
}

/**
 * Clear all user profile caches (Enterprise-Grade Cache Management)
 * OPTIMIZED: Comprehensive cache invalidation for configuration changes
 */
function clearAllUserProfileCaches() {
    userProfileCache.clear();
    expDataCache.clear();
    inventorySummaryCache.clear();
    notificationCache.clear();

    console.log('[userProfileCache] 🗑️ Cleared all user profile caches');
}

/**
 * Batch invalidate multiple user profiles (Enterprise-Grade Optimization)
 * OPTIMIZED: Efficient batch cache invalidation for bulk operations
 * @param {Array} userIds - Array of user IDs to invalidate
 * @param {string} guildId - Guild ID (optional)
 */
function batchInvalidateUserProfiles(userIds, guildId = null) {
    let totalInvalidated = 0;

    userIds.forEach(userId => {
        invalidateUserProfile(userId, guildId);
        // Estimate invalidated count (simplified for batch operations)
        totalInvalidated += 4; // Approximate cache entries per user
    });

    if (userProfileMetrics.verboseLogging) {
        console.log(`[userProfileCache] 🗑️ Batch invalidated ~${totalInvalidated} cache entries for ${userIds.length} users`);
    }
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, userProfileMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    getUserProfile,
    buildAndCacheUserProfile,
    getCachedUserProfile,
    invalidateUserProfile,

    // Enhanced optimization functions
    processExpDataWithCaching,
    processInventorySummaryWithCaching,
    processNotificationsSummaryWithCaching,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,
    clearAllUserProfileCaches,
    batchInvalidateUserProfiles,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...userProfileMetrics })
};
