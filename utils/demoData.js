const { PermissionFlagsBits } = require('discord.js');

/**
 * Get channels that the user can actually see and access
 * @param {Guild} guild - Discord guild
 * @param {GuildMember} member - Guild member (for permission checking)
 * @param {Channel} commandChannel - Channel where command was used (fallback)
 * @returns {Object} Object containing accessible channels
 */
function getAccessibleChannels(guild, member, commandChannel) {
    const textChannels = Array.from(guild.channels.cache.values())
        .filter(channel => {
            if (channel.type !== 0) return false; // Only text channels
            
            // Check if user can view the channel
            const permissions = channel.permissionsFor(member);
            return permissions && permissions.has(PermissionFlagsBits.ViewChannel);
        });
    
    // Ensure command channel is included if it's accessible
    const commandChannelAccessible = commandChannel && 
        commandChannel.type === 0 && 
        commandChannel.permissionsFor(member)?.has(PermissionFlagsBits.ViewChannel);
    
    if (commandChannelAccessible && !textChannels.find(ch => ch.id === commandChannel.id)) {
        textChannels.unshift(commandChannel);
    }
    
    // Use command channel as primary, then other accessible channels
    const primaryChannel = commandChannelAccessible ? commandChannel : textChannels[0];
    
    return {
        primary: primaryChannel,
        all: textChannels,
        commandChannel: commandChannelAccessible ? commandChannel : null
    };
}

/**
 * Get roles that are visible and not managed (bot roles)
 * @param {Guild} guild - Discord guild
 * @returns {Array} Array of accessible roles
 */
function getAccessibleRoles(guild) {
    return Array.from(guild.roles.cache.values())
        .filter(role => role.id !== guild.id && !role.managed) // Exclude @everyone and bot roles
        .slice(0, 5); // Take first 5 roles
}

/**
 * Generate unified demo data for all features
 * @param {Guild} guild - Discord guild
 * @param {GuildMember} member - Guild member
 * @param {Channel} commandChannel - Channel where command was used
 * @returns {Object} Unified demo data
 */
function generateUnifiedDemoData(guild, member, commandChannel) {
    const channels = getAccessibleChannels(guild, member, commandChannel);
    const roles = getAccessibleRoles(guild);
    
    // Use command channel as primary demo channel for consistency
    const demoChannel = channels.primary;
    const demoChannelId = demoChannel?.id;
    
    return {
        // Channel data
        channels: {
            primary: demoChannel,
            all: channels.all,
            primaryId: demoChannelId
        },
        
        // Role data
        roles: {
            all: roles,
            ids: roles.map(role => role.id),
            first3: roles.slice(0, 3)
        },
        
        // Timestamps
        timestamps: {
            recent: Date.now() - (13 * 60 * 1000), // 13 minutes ago
            hourAgo: Date.now() - (60 * 60 * 1000), // 1 hour ago
            dayAgo: Date.now() - (24 * 60 * 60 * 1000) // 1 day ago
        }
    };
}

/**
 * Get demo data for sticky feature
 */
function getStickyDemoData(guild, member, commandChannel) {
    const unified = generateUnifiedDemoData(guild, member, commandChannel);
    
    return {
        enabled: true,
        nick: true,
        roles: unified.roles.first3.map(role => role.id)
    };
}

/**
 * Get demo data for opener feature
 */
function getOpenerDemoData(guild, member, commandChannel) {
    const unified = generateUnifiedDemoData(guild, member, commandChannel);

    // Use different accessible channels if available, otherwise just show one
    const availableChannels = unified.channels.all.slice(0, 2);

    const fakeThreads = [];

    // First thread - use primary channel
    if (availableChannels[0]) {
        fakeThreads.push({
            threadId: '1234567890123456789',
            autoArchiveDuration: 60,
            lastOpened: unified.timestamps.hourAgo,
            realChannelId: availableChannels[0].id
        });
    }

    // Second thread - use different channel if available
    if (availableChannels[1] && availableChannels[1].id !== availableChannels[0]?.id) {
        fakeThreads.push({
            threadId: '2345678901234567890',
            autoArchiveDuration: 1440,
            lastOpened: unified.timestamps.recent,
            realChannelId: availableChannels[1].id
        });
    }

    // If only one channel available, just show one thread
    return fakeThreads;
}

/**
 * Get demo data for logs feature
 */
function getLogsDemoData(guild, member, commandChannel) {
    const unified = generateUnifiedDemoData(guild, member, commandChannel);

    // Use just the primary channel to avoid duplicate events
    const primaryChannel = unified.channels.primary;

    const set = primaryChannel ?
        [`${primaryChannel}: \`guildMemberAdd\` \`guildMemberRemove\` \`messageDelete\``] :
        [];

    // Split unset events into core and specialty categories (using unified specialty events)
    const unsetCore = ['`messageUpdate`', '`messageDeleteBulk`', '`voiceStateUpdate`'];
    const unsetSpecialty = ['`featureManagement`', '`expSystem`', '`threadOpener`'];

    return { set, unsetCore, unsetSpecialty };
}

/**
 * Get demo data for EXP feature
 */
function getExpDemoData(guild, member, commandChannel) {
    const unified = generateUnifiedDemoData(guild, member, commandChannel);
    
    return {
        enabled: true,
        levels: unified.roles.first3.map((role, index) => ({
            roleId: role.id,
            exp: [100, 500, 1000][index] || (index + 1) * 100
        })),
        levelChannel: unified.channels.primaryId, // Use accessible channel
        levelMsgEnabled: true,
        levelMsg: '{mention} leveled up to level {level} and received the {role} role.',
        text: {
            enabled: true,
            expPerMin: 1,  // Use fair default value
            cooldown: 1,
            minChars: 4    // Use actual default from messageCreate.js
        },
        voice: {
            enabled: true,
            expPerMin: 2,  // Correct default value
            cooldown: 1,
            msgEnabled: true,
            msg: '{mention} spent {duration} in voice and earned {exp} exp.'
        }
    };
}

/**
 * Get demo data for dehoist feature
 */
function getDehoistDemoData(guild, member, commandChannel) {
    const unified = generateUnifiedDemoData(guild, member, commandChannel);
    
    return {
        nameEnabled: true,
        names: ["Alien", "Pluto", "Neptune"],
        blocked: ["!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", ":", ";", "<", "=", ">", "?", "@", "[", "\\", "]", "^", "_", "`", "{", "|", "}", "~"],
        lastScan: unified.timestamps.recent, // Recent timestamp
        dehoisted: 1,
        failed: 0
    };
}

module.exports = {
    generateUnifiedDemoData,
    getAccessibleChannels,
    getAccessibleRoles,
    getStickyDemoData,
    getOpenerDemoData,
    getLogsDemoData,
    getExpDemoData,
    getDehoistDemoData
};
