const { optimizedFindOne, optimizedInsertOne, optimizedFindOneAndUpdate } = require('./database-optimizer.js');

// OPTIMIZATION: In-memory cache for command usage counts
const usageCache = new Map();
const CACHE_TTL = 30 * 1000; // 30 seconds cache for usage counts

/**
 * Increments the usage count for a given command and returns the updated count.
 * SAFE OPTIMIZED VERSION that preserves existing data
 * @param {string} commandName - The name of the command to track.
 * @returns {Promise<number>} - The updated usage count for the command.
 */
async function incrementCommandUsage(commandName) {
    try {
        // Check if document exists first to preserve existing data
        let stats = await optimizedFindOne("stats", { type: "command_usage", command: commandName });

        if (!stats) {
            // Create new document if it doesn't exist
            await optimizedInsertOne("stats", {
                type: "command_usage",
                command: commandName,
                count: 1
            });

            // Update cache
            usageCache.set(commandName, {
                count: 1,
                timestamp: Date.now()
            });

            return 1;
        } else {
            // OPTIMIZATION: Use findOneAndUpdate for existing documents
            const result = await optimizedFindOneAndUpdate("stats",
                { type: "command_usage", command: commandName },
                { $inc: { count: 1 } },
                {
                    returnDocument: 'after',
                    projection: { count: 1 }
                }
            );

            const newCount = result.value?.count || (stats.count + 1);

            // Update cache
            usageCache.set(commandName, {
                count: newCount,
                timestamp: Date.now()
            });

            return newCount;
        }
    } catch (error) {
        console.error(`[commandUsage] Error incrementing usage for ${commandName}:`, error);

        // Return cached value if available
        const cached = usageCache.get(commandName);
        return cached ? cached.count : 0;
    }
}

/**
 * Fetches the current usage count for a given command (without incrementing).
 * OPTIMIZED VERSION with caching
 * @param {string} commandName - The name of the command to fetch.
 * @returns {Promise<number>} - The current usage count for the command.
 */
async function getCommandUsage(commandName) {
    try {
        // Check cache first
        const cached = usageCache.get(commandName);
        if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
            return cached.count;
        }

        // Fetch from database
        const stats = await optimizedFindOne("stats",
            { type: "command_usage", command: commandName },
            { projection: { count: 1 } } // Only get count field
        );

        const count = stats ? stats.count : 0;

        // Update cache
        usageCache.set(commandName, {
            count: count,
            timestamp: Date.now()
        });

        return count;
    } catch (error) {
        console.error(`[commandUsage] Error getting usage for ${commandName}:`, error);

        // Return cached value if available
        const cached = usageCache.get(commandName);
        return cached ? cached.count : 0;
    }
}

module.exports = { incrementCommandUsage, getCommandUsage }; 