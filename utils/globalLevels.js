/**
 * Global Levels System (Enterprise-Grade Performance Optimized)
 * Handles global XP tracking, level progression, prestige system, and boosters
 * OPTIMIZED: Multi-tier LRU caching, batch processing, and intelligent database operations
 */

const { optimizedFind, optimizedFindOne, optimizedUpdateOne, optimizedInsertOne, optimizedAggregate, optimizedCountDocuments, optimizedBulkWrite } = require('./database-optimizer.js');
const { Decimal128 } = require('mongodb');
const { CacheFactory, registerCache } = require('./LRUCache.js');

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Enterprise-grade performance monitoring with environment awareness
const globalLevelsMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    batchOperations: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 15 * 60 * 1000 : 30 * 60 * 1000 // 15min dev, 30min prod
};

// OPTIMIZED: Multi-tier LRU caches with intelligent sizing
const globalLevelsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes
const globalUserCache = CacheFactory.createUserCache(); // 2000 entries, 5 minutes
const globalRankingsCache = CacheFactory.createHighFrequencyCache(); // 5000 entries, 2 minutes
const globalStatsCache = CacheFactory.createComputationCache(); // 1000 entries, 15 minutes for aggregated statistics

// Register caches for global cleanup
registerCache(globalLevelsCache);
registerCache(globalUserCache);
registerCache(globalRankingsCache);
registerCache(globalStatsCache);

/**
 * Initialize global user data if it doesn't exist
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Global user data
 */
async function initializeGlobalUser(userId) {
    try {

        
        const existingUser = await optimizedFindOne("global_user_data", { userId: userId });
        if (existingUser) {
            return existingUser;
        }
        
        const newUser = {
            userId: userId,
            globalExp: 0,
            globalLevel: 0,
            prestigeLevel: 0,
            prestigeMultiplier: 1.0,
            boosters: {
                expMultiplier: Decimal128.fromString("1.0"),
                dropChanceMultiplier: Decimal128.fromString("1.0")
            },
            starfall: {
                stars: 0,
                currentStreak: 0,
                longestStreak: 0,
                lastClaimDate: null,
                totalClaims: 0,
                itemsFound: 0
            },
            lastActivity: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        await optimizedInsertOne("global_user_data", newUser);
        console.log(`[globalLevels] ✅ Initialized global user data for ${userId}`);
        
        return newUser;
    } catch (error) {
        console.error('[globalLevels] Error initializing global user:', error);
        throw error;
    }
}

/**
 * Get cached global user data (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and metrics tracking
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Global user data
 */
async function getCachedGlobalUser(userId) {
    const startTime = Date.now();

    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = globalUserCache.get(userId);
    if (cached) {
        globalLevelsMetrics.cacheHits++;
        console.log(`[globalLevels] ⚡ Cache hit for user ${userId} (${Date.now() - startTime}ms)`);
        return cached;
    }

    try {
        globalLevelsMetrics.cacheMisses++;
        globalLevelsMetrics.databaseQueries++;

        let userData = await optimizedFindOne("global_user_data", { userId: userId });

        if (!userData) {
            userData = await initializeGlobalUser(userId);
        }

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        globalUserCache.set(userId, userData);

        // Enhanced performance monitoring
        const duration = Date.now() - startTime;
        globalLevelsMetrics.averageQueryTime =
            (globalLevelsMetrics.averageQueryTime * (globalLevelsMetrics.databaseQueries - 1) + duration) /
            globalLevelsMetrics.databaseQueries;

        // Environment-aware performance logging
        const slowThreshold = isDevelopment ? 50 : 100;
        if (duration > slowThreshold) {
            console.warn(`[globalLevels] ⚠️  SLOW USER FETCH: ${duration}ms for user ${userId} (avg: ${globalLevelsMetrics.averageQueryTime.toFixed(2)}ms)`);
        } else if (globalLevelsMetrics.verboseLogging || duration > 25) {
            console.log(`[globalLevels] ✅ User data fetched for ${userId} (${duration}ms) - cached for future access`);
        }

        return userData;
    } catch (error) {
        console.error(`[globalLevels] ❌ Error getting global user data for ${userId}:`, error);
        throw error;
    }
}

/**
 * Get cached global levels configuration
 * @returns {Promise<Array>} Array of global levels
 */
async function getCachedGlobalLevels() {
    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = globalLevelsCache.get('levels');
    if (cached) {
        return cached;
    }

    try {
        const startTime = Date.now();
        const levels = await optimizedFind("global_levels", { isActive: true }, {
            sort: { level: 1 }
        });

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        globalLevelsCache.set('levels', levels);

        // Performance logging
        const duration = Date.now() - startTime;
        if (duration > 100) {
            console.warn(`[globalLevels] SLOW LEVELS FETCH: ${duration}ms`);
        }

        return levels;
    } catch (error) {
        console.error('[globalLevels] Error getting global levels:', error);
        return [];
    }
}

/**
 * Calculate global level from XP with prestige multiplier
 * @param {number} globalExp - User's global XP
 * @param {number} prestigeLevel - User's prestige level
 * @param {Array} levels - Global levels configuration
 * @returns {Object} Level calculation result
 */
function calculateGlobalLevel(globalExp, prestigeLevel, levels) {
    if (!levels || levels.length === 0) {
        return { currentLevel: 0, nextLevelExp: null, levelIndex: -1, canPrestige: false };
    }
    
    // Calculate prestige multiplier (2x, 3x, 4x, 5x, 6x)
    const prestigeMultiplier = prestigeLevel > 0 ? (prestigeLevel + 1) : 1;
    
    let levelIndex = -1;
    let canPrestige = false;
    
    // Find current level based on adjusted XP requirements
    for (let i = levels.length - 1; i >= 0; i--) {
        const adjustedExpRequired = levels[i].expRequired * prestigeMultiplier;
        if (globalExp >= adjustedExpRequired) {
            levelIndex = i;
            break;
        }
    }
    
    // Check if user can prestige (reached max level)
    if (levelIndex === levels.length - 1) {
        canPrestige = true;
    }
    
    // Calculate next level XP
    let nextLevelExp = null;
    if (levelIndex + 1 < levels.length) {
        nextLevelExp = levels[levelIndex + 1].expRequired * prestigeMultiplier;
    }
    
    return {
        currentLevel: levelIndex >= 0 ? levelIndex + 1 : 0,
        nextLevelExp: nextLevelExp,
        levelIndex: levelIndex,
        canPrestige: canPrestige,
        prestigeMultiplier: prestigeMultiplier
    };
}

/**
 * Award global XP to a user
 * @param {string} userId - User ID
 * @param {number} expAmount - Amount of XP to award
 * @param {string} source - Source of XP (TEXT, VOICE)
 * @returns {Promise<Object>} Level up result
 */
async function awardGlobalExp(userId, expAmount, source = 'UNKNOWN') {
    try {

        const levels = await getCachedGlobalLevels();

        // Get current user data
        let userData = await getCachedGlobalUser(userId);

        // Apply booster multiplier to XP
        const expMultiplier = parseFloat(userData.boosters.expMultiplier.toString());
        const boostedExp = Math.floor(expAmount * expMultiplier);

        const oldExp = userData.globalExp;
        const oldLevel = calculateGlobalLevel(oldExp, userData.prestigeLevel, levels);

        // Update XP in database
        const updateResult = await optimizedUpdateOne("global_user_data",
            { userId: userId },
            {
                $inc: { globalExp: boostedExp },
                $set: {
                    lastActivity: new Date(),
                    updatedAt: new Date()
                }
            }
        );

        if (updateResult.matchedCount === 0) {
            // User doesn't exist, initialize and try again
            await initializeGlobalUser(userId);
            return await awardGlobalExp(userId, expAmount, source);
        }

        // Get updated data and invalidate cache
        globalUserCache.delete(userId);
        userData = await getCachedGlobalUser(userId);

        const newExp = userData.globalExp;
        const newLevel = calculateGlobalLevel(newExp, userData.prestigeLevel, levels);

        // Check for level up
        const leveledUp = newLevel.currentLevel > oldLevel.currentLevel;
        let levelRewards = null;

        if (leveledUp && newLevel.levelIndex >= 0) {
            const levelData = levels[newLevel.levelIndex];
            levelRewards = await processLevelRewards(userId, levelData);
        }

        console.log(`[globalLevels] ✅ Awarded ${boostedExp} global XP to ${userId} (${source})`);

        return {
            leveledUp: leveledUp,
            oldLevel: oldLevel.currentLevel,
            newLevel: newLevel.currentLevel,
            oldExp: oldExp,
            newExp: newExp,
            expGained: boostedExp,
            source: source,
            canPrestige: newLevel.canPrestige,
            levelRewards: levelRewards
        };

    } catch (error) {
        console.error('[globalLevels] Error awarding global XP:', error);
        throw error;
    }
}

/**
 * Process level rewards with batch optimization (Enterprise-Grade Optimized)
 * OPTIMIZED: Parallel item processing, single user data fetch, batch database operations
 * @param {string} userId - User ID
 * @param {Object} levelData - Level configuration data
 * @returns {Promise<Object>} Rewards processed
 */
async function processLevelRewards(userId, levelData) {
    const startTime = Date.now();
    const rewards = {
        items: [],
        xpBooster: null,
        dropBooster: null,
        stars: null
    };

    try {
        if (!levelData.rewards) {
            return rewards;
        }

        // Get user data once for all booster calculations
        const userData = await getCachedGlobalUser(userId);
        const batchUpdates = [];

        // OPTIMIZED: Process item rewards in parallel
        if (levelData.rewards.items && levelData.rewards.items.length > 0) {
            const { addItemToInventory } = require('./itemDrops.js');

            // Fetch all item data in parallel
            const itemDataPromises = levelData.rewards.items.map(itemId =>
                optimizedFindOne("custom_items", { id: itemId })
            );

            const itemDataResults = await Promise.allSettled(itemDataPromises);

            // Process items in parallel
            const itemPromises = itemDataResults.map(async (result, index) => {
                if (result.status === 'fulfilled' && result.value) {
                    try {
                        const awardedItem = await addItemToInventory(userId, null, result.value, 'LEVEL_UP');
                        return awardedItem;
                    } catch (itemError) {
                        console.error(`[globalLevels] Error awarding item ${levelData.rewards.items[index]}:`, itemError);
                        return null;
                    }
                } else {
                    console.warn(`[globalLevels] Item ${levelData.rewards.items[index]} not found for level reward`);
                    return null;
                }
            });

            const awardedItems = await Promise.allSettled(itemPromises);
            rewards.items = awardedItems
                .filter(result => result.status === 'fulfilled' && result.value)
                .map(result => result.value);
        }

        // OPTIMIZED: Prepare batch updates for boosters and stars
        const updateFields = {};

        if (levelData.rewards.xpBooster) {
            const currentMultiplier = parseFloat(userData.boosters.expMultiplier.toString());
            const newMultiplier = currentMultiplier * levelData.rewards.xpBooster;
            updateFields['boosters.expMultiplier'] = Decimal128.fromString(newMultiplier.toString());
            rewards.xpBooster = levelData.rewards.xpBooster;
        }

        if (levelData.rewards.dropBooster) {
            const currentMultiplier = parseFloat(userData.boosters.dropChanceMultiplier.toString());
            const newMultiplier = currentMultiplier * levelData.rewards.dropBooster;
            updateFields['boosters.dropChanceMultiplier'] = Decimal128.fromString(newMultiplier.toString());
            rewards.dropBooster = levelData.rewards.dropBooster;
        }

        // Combine booster and star updates into single database operation
        if (levelData.rewards.stars) {
            rewards.stars = levelData.rewards.stars;
        }

        // OPTIMIZED: Single database update for all rewards
        if (Object.keys(updateFields).length > 0 || levelData.rewards.stars) {
            const updateOperation = {};

            if (Object.keys(updateFields).length > 0) {
                updateOperation.$set = updateFields;
            }

            if (levelData.rewards.stars) {
                updateOperation.$inc = { 'starfall.stars': levelData.rewards.stars };
            }

            await optimizedUpdateOne("global_user_data", { userId: userId }, updateOperation);

            // Invalidate cache once
            globalUserCache.delete(userId);
        }

        const duration = Date.now() - startTime;
        console.log(`[globalLevels] ✅ Processed level rewards for ${userId} in ${duration}ms:`, rewards);

        if (duration > 200) {
            console.warn(`[globalLevels] ⚠️  Slow reward processing: ${duration}ms for user ${userId}`);
        }

        return rewards;

    } catch (error) {
        console.error('[globalLevels] ❌ Error processing level rewards:', error);
        return rewards;
    }
}

/**
 * Get user's current booster multipliers
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Booster multipliers
 */
async function getUserBoosters(userId) {
    try {
        const userData = await getCachedGlobalUser(userId);
        return {
            expMultiplier: parseFloat(userData.boosters.expMultiplier.toString()),
            dropChanceMultiplier: parseFloat(userData.boosters.dropChanceMultiplier.toString())
        };
    } catch (error) {
        console.error('[globalLevels] Error getting user boosters:', error);
        return {
            expMultiplier: 1.0,
            dropChanceMultiplier: 1.0
        };
    }
}

/**
 * Perform prestige for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Prestige result
 */
async function performPrestige(userId) {
    try {

        const userData = await getCachedGlobalUser(userId);
        const levels = await getCachedGlobalLevels();

        const currentLevel = calculateGlobalLevel(userData.globalExp, userData.prestigeLevel, levels);

        if (!currentLevel.canPrestige) {
            throw new Error('User cannot prestige yet');
        }

        // Reset XP and level, increment prestige
        const newPrestigeLevel = userData.prestigeLevel + 1;
        const newPrestigeMultiplier = newPrestigeLevel + 1; // 2x, 3x, 4x, 5x, 6x

        await optimizedUpdateOne("global_user_data",
            { userId: userId },
            {
                $set: {
                    globalExp: 0,
                    globalLevel: 0,
                    prestigeLevel: newPrestigeLevel,
                    prestigeMultiplier: newPrestigeMultiplier,
                    updatedAt: new Date()
                }
                // Note: boosters are preserved through prestige
            }
        );

        // Invalidate cache
        globalUserCache.delete(userId);

        console.log(`[globalLevels] ✅ User ${userId} prestiged to level ${newPrestigeLevel}`);

        return {
            success: true,
            newPrestigeLevel: newPrestigeLevel,
            newPrestigeMultiplier: newPrestigeMultiplier,
            preservedBoosters: userData.boosters
        };

    } catch (error) {
        console.error('[globalLevels] Error performing prestige:', error);
        throw error;
    }
}

/**
 * Get global rankings with efficient aggregation
 * @param {number} limit - Number of results to return
 * @returns {Promise<Array>} Array of ranked users
 */
async function getGlobalRankings(limit = 50) {
    const cacheKey = `rankings_${limit}`;
    // OPTIMIZED: Use LRU cache with automatic TTL and eviction
    const cached = globalRankingsCache.get(cacheKey);
    if (cached) {
        return cached;
    }

    try {
        const startTime = Date.now();


        // Use aggregation pipeline for efficient ranking
        const rankings = await optimizedAggregate("global_user_data", [
            { $match: { globalExp: { $gt: 0 } } },
            { $sort: { globalExp: -1 } },
            { $limit: limit },
            {
                $project: {
                    userId: 1,
                    globalExp: 1,
                    globalLevel: 1,
                    prestigeLevel: 1,
                    prestigeMultiplier: 1
                }
            }
        ]);

        // Add rank numbers
        const rankedResults = rankings.map((user, index) => ({
            rank: index + 1,
            userId: user.userId,
            globalExp: user.globalExp,
            globalLevel: user.globalLevel,
            prestigeLevel: user.prestigeLevel,
            prestigeMultiplier: user.prestigeMultiplier
        }));

        // OPTIMIZED: Cache using LRU cache (automatic TTL and size management)
        globalRankingsCache.set(cacheKey, rankedResults);

        // Performance logging
        const duration = Date.now() - startTime;
        if (duration > 100) {
            console.warn(`[globalLevels] SLOW RANKINGS FETCH: ${duration}ms for limit ${limit}`);
        }

        return rankedResults;

    } catch (error) {
        console.error('[globalLevels] Error getting global rankings:', error);
        return [];
    }
}

/**
 * Get a specific user's global rank efficiently (Enterprise-Grade Optimized)
 * OPTIMIZED: Uses optimizedCountDocuments instead of fetching full documents
 * @param {string} userId - User ID
 * @returns {Promise<Object>} User's rank information
 */
async function getUserGlobalRank(userId) {
    const startTime = Date.now();
    const cacheKey = `user_rank_${userId}`;

    try {
        // Check cache first
        const cached = globalStatsCache.get(cacheKey);
        if (cached) {
            globalLevelsMetrics.cacheHits++;
            console.log(`[globalLevels] ⚡ Rank cache hit for user ${userId} (${Date.now() - startTime}ms)`);
            return cached;
        }

        globalLevelsMetrics.cacheMisses++;

        // Get user's data
        const userData = await getCachedGlobalUser(userId);

        if (userData.globalExp === 0) {
            const result = { rank: null, globalExp: 0, total: 0 };
            globalStatsCache.set(cacheKey, result);
            return result;
        }

        // OPTIMIZED: Use optimizedCountDocuments instead of fetching full documents
        const [higherCount, totalWithExp] = await Promise.all([
            optimizedCountDocuments("global_user_data", {
                globalExp: { $gt: userData.globalExp }
            }),
            optimizedCountDocuments("global_user_data", {
                globalExp: { $gt: 0 }
            })
        ]);

        const result = {
            rank: higherCount + 1,
            globalExp: userData.globalExp,
            globalLevel: userData.globalLevel,
            prestigeLevel: userData.prestigeLevel,
            total: totalWithExp
        };

        // Cache the result
        globalStatsCache.set(cacheKey, result);

        const duration = Date.now() - startTime;
        console.log(`[globalLevels] ✅ User rank calculated for ${userId}: #${result.rank}/${result.total} (${duration}ms)`);

        if (duration > 100) {
            console.warn(`[globalLevels] ⚠️  Slow rank calculation: ${duration}ms for user ${userId}`);
        }

        return result;

    } catch (error) {
        console.error('[globalLevels] ❌ Error getting user global rank:', error);
        return { rank: null, globalExp: 0, total: 0 };
    }
}

/**
 * Invalidate all caches (used when levels are modified)
 */
function invalidateAllCaches() {
    // OPTIMIZED: Use LRU cache clear methods
    globalLevelsCache.clear();
    globalUserCache.clear();
    globalRankingsCache.clear();
    console.log('[globalLevels] 🗑️ Cleared all global levels caches');
}

/**
 * Invalidate specific user's cache
 * @param {string} userId - User ID to invalidate
 */
function invalidateUserCache(userId) {
    // OPTIMIZED: Use LRU cache delete method
    globalUserCache.delete(userId);
}

/**
 * Get comprehensive cache statistics with performance metrics (Enterprise-Grade)
 * OPTIMIZED: Enhanced analytics with performance insights and recommendations
 * @returns {Object} Comprehensive cache and performance statistics
 */
function getCacheStats() {
    const cacheHitRate = globalLevelsMetrics.cacheHits + globalLevelsMetrics.cacheMisses > 0 ?
        (globalLevelsMetrics.cacheHits / (globalLevelsMetrics.cacheHits + globalLevelsMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: globalLevelsMetrics.cacheHits,
            cacheMisses: globalLevelsMetrics.cacheMisses,
            databaseQueries: globalLevelsMetrics.databaseQueries,
            averageQueryTime: `${globalLevelsMetrics.averageQueryTime.toFixed(2)}ms`,
            batchOperations: globalLevelsMetrics.batchOperations,
            lastOptimization: new Date(globalLevelsMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        caches: {
            globalLevels: globalLevelsCache.getStats(),
            globalUsers: globalUserCache.getStats(),
            globalRankings: globalRankingsCache.getStats(),
            globalStats: globalStatsCache.getStats()
        },

        // Memory usage breakdown
        totalMemoryUsage: {
            globalLevels: globalLevelsCache.getStats().memoryUsage,
            globalUsers: globalUserCache.getStats().memoryUsage,
            globalRankings: globalRankingsCache.getStats().memoryUsage,
            globalStats: globalStatsCache.getStats().memoryUsage,
            total: globalLevelsCache.getStats().memoryUsage +
                   globalUserCache.getStats().memoryUsage +
                   globalRankingsCache.getStats().memoryUsage +
                   globalStatsCache.getStats().memoryUsage
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization',
            recommendations: generatePerformanceRecommendations(cacheHitRate, globalLevelsMetrics)
        }
    };
}

/**
 * Generate performance recommendations based on metrics
 * @param {number} cacheHitRate - Current cache hit rate
 * @param {Object} metrics - Performance metrics
 * @returns {Array} Array of recommendations
 */
function generatePerformanceRecommendations(cacheHitRate, metrics) {
    const recommendations = [];

    if (cacheHitRate < 60) {
        recommendations.push('Consider increasing cache TTL or size for better hit rates');
    }

    if (metrics.averageQueryTime > 100) {
        recommendations.push('Database queries are slow - consider adding indexes or optimizing queries');
    }

    if (metrics.databaseQueries > metrics.cacheHits * 2) {
        recommendations.push('High database query ratio - consider caching more aggressively');
    }

    if (recommendations.length === 0) {
        recommendations.push('System performance is optimal');
    }

    return recommendations;
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 * OPTIMIZED: Automatic performance analysis and cache optimization
 */
function performanceCleanupAndOptimization() {
    const now = Date.now();

    // Update optimization timestamp
    globalLevelsMetrics.lastOptimization = now;

    // Log performance statistics
    const stats = getCacheStats();
    console.log(`[globalLevels] 📊 Performance Report:`);
    console.log(`[globalLevels]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
    console.log(`[globalLevels]   Database Queries: ${stats.performance.databaseQueries}`);
    console.log(`[globalLevels]   Average Query Time: ${stats.performance.averageQueryTime}`);
    console.log(`[globalLevels]   Total Memory Usage: ${(stats.totalMemoryUsage.total / 1024 / 1024).toFixed(2)}MB`);
    console.log(`[globalLevels]   System Health: ${stats.systemHealth.status}`);

    // Performance recommendations
    stats.systemHealth.recommendations.forEach(rec => {
        if (rec !== 'System performance is optimal') {
            console.warn(`[globalLevels] ⚠️  ${rec}`);
        }
    });

    return stats;
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, globalLevelsMetrics.performanceReportInterval);



/**
 * Get current level rewards for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} Current level rewards or null if no level/rewards
 */
async function getCurrentLevelRewards(userId) {
    try {
        const userData = await getCachedGlobalUser(userId);
        const levels = await getCachedGlobalLevels();

        if (!levels || levels.length === 0) {
            return null;
        }

        const levelCalc = calculateGlobalLevel(userData.globalExp, userData.prestigeLevel, levels);

        if (levelCalc.levelIndex >= 0) {
            const levelData = levels[levelCalc.levelIndex];
            return levelData.rewards || null;
        }

        return null;
    } catch (error) {
        console.error('[globalLevels] Error getting current level rewards:', error);
        return null;
    }
}

module.exports = {
    // Core functions
    initializeGlobalUser,
    getCachedGlobalUser,
    getCachedGlobalLevels,
    calculateGlobalLevel,
    awardGlobalExp,
    processLevelRewards,
    getUserBoosters,
    performPrestige,
    getGlobalRankings,
    getUserGlobalRank,
    getCurrentLevelRewards,

    // Enhanced optimization functions
    invalidateAllCaches,
    invalidateUserCache,
    getCacheStats,
    performanceCleanupAndOptimization,
    generatePerformanceRecommendations,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...globalLevelsMetrics })
};
