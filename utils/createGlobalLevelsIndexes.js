/**
 * Database Index Creation for Global Levels System
 * Run this script to create optimized indexes for global levels performance
 */

const { mongoClient } = require('../mongo/client.js');

/**
 * Create all necessary indexes for global levels system
 */
async function createGlobalLevelsIndexes() {
    try {
        console.log('[globalLevelsIndexes] 🔧 Creating global levels database indexes...');
        
        const db = mongoClient.db('test');
        
        // Global user data indexes
        const globalUserCol = db.collection('global_user_data');
        
        console.log('[globalLevelsIndexes] Creating global_user_data indexes...');
        
        // Unique index on userId
        await globalUserCol.createIndex(
            { userId: 1 }, 
            { unique: true, name: 'userId_unique' }
        );
        
        // Index for global rankings (most important query)
        await globalUserCol.createIndex(
            { globalExp: -1 }, 
            { name: 'globalExp_desc' }
        );
        
        // Compound index for level-based queries
        await globalUserCol.createIndex(
            { globalLevel: -1, globalExp: -1 }, 
            { name: 'globalLevel_globalExp_desc' }
        );
        
        // Index for prestige queries
        await globalUserCol.createIndex(
            { prestigeLevel: -1, globalExp: -1 }, 
            { name: 'prestigeLevel_globalExp_desc' }
        );
        
        // Index for activity tracking
        await globalUserCol.createIndex(
            { lastActivity: -1 }, 
            { name: 'lastActivity_desc' }
        );
        
        // Global levels configuration indexes
        const globalLevelsCol = db.collection('global_levels');
        
        console.log('[globalLevelsIndexes] Creating global_levels indexes...');
        
        // Unique index on level number
        await globalLevelsCol.createIndex(
            { level: 1 }, 
            { unique: true, name: 'level_unique' }
        );
        
        // Index for XP requirement queries
        await globalLevelsCol.createIndex(
            { expRequired: 1 }, 
            { name: 'expRequired_asc' }
        );
        
        // Compound index for active levels
        await globalLevelsCol.createIndex(
            { isActive: 1, level: 1 }, 
            { name: 'isActive_level_asc' }
        );
        
        // Index for reward queries
        await globalLevelsCol.createIndex(
            { 'rewards.itemId': 1 }, 
            { sparse: true, name: 'rewards_itemId' }
        );
        
        console.log('[globalLevelsIndexes] ✅ All global levels indexes created successfully!');
        
        // Display index information
        await displayIndexInfo();
        
    } catch (error) {
        console.error('[globalLevelsIndexes] ❌ Error creating indexes:', error);
        throw error;
    }
}

/**
 * Display information about created indexes
 */
async function displayIndexInfo() {
    try {
        const db = mongoClient.db('test');
        
        console.log('\n[globalLevelsIndexes] 📊 Index Information:');
        
        // Global user data indexes
        const globalUserIndexes = await db.collection('global_user_data').indexes();
        console.log('\nGlobal User Data Indexes:');
        globalUserIndexes.forEach(index => {
            console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
        // Global levels indexes
        const globalLevelsIndexes = await db.collection('global_levels').indexes();
        console.log('\nGlobal Levels Indexes:');
        globalLevelsIndexes.forEach(index => {
            console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
        });
        
    } catch (error) {
        console.error('[globalLevelsIndexes] Error displaying index info:', error);
    }
}

/**
 * Drop all global levels indexes (for cleanup/recreation)
 */
async function dropGlobalLevelsIndexes() {
    try {
        console.log('[globalLevelsIndexes] 🗑️ Dropping global levels indexes...');
        
        const db = mongoClient.db('test');
        
        // Drop global user data indexes (except _id)
        const globalUserCol = db.collection('global_user_data');
        const globalUserIndexes = await globalUserCol.indexes();
        
        for (const index of globalUserIndexes) {
            if (index.name !== '_id_') {
                await globalUserCol.dropIndex(index.name);
                console.log(`  - Dropped: ${index.name}`);
            }
        }
        
        // Drop global levels indexes (except _id)
        const globalLevelsCol = db.collection('global_levels');
        const globalLevelsIndexes = await globalLevelsCol.indexes();
        
        for (const index of globalLevelsIndexes) {
            if (index.name !== '_id_') {
                await globalLevelsCol.dropIndex(index.name);
                console.log(`  - Dropped: ${index.name}`);
            }
        }
        
        console.log('[globalLevelsIndexes] ✅ All global levels indexes dropped!');
        
    } catch (error) {
        console.error('[globalLevelsIndexes] Error dropping indexes:', error);
        throw error;
    }
}

/**
 * Check if global levels indexes exist
 */
async function checkGlobalLevelsIndexes() {
    try {
        const db = mongoClient.db('test');
        
        const requiredIndexes = {
            'global_user_data': [
                'userId_unique',
                'globalExp_desc',
                'globalLevel_globalExp_desc',
                'prestigeLevel_globalExp_desc',
                'lastActivity_desc'
            ],
            'global_levels': [
                'level_unique',
                'expRequired_asc',
                'isActive_level_asc',
                'rewards_itemId'
            ]
        };
        
        let allIndexesExist = true;
        
        for (const [collectionName, indexNames] of Object.entries(requiredIndexes)) {
            const collection = db.collection(collectionName);
            const existingIndexes = await collection.indexes();
            const existingIndexNames = existingIndexes.map(idx => idx.name);
            
            console.log(`\n[globalLevelsIndexes] Checking ${collectionName} indexes:`);
            
            for (const indexName of indexNames) {
                const exists = existingIndexNames.includes(indexName);
                console.log(`  - ${indexName}: ${exists ? '✅' : '❌'}`);
                if (!exists) allIndexesExist = false;
            }
        }
        
        return allIndexesExist;
        
    } catch (error) {
        console.error('[globalLevelsIndexes] Error checking indexes:', error);
        return false;
    }
}

module.exports = {
    createGlobalLevelsIndexes,
    dropGlobalLevelsIndexes,
    checkGlobalLevelsIndexes,
    displayIndexInfo
};

// If run directly, create the indexes
if (require.main === module) {
    (async () => {
        try {
            await createGlobalLevelsIndexes();
            process.exit(0);
        } catch (error) {
            console.error('Failed to create indexes:', error);
            process.exit(1);
        }
    })();
}
