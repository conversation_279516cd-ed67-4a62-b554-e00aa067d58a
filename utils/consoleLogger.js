const { EmbedBuilder } = require('discord.js');
const { optimizedFind } = require('./database-optimizer.js');
const { LOG_COLORS } = require('./colors.js');
const { CacheFactory, registerCache } = require('./LRUCache.js');

/**
 * Console Logger System (Enterprise-Grade Performance Optimized)
 * Optimizes guild logging configuration lookups with comprehensive caching
 * OPTIMIZED: LRU caching, performance monitoring, and environment-aware optimization
 */

// Environment-aware configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Enterprise-grade performance monitoring
const consoleLoggerMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    databaseQueries: 0,
    averageQueryTime: 0,
    logsProcessed: 0,
    channelsServed: 0,
    batchesProcessed: 0,
    immediateLogsProcessed: 0,
    lastOptimization: Date.now(),
    verboseLogging: isDevelopment,
    performanceReportInterval: isDevelopment ? 10 * 60 * 1000 : 20 * 60 * 1000
};

// OPTIMIZED: LRU cache for guild logging configurations
const guildLoggingConfigCache = CacheFactory.createGuildCache(); // 500 entries, 10 minutes for guild configs

// Register cache for global cleanup
registerCache(guildLoggingConfigCache);

// Log levels
const LOG_LEVELS = {
    ERROR: { priority: 0, color: LOG_COLORS.ERROR, emoji: '🔴' },
    WARN: { priority: 1, color: LOG_COLORS.WARNING, emoji: '🟡' },
    INFO: { priority: 2, color: LOG_COLORS.SUCCESS, emoji: '🟢' },
    DEBUG: { priority: 3, color: LOG_COLORS.INFO, emoji: '🔵' }
};

// Message queue for batching
let messageQueue = [];
let batchTimeout = null;
const BATCH_DELAY = 15000; // 15 seconds
const MAX_EMBED_LENGTH = 4000;

/**
 * Log a message to Discord console logging channel
 * @param {string} level - Log level (ERROR, WARN, INFO, DEBUG)
 * @param {string} category - Category/module name (e.g., 'voiceExp', 'dehoist')
 * @param {string} message - Log message
 * @param {Object} [client] - Discord client
 */
async function logToDiscord(level, category, message, client = null) {
    // Skip if invalid level
    if (!LOG_LEVELS[level]) return;

    // Get current timestamp
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    
    // Format log entry
    const logEntry = {
        level,
        category,
        message,
        timestamp,
        priority: LOG_LEVELS[level].priority
    };

    // For ERROR level, send immediately
    if (level === 'ERROR') {
        await sendLogImmediately(logEntry, client);
        return;
    }

    // For other levels, add to batch queue
    messageQueue.push(logEntry);

    // Set up batch processing if not already scheduled
    if (!batchTimeout) {
        batchTimeout = setTimeout(() => {
            processBatch(client);
        }, BATCH_DELAY);
    }
}

/**
 * Send error logs immediately (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced performance monitoring and error tracking
 */
async function sendLogImmediately(logEntry, client) {
    const startTime = Date.now();

    try {
        const channels = await getConsoleLogChannels(client);
        if (channels.length === 0) return;

        const embed = createLogEmbed([logEntry]);

        for (const channel of channels) {
            try {
                await channel.send({ embeds: [embed] });
            } catch (error) {
                console.error(`[consoleLogger] ❌ Failed to send to channel ${channel.name}:`, error.message);
            }
        }

        const duration = Date.now() - startTime;
        if (consoleLoggerMetrics.verboseLogging || duration > 200) {
            console.log(`[consoleLogger] ✅ Immediate log sent to ${channels.length} channels in ${duration}ms`);
        }

        consoleLoggerMetrics.immediateLogsProcessed++;
        consoleLoggerMetrics.logsProcessed++;
    } catch (error) {
        console.error('[consoleLogger] ❌ Error in sendLogImmediately:', error);
    }
}

/**
 * Process batched logs (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced performance monitoring and batch processing tracking
 */
async function processBatch(client) {
    const startTime = Date.now();

    if (messageQueue.length === 0) {
        batchTimeout = null;
        return;
    }

    try {
        const channels = await getConsoleLogChannels(client);
        if (channels.length === 0) {
            messageQueue = [];
            batchTimeout = null;
            return;
        }

        // Sort by priority (errors first) then by timestamp
        const sortedLogs = messageQueue.sort((a, b) => {
            if (a.priority !== b.priority) return a.priority - b.priority;
            return new Date(a.timestamp) - new Date(b.timestamp);
        });

        // Group similar logs to reduce spam
        const groupedLogs = groupSimilarLogs(sortedLogs);

        // Split into chunks that fit in embeds
        const chunks = chunkLogs(groupedLogs);

        // Send each chunk
        for (const chunk of chunks) {
            const embed = createLogEmbed(chunk);

            for (const channel of channels) {
                try {
                    await channel.send({ embeds: [embed] });
                    // Small delay between messages to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 100));
                } catch (error) {
                    console.error(`[consoleLogger] ❌ Failed to send to channel ${channel.name}:`, error.message);
                }
            }
        }

        const duration = Date.now() - startTime;
        if (consoleLoggerMetrics.verboseLogging || duration > 300) {
            console.log(`[consoleLogger] ✅ Batch processed: ${messageQueue.length} logs in ${chunks.length} chunks to ${channels.length} channels (${duration}ms)`);
        }

        consoleLoggerMetrics.batchesProcessed++;
        consoleLoggerMetrics.logsProcessed += messageQueue.length;

        // Clear queue and reset timeout
        messageQueue = [];
        batchTimeout = null;

    } catch (error) {
        console.error('[consoleLogger] ❌ Error in processBatch:', error);
        messageQueue = [];
        batchTimeout = null;
    }
}

/**
 * Group similar log messages to reduce spam
 */
function groupSimilarLogs(logs) {
    const grouped = [];
    const groups = new Map();

    for (const log of logs) {
        // Create a key for grouping similar messages
        const groupKey = `${log.level}:${log.category}:${log.message.split(' ')[0]}`;
        
        if (groups.has(groupKey)) {
            const group = groups.get(groupKey);
            group.count++;
            group.lastTimestamp = log.timestamp;
        } else {
            const group = {
                ...log,
                count: 1,
                lastTimestamp: log.timestamp
            };
            groups.set(groupKey, group);
            grouped.push(group);
        }
    }

    return grouped;
}

/**
 * Split logs into chunks that fit in Discord embeds
 */
function chunkLogs(logs) {
    const chunks = [];
    let currentChunk = [];
    let currentLength = 0;

    for (const log of logs) {
        const logText = formatLogMessage(log);
        const logLength = logText.length + 50; // Buffer for embed formatting

        if (currentLength + logLength > MAX_EMBED_LENGTH && currentChunk.length > 0) {
            chunks.push([...currentChunk]);
            currentChunk = [log];
            currentLength = logLength;
        } else {
            currentChunk.push(log);
            currentLength += logLength;
        }
    }

    if (currentChunk.length > 0) {
        chunks.push(currentChunk);
    }

    return chunks;
}

/**
 * Create Discord embed for log messages
 */
function createLogEmbed(logs) {
    if (logs.length === 0) return null;

    // Determine embed color based on highest priority log
    const highestPriority = Math.min(...logs.map(log => LOG_LEVELS[log.level].priority));
    const color = Object.values(LOG_LEVELS).find(level => level.priority === highestPriority).color;

    const embed = new EmbedBuilder()
        .setTitle('🤖 Bot Console Logs')
        .setColor(color)
        .setTimestamp();

    // Build description from log messages
    const description = logs.map(log => formatLogMessage(log)).join('\n');
    embed.setDescription(description);

    return embed;
}

/**
 * Format individual log message
 */
function formatLogMessage(log) {
    const emoji = LOG_LEVELS[log.level].emoji;
    const time = log.timestamp.substring(11); // Just the time part
    
    let message = log.message;
    if (log.count > 1) {
        message += ` *(${log.count}x)*`;
    }

    return `${emoji} \`${time}\` **[${log.category}]** ${message}`;
}

/**
 * Get console log channels from guild configurations (Enterprise-Grade Optimized)
 * OPTIMIZED: Enhanced caching with performance monitoring and LRU cache integration
 * @param {Object} client - Discord client
 * @returns {Promise<Array>} Array of console log channels
 */
async function getConsoleLogChannels(client) {
    const startTime = Date.now();
    const cacheKey = 'console_log_channels';

    try {
        if (!client) return [];

        // OPTIMIZED: Check LRU cache first
        const cached = guildLoggingConfigCache.get(cacheKey);
        if (cached) {
            consoleLoggerMetrics.cacheHits++;
            if (consoleLoggerMetrics.verboseLogging) {
                console.log(`[consoleLogger] ⚡ Console log channels cache hit (${Date.now() - startTime}ms)`);
            }

            // Validate cached channels are still accessible
            const validChannels = cached.filter(channel => {
                try {
                    return channel && channel.isTextBased() && client.guilds.cache.has(channel.guild.id);
                } catch {
                    return false;
                }
            });

            if (validChannels.length === cached.length) {
                consoleLoggerMetrics.channelsServed += validChannels.length;
                return validChannels;
            }

            // Cache is stale, remove it
            guildLoggingConfigCache.delete(cacheKey);
        }

        consoleLoggerMetrics.cacheMisses++;
        consoleLoggerMetrics.databaseQueries++;

        const guilds = await optimizedFind("guilds", {
            "logs.enabled": true,
            "logs.channels": {
                $elemMatch: {
                    "events": "botConsole"
                }
            }
        });

        const channels = [];
        for (const guildData of guilds) {
            const guild = client.guilds.cache.get(guildData.id);
            if (!guild) continue;

            const consoleChannels = guildData.logs.channels
                .filter(ch => ch.events.includes("botConsole"))
                .map(ch => guild.channels.cache.get(ch.id))
                .filter(ch => ch && ch.isTextBased());

            channels.push(...consoleChannels);
        }

        // Cache the result
        guildLoggingConfigCache.set(cacheKey, channels);

        const duration = Date.now() - startTime;
        consoleLoggerMetrics.averageQueryTime =
            (consoleLoggerMetrics.averageQueryTime * (consoleLoggerMetrics.databaseQueries - 1) + duration) /
            consoleLoggerMetrics.databaseQueries;

        if (consoleLoggerMetrics.verboseLogging || duration > 100) {
            console.log(`[consoleLogger] ✅ Console log channels fetched: ${channels.length} channels in ${duration}ms - cached for future access`);
        }

        consoleLoggerMetrics.channelsServed += channels.length;
        return channels;
    } catch (error) {
        console.error('[consoleLogger] ❌ Error getting console log channels:', error);
        return [];
    }
}

/**
 * Convenience functions for different log levels
 */
const logger = {
    error: (category, message, client) => logToDiscord('ERROR', category, message, client),
    warn: (category, message, client) => logToDiscord('WARN', category, message, client),
    info: (category, message, client) => logToDiscord('INFO', category, message, client),
    debug: (category, message, client) => logToDiscord('DEBUG', category, message, client)
};

/**
 * Wrap a function with error logging
 * @param {Function} fn - Function to wrap
 * @param {string} category - Log category
 * @param {Object} client - Discord client
 * @returns {Function} Wrapped function
 */
function withErrorLogging(fn, category, client) {
    return async (...args) => {
        try {
            return await fn(...args);
        } catch (error) {
            console.error(`[${category}] Error:`, error);
            logger.error(category, `${error.message}`, client);
            throw error; // Re-throw to maintain original behavior
        }
    };
}

/**
 * Get comprehensive console logger performance data (Enterprise-Grade)
 * @returns {Object} Comprehensive console logger performance data
 */
function getConsoleLoggerStats() {
    const cacheHitRate = consoleLoggerMetrics.cacheHits + consoleLoggerMetrics.cacheMisses > 0 ?
        (consoleLoggerMetrics.cacheHits / (consoleLoggerMetrics.cacheHits + consoleLoggerMetrics.cacheMisses) * 100).toFixed(2) : 0;

    return {
        // Performance metrics
        performance: {
            cacheHitRate: `${cacheHitRate}%`,
            cacheHits: consoleLoggerMetrics.cacheHits,
            cacheMisses: consoleLoggerMetrics.cacheMisses,
            databaseQueries: consoleLoggerMetrics.databaseQueries,
            averageQueryTime: `${consoleLoggerMetrics.averageQueryTime.toFixed(2)}ms`,
            logsProcessed: consoleLoggerMetrics.logsProcessed,
            channelsServed: consoleLoggerMetrics.channelsServed,
            batchesProcessed: consoleLoggerMetrics.batchesProcessed,
            immediateLogsProcessed: consoleLoggerMetrics.immediateLogsProcessed,
            lastOptimization: new Date(consoleLoggerMetrics.lastOptimization).toISOString()
        },

        // Cache statistics
        cache: guildLoggingConfigCache.getStats(),

        // Queue status
        queueStatus: {
            currentQueueSize: messageQueue.length,
            batchTimeoutActive: batchTimeout !== null,
            batchDelay: BATCH_DELAY
        },

        // System health assessment
        systemHealth: {
            status: cacheHitRate > 80 ? 'excellent' : cacheHitRate > 60 ? 'good' : 'needs optimization'
        }
    };
}

/**
 * Performance monitoring and optimization (Enterprise-Grade Maintenance)
 */
function performanceCleanupAndOptimization() {
    consoleLoggerMetrics.lastOptimization = Date.now();

    const stats = getConsoleLoggerStats();
    if (consoleLoggerMetrics.verboseLogging) {
        console.log(`[consoleLogger] 📊 Performance Report:`);
        console.log(`[consoleLogger]   Cache Hit Rate: ${stats.performance.cacheHitRate}`);
        console.log(`[consoleLogger]   Logs Processed: ${stats.performance.logsProcessed}`);
        console.log(`[consoleLogger]   Channels Served: ${stats.performance.channelsServed}`);
        console.log(`[consoleLogger]   Batches Processed: ${stats.performance.batchesProcessed}`);
        console.log(`[consoleLogger]   Immediate Logs: ${stats.performance.immediateLogsProcessed}`);
        console.log(`[consoleLogger]   Average Query Time: ${stats.performance.averageQueryTime}`);
        console.log(`[consoleLogger]   Current Queue Size: ${stats.queueStatus.currentQueueSize}`);
        console.log(`[consoleLogger]   System Health: ${stats.systemHealth.status}`);
    }

    return stats;
}

/**
 * Clear console logger cache (Enterprise-Grade Cache Management)
 */
function clearConsoleLoggerCache() {
    guildLoggingConfigCache.clear();
    console.log('[consoleLogger] 🗑️ Cleared console logger cache');
}

/**
 * Force process current batch (for testing/debugging)
 */
async function forceProcessBatch(client) {
    if (batchTimeout) {
        clearTimeout(batchTimeout);
        batchTimeout = null;
    }
    await processBatch(client);
}

// Environment-aware automatic performance monitoring
setInterval(performanceCleanupAndOptimization, consoleLoggerMetrics.performanceReportInterval);

module.exports = {
    // Core functions
    logToDiscord,
    logger,
    withErrorLogging,
    LOG_LEVELS,

    // Enhanced optimization functions
    getConsoleLogChannels,
    getConsoleLoggerStats,
    performanceCleanupAndOptimization,
    clearConsoleLoggerCache,
    forceProcessBatch,

    // Performance metrics (read-only)
    getPerformanceMetrics: () => ({ ...consoleLoggerMetrics })
};
