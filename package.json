{"name": "17", "version": "1.0.0", "description": "a bot from ??", "main": "index.js", "scripts": {"test": "node tests/comprehensive-test-runner.js", "test:basic": "node tests/run-interaction-tests.js", "test:scan": "node -e \"const scanner = require('./tests/interaction-scanner.js'); const s = new scanner(); s.scanCodebase().then(() => s.printResults());\"", "test:watch": "nodemon --watch . --ext js --exec \"npm test\""}, "author": "", "license": "ISC", "dependencies": {"discord.js": "^14.21.0", "dotenv": "^16.4.5", "mongodb": "^6.10.0", "node-fetch": "^3.3.2"}}