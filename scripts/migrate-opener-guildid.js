/**
 * Migration script to add guildId to existing opener_threads documents
 * This fixes the privacy issue where threads from all servers were visible
 */

require('dotenv').config();
const { mongoClient, connect } = require('../mongo/client.js');
const { Client, GatewayIntentBits } = require('discord.js');

const client = new Client({
    intents: [GatewayIntentBits.Guilds]
});

async function migrateOpenerGuildIds() {
    console.log('🔄 Starting opener guildId migration...\n');
    
    try {
        // Connect to MongoDB
        await connect();
        console.log('📡 Connected to MongoDB');
        
        // Login to Discord
        await client.login(process.env.TOKEN);
        console.log('🤖 Connected to Discord\n');
        
        const col = mongoClient.db('test').collection('opener_threads');
        
        // Find all threads without guildId
        const threadsWithoutGuildId = await col.find({ guildId: { $exists: false } }).toArray();
        console.log(`📊 Found ${threadsWithoutGuildId.length} threads without guildId\n`);
        
        if (threadsWithoutGuildId.length === 0) {
            console.log('✅ No migration needed - all threads already have guildId');
            process.exit(0);
        }
        
        let updated = 0;
        let failed = 0;
        
        for (const threadDoc of threadsWithoutGuildId) {
            try {
                // Try to fetch the thread from Discord to get its guildId
                const thread = await client.channels.fetch(threadDoc.threadId);
                
                if (thread && thread.guild) {
                    // Update the document with guildId
                    await col.updateOne(
                        { _id: threadDoc._id },
                        { $set: { guildId: thread.guild.id } }
                    );
                    
                    console.log(`✅ Updated thread ${threadDoc.threadId} with guildId ${thread.guild.id}`);
                    updated++;
                } else {
                    console.log(`❌ Thread ${threadDoc.threadId} not found or has no guild`);
                    failed++;
                }
            } catch (error) {
                console.log(`❌ Failed to fetch thread ${threadDoc.threadId}: ${error.message}`);
                failed++;
            }
        }
        
        console.log(`\n📊 Migration complete:`);
        console.log(`   ✅ Updated: ${updated} threads`);
        console.log(`   ❌ Failed: ${failed} threads`);
        
        if (failed > 0) {
            console.log(`\n⚠️  ${failed} threads could not be updated. These are likely deleted threads.`);
            console.log('   You may want to clean them up manually.');
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
}

client.once('ready', () => {
    console.log(`🤖 Logged in as ${client.user.tag}`);
    migrateOpenerGuildIds();
});

client.login(process.env.TOKEN);
