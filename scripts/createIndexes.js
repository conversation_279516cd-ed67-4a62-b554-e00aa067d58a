require('dotenv').config();
const { mongoClient } = require('../mongo/client.js');

/**
 * Helper function to create index with error handling
 */
async function createIndexSafely(collection, indexSpec, options, description) {
    try {
        await collection.createIndex(indexSpec, options);
        console.log(`✅ Created ${description} index`);
    } catch (error) {
        if (error.code === 85) {
            console.log(`ℹ️  ${description} index already exists`);
        } else {
            console.error(`❌ Error creating ${description} index:`, error.message);
        }
    }
}

/**
 * Create database indexes for optimal performance
 * Auto-runs on bot startup
 */
async function createIndexes() {
    try {
        // Ensure MongoDB is connected first
        if (!mongoClient.topology || !mongoClient.topology.isConnected()) {
            console.log('⏳ Waiting for MongoDB connection...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        console.log('🔧 Creating database indexes for performance optimization...');

        // User inventory indexes
        const inventoryCol = mongoClient.db('test').collection('user_inventory');
        
        console.log('📦 Creating user_inventory indexes...');
        
        // Primary index for getUserGlobalInventory
        await createIndexSafely(
            inventoryCol,
            { userId: 1, droppedAt: -1 },
            { name: 'userId_droppedAt_desc', background: true },
            'userId + droppedAt'
        );

        // Guild-specific inventory index
        await createIndexSafely(
            inventoryCol,
            { userId: 1, guildId: 1, droppedAt: -1 },
            { name: 'userId_guildId_droppedAt_desc', background: true },
            'userId + guildId + droppedAt'
        );

        // Item type and name index for rankings
        await createIndexSafely(
            inventoryCol,
            { itemName: 1, itemType: 1, guildId: 1 },
            { name: 'itemName_itemType_guildId', background: true },
            'itemName + itemType + guildId'
        );

        // Member collection indexes
        const memberCol = mongoClient.db('test').collection('member');
        
        console.log('👥 Creating member indexes...');
        
        // Primary member lookup index
        await createIndexSafely(
            memberCol,
            { guildId: 1, userId: 1 },
            { name: 'guildId_userId', background: true },
            'guildId + userId'
        );

        // EXP ranking index
        await createIndexSafely(
            memberCol,
            { 'exp.total': -1 },
            { name: 'exp_total_desc', background: true },
            'exp.total'
        );

        // Guild collection indexes
        const guildsCol = mongoClient.db('test').collection('guilds');
        
        console.log('🏰 Creating guild indexes...');
        
        // Primary guild lookup index
        await createIndexSafely(
            guildsCol,
            { id: 1 },
            { name: 'guild_id', background: true },
            'guild id'
        );

        // Custom items indexes
        const itemsCol = mongoClient.db('test').collection('custom_items');
        
        console.log('🎨 Creating custom_items indexes...');
        
        // Guild items index
        await createIndexSafely(
            itemsCol,
            { guildId: 1, createdAt: -1 },
            { name: 'guildId_createdAt_desc', background: true },
            'guildId + createdAt'
        );

        // Global order index
        await createIndexSafely(
            itemsCol,
            { globalOrder: -1 },
            { name: 'globalOrder_desc', background: true },
            'globalOrder'
        );

        console.log('🎉 All indexes created successfully!');
        console.log('📊 Performance should be significantly improved for /you command');

    } catch (error) {
        console.error('❌ Error creating indexes:', error.message);
        // Don't throw - indexes are nice to have but not critical
    }
}

// Run if called directly
if (require.main === module) {
    createIndexes().then(() => {
        console.log('✅ Index creation complete');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Index creation failed:', error);
        process.exit(1);
    });
}

module.exports = { createIndexes };
